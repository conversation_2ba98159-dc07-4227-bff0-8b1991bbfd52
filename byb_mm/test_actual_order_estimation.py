#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基于实际生成订单数量的预估功能
"""

import sys
import os
import random
import time
from datetime import datetime, timedelta
import pytz

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm, get_trading_config, get_current_symbol

def test_actual_order_estimation():
    """测试基于实际生成订单数量的预估功能"""
    print("=" * 80)
    print("基于实际生成订单数量的预估功能测试".center(80))
    print("=" * 80)
    
    # 创建算法实例
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数组合
    test_cases = [
        {"days": 1, "total_amount": 5000, "each_amount": 500, "name": "小额短期"},
        {"days": 3, "total_amount": 15000, "each_amount": 1000, "name": "中等中期"},
        {"days": 7, "total_amount": 30000, "each_amount": 2000, "name": "大额长期"},
        {"days": 2, "total_amount": 8000, "each_amount": None, "name": "默认配置"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试用例 {i}: {case['name']}")
        print(f"{'='*60}")
        print(f"时间范围: {case['days']} 天")
        print(f"总金额: {case['total_amount']:,.2f}")
        print(f"单次最大: {case['each_amount'] if case['each_amount'] else '默认配置'}")
        
        try:
            # 生成订单计划
            schedule = algorithm.generate_order_schedule(
                case['days'], 
                case['total_amount'], 
                case['each_amount']
            )
            
            print(f"\n结果分析:")
            print(f"  实际生成订单数: {len(schedule)}")
            
            # 分析时间间隔
            if len(schedule) > 1:
                intervals = []
                for j in range(1, min(10, len(schedule))):  # 只分析前10个订单
                    prev_time = schedule[j-1]['scheduled_time']
                    curr_time = schedule[j]['scheduled_time']
                    interval = (curr_time - prev_time).total_seconds()
                    intervals.append(interval)
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    min_interval = min(intervals)
                    max_interval = max(intervals)
                    
                    print(f"  平均间隔: {avg_interval/60:.1f} 分钟")
                    print(f"  最小间隔: {min_interval/60:.1f} 分钟")
                    print(f"  最大间隔: {max_interval/60:.1f} 分钟")
                    print(f"  间隔范围: {min_interval/60:.1f} - {max_interval/60:.1f} 分钟")
                    
                    # 计算间隔的变异系数
                    variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    std_dev = variance ** 0.5
                    cv = (std_dev / avg_interval) * 100
                    print(f"  间隔标准差: {std_dev/60:.1f} 分钟")
                    print(f"  变异系数: {cv:.1f}%")
            
            # 分析订单金额分布
            amounts = [order['amount'] for order in schedule]
            total_generated = sum(amounts)
            avg_amount = total_generated / len(schedule)
            
            print(f"  总生成金额: {total_generated:,.2f}")
            print(f"  平均订单金额: {avg_amount:,.2f}")
            print(f"  最小订单金额: {min(amounts):,.2f}")
            print(f"  最大订单金额: {max(amounts):,.2f}")
            
            # 验证金额匹配度
            amount_diff = abs(total_generated - case['total_amount'])
            amount_diff_ratio = amount_diff / case['total_amount'] * 100
            print(f"  金额匹配度: {amount_diff_ratio:.2f}% 差异")
            
            if amount_diff_ratio < 1:
                print(f"  ✅ 金额匹配良好")
            elif amount_diff_ratio < 5:
                print(f"  ⚠️  金额匹配可接受")
            else:
                print(f"  ❌ 金额匹配偏差较大")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 80)

def test_estimation_accuracy():
    """测试预估准确性"""
    print("\n预估准确性测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 3
    total_amount = 15000
    each_amount = 1000
    
    print(f"测试参数:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    
    # 多次运行，观察预估准确性
    estimation_results = []
    
    for run in range(5):
        print(f"\n运行 {run + 1}:")
        
        try:
            # 先进行预估
            current_price = 0.1  # 模拟价格
            estimated_orders = algorithm.estimate_order_count(total_amount, each_amount, current_price)
            print(f"  预估订单数: {estimated_orders}")
            
            # 实际生成订单计划
            schedule = algorithm.generate_order_schedule(days, total_amount, each_amount)
            actual_orders = len(schedule)
            print(f"  实际订单数: {actual_orders}")
            
            # 计算预估准确性
            accuracy = abs(actual_orders - estimated_orders) / estimated_orders * 100
            estimation_results.append({
                'estimated': estimated_orders,
                'actual': actual_orders,
                'accuracy': accuracy
            })
            
            print(f"  预估准确性: {accuracy:.1f}% 偏差")
            
            if accuracy < 20:
                print(f"  ✅ 预估准确")
            elif accuracy < 50:
                print(f"  ⚠️  预估可接受")
            else:
                print(f"  ❌ 预估偏差较大")
                
        except Exception as e:
            print(f"  ❌ 运行失败: {str(e)}")
    
    # 统计分析
    if estimation_results:
        print(f"\n预估准确性统计:")
        accuracies = [r['accuracy'] for r in estimation_results]
        avg_accuracy = sum(accuracies) / len(accuracies)
        max_accuracy = max(accuracies)
        min_accuracy = min(accuracies)
        
        print(f"  平均偏差: {avg_accuracy:.1f}%")
        print(f"  最大偏差: {max_accuracy:.1f}%")
        print(f"  最小偏差: {min_accuracy:.1f}%")
        
        if avg_accuracy < 30:
            print(f"  ✅ 整体预估准确性良好")
        else:
            print(f"  ⚠️  预估准确性需要改进")
    
    print("\n" + "=" * 80)

def test_interval_optimization():
    """测试间隔优化效果"""
    print("\n间隔优化效果测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 2
    total_amount = 8000
    each_amount = 800
    
    print(f"测试参数:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    
    try:
        # 生成订单计划
        schedule = algorithm.generate_order_schedule(days, total_amount, each_amount)
        
        print(f"\n生成的订单计划:")
        print(f"  总订单数: {len(schedule)}")
        
        # 分析时间分布
        if len(schedule) > 1:
            intervals = []
            for i in range(1, len(schedule)):
                prev_time = schedule[i-1]['scheduled_time']
                curr_time = schedule[i]['scheduled_time']
                interval = (curr_time - prev_time).total_seconds()
                intervals.append(interval)
            
            print(f"\n时间间隔分析:")
            print(f"  总间隔数: {len(intervals)}")
            print(f"  平均间隔: {sum(intervals)/len(intervals)/60:.1f} 分钟")
            print(f"  最小间隔: {min(intervals)/60:.1f} 分钟")
            print(f"  最大间隔: {max(intervals)/60:.1f} 分钟")
            
            # 计算间隔分布
            short_intervals = [i for i in intervals if i < 3600]  # 小于1小时
            medium_intervals = [i for i in intervals if 3600 <= i < 7200]  # 1-2小时
            long_intervals = [i for i in intervals if i >= 7200]  # 大于2小时
            
            print(f"\n间隔分布:")
            print(f"  短间隔 (<1小时): {len(short_intervals)} 个 ({len(short_intervals)/len(intervals)*100:.1f}%)")
            print(f"  中等间隔 (1-2小时): {len(medium_intervals)} 个 ({len(medium_intervals)/len(intervals)*100:.1f}%)")
            print(f"  长间隔 (>2小时): {len(long_intervals)} 个 ({len(long_intervals)/len(intervals)*100:.1f}%)")
            
            # 检查间隔的合理性
            total_time = days * 24 * 3600  # 总时间（秒）
            theoretical_avg_interval = total_time / len(schedule)
            actual_avg_interval = sum(intervals) / len(intervals)
            
            interval_ratio = actual_avg_interval / theoretical_avg_interval
            print(f"\n间隔合理性:")
            print(f"  理论平均间隔: {theoretical_avg_interval/60:.1f} 分钟")
            print(f"  实际平均间隔: {actual_avg_interval/60:.1f} 分钟")
            print(f"  间隔比例: {interval_ratio:.2f}")
            
            if 0.5 <= interval_ratio <= 2.0:
                print(f"  ✅ 间隔分布合理")
            else:
                print(f"  ⚠️  间隔分布需要调整")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    print("开始测试基于实际生成订单数量的预估功能...")
    print(f"当前时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前交易对: {get_current_symbol()}")
    
    # 运行测试
    test_actual_order_estimation()
    test_estimation_accuracy()
    test_interval_optimization()
    
    print("\n测试完成！") 