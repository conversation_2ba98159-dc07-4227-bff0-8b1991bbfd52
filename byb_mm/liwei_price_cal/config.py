# 实时交易系统配置文件

# 交易对列表
SYMBOLS = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
    'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'BCHUSDT', 'XLMUSDT',
    'AVAXUSDT', 'MATICUSDT', 'UNIUSDT', 'ATOMUSDT', 'FTMUSDT'
]

# 模型目录
MODELS_DIR = "fused_lasso_models"

# WebSocket配置
WEBSOCKET_URL = "wss://stream.binance.com:9443/ws/"
RECONNECT_DELAY = 5  # 重连延迟（秒）

# 输出配置
OUTPUT_DIR = "real_time_output"
LOG_FILE = "real_time_trading.log"

# 预测间隔（秒）
PREDICTION_INTERVAL = 60

# 日志级别
LOG_LEVEL = "INFO"

# 时区设置
TIMEZONE = "Asia/Shanghai"

# 文件编码
FILE_ENCODING = "utf-8"

# 数据格式
TIMESTAMP_FORMAT = "%Y-%m-%d %H:%M:%S"
CSV_DELIMITER = ","

# 错误处理
MAX_RECONNECT_ATTEMPTS = 10
ERROR_DELAY = 1  # 错误处理延迟（秒）






