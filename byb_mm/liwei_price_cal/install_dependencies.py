#!/usr/bin/env python3
"""
安装实时交易系统依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ 安装 {package} 失败")
        return False

def main():
    """主安装函数"""
    print("=== 安装实时交易系统依赖 ===")
    
    # 必需的包列表
    required_packages = [
        "websockets",
        "pandas",
        "numpy",
        "pytz",
        "asyncio"
    ]
    
    # 可选的包列表
    optional_packages = [
        "matplotlib",
        "seaborn",
        "openpyxl"
    ]
    
    print("安装必需的包...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n必需包安装完成: {success_count}/{len(required_packages)}")
    
    if success_count < len(required_packages):
        print("警告: 部分必需包安装失败，系统可能无法正常运行")
        return False
    
    print("\n安装可选的包...")
    optional_success = 0
    
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
    
    print(f"可选包安装完成: {optional_success}/{len(optional_packages)}")
    
    # 创建必要的目录
    directories = [
        "fused_lasso_models",
        "real_time_output"
    ]
    
    print("\n创建必要的目录...")
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✓ 创建目录: {directory}")
        else:
            print(f"✓ 目录已存在: {directory}")
    
    print("\n=== 安装完成 ===")
    print("现在可以运行实时交易系统了:")
    print("python real_time_trading_v2.py")
    
    return True

if __name__ == "__main__":
    main() 