import asyncio
import json
import websockets
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
import pickle
import os
import logging
from typing import Dict, List, Optional
import time
from config import *

class BinanceWebSocketClient:
    """
    币安WebSocket客户端
    """
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.ws_url = WEBSOCKET_URL
        self.connections = {}
        self.price_data = {}
        self.is_connected = False
        self.reconnect_attempts = 0
        
        # 初始化价格数据
        for symbol in symbols:
            self.price_data[symbol] = {
                'open': None,
                'high': None,
                'low': None,
                'close': None,
                'volume': None,
                'timestamp': None,
                'minute_return': None,
                'last_update': None
            }
    
    async def connect(self):
        """连接到币安WebSocket"""
        try:
            # 构建订阅消息
            streams = [f"{symbol.lower()}@kline_1m" for symbol in self.symbols]
            stream_url = self.ws_url + "/".join(streams)
            
            logging.info(f"连接到币安WebSocket: {stream_url}")
            
            self.websocket = await websockets.connect(stream_url)
            self.is_connected = True
            self.reconnect_attempts = 0
            logging.info("WebSocket连接成功")
            
        except Exception as e:
            logging.error(f"WebSocket连接失败: {e}")
            self.is_connected = False
            self.reconnect_attempts += 1
    
    async def listen(self):
        """监听WebSocket消息"""
        while True:
            try:
                if not self.is_connected:
                    if self.reconnect_attempts >= MAX_RECONNECT_ATTEMPTS:
                        logging.error(f"重连次数超过限制 ({MAX_RECONNECT_ATTEMPTS})，停止重连")
                        break
                    
                    logging.warning(f"WebSocket连接断开，尝试重连... (第{self.reconnect_attempts + 1}次)")
                    await self.connect()
                    if not self.is_connected:
                        await asyncio.sleep(RECONNECT_DELAY)
                        continue
                
                message = await self.websocket.recv()
                await self.process_message(message)
                
            except websockets.exceptions.ConnectionClosed:
                logging.error("WebSocket连接关闭")
                self.is_connected = False
                await asyncio.sleep(RECONNECT_DELAY)
                
            except Exception as e:
                logging.error(f"处理消息时出错: {e}")
                await asyncio.sleep(ERROR_DELAY)
    
    async def process_message(self, message: str):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            
            if 'data' in data:
                kline_data = data['data']
                symbol = kline_data['s']
                
                # 更新价格数据
                kline = kline_data['k']
                current_time = datetime.now()
                
                # 更新基本信息
                self.price_data[symbol].update({
                    'open': float(kline['o']),
                    'high': float(kline['h']),
                    'low': float(kline['l']),
                    'close': float(kline['c']),
                    'volume': float(kline['v']),
                    'timestamp': kline['t'],
                    'last_update': current_time
                })
                
                if kline['x']:  # 如果K线已完成
                    # 计算分钟收益率（如果有前一个价格）
                    if hasattr(self, 'previous_prices') and symbol in self.previous_prices:
                        prev_close = self.previous_prices[symbol]
                        if prev_close is not None and prev_close > 0:
                            self.price_data[symbol]['minute_return'] = (
                                (float(kline['c']) - prev_close) / prev_close
                            )
                    
                    # 更新前一个价格
                    if not hasattr(self, 'previous_prices'):
                        self.previous_prices = {}
                    self.previous_prices[symbol] = float(kline['c'])
                    
        except Exception as e:
            logging.error(f"处理消息时出错: {e}")
    
    async def close(self):
        """关闭WebSocket连接"""
        if hasattr(self, 'websocket'):
            await self.websocket.close()
        self.is_connected = False
    
    def get_data_quality(self) -> Dict[str, bool]:
        """检查数据质量"""
        quality = {}
        current_time = datetime.now()
        
        for symbol in self.symbols:
            if symbol in self.price_data:
                last_update = self.price_data[symbol].get('last_update')
                if last_update is None:
                    quality[symbol] = False
                else:
                    # 检查数据是否在5分钟内更新过
                    time_diff = (current_time - last_update).total_seconds()
                    quality[symbol] = time_diff < 300  # 5分钟
            else:
                quality[symbol] = False
        
        return quality

class FusedLassoPredictor:
    """
    Fused Lasso模型预测器
    """
    
    def __init__(self, model_file: str):
        self.model_file = model_file
        self.coefficients = {}
        self.load_model()
    
    def load_model(self):
        """加载Fused Lasso模型"""
        try:
            with open(self.model_file, 'rb') as f:
                model_data = pickle.load(f)
            
            self.coefficients = model_data.get('coefficients', {})
            logging.info(f"成功加载模型: {self.model_file}")
            logging.info(f"模型系数: {list(self.coefficients.keys())}")
            
        except Exception as e:
            logging.error(f"加载模型失败: {e}")
            self.coefficients = {}
    
    def predict_return(self, symbol_returns: Dict[str, float]) -> float:
        """
        预测收益率
        
        Parameters:
        - symbol_returns: 符号收益率字典
        
        Returns:
        - predicted_return: 预测收益率
        """
        predicted_return = 0.0
        valid_predictions = 0
        
        for symbol, coeff in self.coefficients.items():
            if symbol in symbol_returns and symbol_returns[symbol] is not None:
                predicted_return += coeff * symbol_returns[symbol]
                valid_predictions += 1
        
        # 记录预测质量
        if valid_predictions > 0:
            logging.debug(f"使用 {valid_predictions}/{len(self.coefficients)} 个系数进行预测")
        
        return predicted_return

class RealTimeTradingSystem:
    """
    实时交易系统
    """
    
    def __init__(self, symbols: List[str], model_file: str, output_file: str):
        self.symbols = symbols
        self.model_file = model_file
        self.output_file = output_file
        self.websocket_client = BinanceWebSocketClient(symbols)
        self.predictor = FusedLassoPredictor(model_file)
        
        # 设置时区
        self.shanghai_tz = pytz.timezone(TIMEZONE)
        
        # 创建输出目录
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        
        # 初始化输出文件
        self.init_output_file()
        
        # 统计信息
        self.stats = {
            'total_predictions': 0,
            'start_time': datetime.now(),
            'last_prediction_time': None
        }
    
    def init_output_file(self):
        """初始化输出文件"""
        header = "timestamp,utc8_time"
        for symbol in self.symbols:
            header += f",{symbol}_return"
        header += ",model_return,data_quality\n"
        
        with open(self.output_file, 'w', encoding=FILE_ENCODING) as f:
            f.write(header)
    
    def write_to_file(self, timestamp: int, symbol_returns: Dict[str, float], 
                     model_return: float, data_quality: Dict[str, bool]):
        """写入文件"""
        try:
            # 转换时间戳为UTC+8时间
            utc_time = datetime.fromtimestamp(timestamp / 1000, tz=pytz.UTC)
            utc8_time = utc_time.astimezone(self.shanghai_tz)
            
            # 计算数据质量分数
            quality_score = sum(data_quality.values()) / len(data_quality) if data_quality else 0
            
            # 构建CSV行
            line = f"{timestamp},{utc8_time.strftime(TIMESTAMP_FORMAT)}"
            for symbol in self.symbols:
                return_value = symbol_returns.get(symbol, '')
                line += f",{return_value}"
            line += f",{model_return},{quality_score:.3f}\n"
            
            # 写入文件
            with open(self.output_file, 'a', encoding=FILE_ENCODING) as f:
                f.write(line)
                
        except Exception as e:
            logging.error(f"写入文件失败: {e}")
    
    def print_to_screen(self, utc8_time: str, model_return: float, data_quality: Dict[str, bool]):
        """打印到屏幕"""
        quality_score = sum(data_quality.values()) / len(data_quality) if data_quality else 0
        print(f"[{utc8_time}] 预测收益率: {model_return:.6f} | 数据质量: {quality_score:.1%}")
    
    def print_stats(self):
        """打印统计信息"""
        if self.stats['total_predictions'] > 0:
            runtime = datetime.now() - self.stats['start_time']
            avg_interval = runtime.total_seconds() / self.stats['total_predictions']
            
            print(f"\n=== 统计信息 ===")
            print(f"运行时间: {runtime}")
            print(f"总预测次数: {self.stats['total_predictions']}")
            print(f"平均预测间隔: {avg_interval:.1f}秒")
            if self.stats['last_prediction_time']:
                print(f"最后预测时间: {self.stats['last_prediction_time']}")
    
    async def run(self):
        """运行实时交易系统"""
        logging.info("启动实时交易系统...")
        print(f"监控的交易对: {', '.join(self.symbols)}")
        print(f"使用模型: {self.model_file}")
        print(f"输出文件: {self.output_file}")
        print(f"预测间隔: {PREDICTION_INTERVAL}秒")
        print("按 Ctrl+C 停止程序\n")
        
        # 启动WebSocket监听
        websocket_task = asyncio.create_task(self.websocket_client.listen())
        
        try:
            while True:
                # 等待预测间隔
                await asyncio.sleep(PREDICTION_INTERVAL)
                
                # 检查数据质量
                data_quality = self.websocket_client.get_data_quality()
                quality_score = sum(data_quality.values()) / len(data_quality) if data_quality else 0
                
                if quality_score < 0.5:
                    logging.warning(f"数据质量较低: {quality_score:.1%}")
                
                # 收集所有符号的收益率
                symbol_returns = {}
                for symbol in self.symbols:
                    if symbol in self.websocket_client.price_data:
                        minute_return = self.websocket_client.price_data[symbol].get('minute_return')
                        symbol_returns[symbol] = minute_return
                    else:
                        symbol_returns[symbol] = None
                
                # 预测收益率
                model_return = self.predictor.predict_return(symbol_returns)
                
                # 获取当前时间戳
                current_timestamp = int(time.time() * 1000)
                
                # 转换时间戳为UTC+8时间
                utc_time = datetime.fromtimestamp(current_timestamp / 1000, tz=pytz.UTC)
                utc8_time = utc_time.astimezone(self.shanghai_tz)
                utc8_str = utc8_time.strftime(TIMESTAMP_FORMAT)
                
                # 更新统计信息
                self.stats['total_predictions'] += 1
                self.stats['last_prediction_time'] = utc8_str
                
                # 写入文件
                self.write_to_file(current_timestamp, symbol_returns, model_return, data_quality)
                
                # 打印到屏幕
                self.print_to_screen(utc8_str, model_return, data_quality)
                
                # 记录日志
                logging.info(f"时间: {utc8_str}, 预测收益率: {model_return:.6f}, 数据质量: {quality_score:.1%}")
                
        except KeyboardInterrupt:
            logging.info("收到中断信号，正在关闭...")
            self.print_stats()
        except Exception as e:
            logging.error(f"运行时出错: {e}")
        finally:
            # 关闭WebSocket连接
            await self.websocket_client.close()
            websocket_task.cancel()

def find_latest_model(models_dir: str = MODELS_DIR) -> str:
    """
    找到最新的模型文件
    
    Parameters:
    - models_dir: 模型目录
    
    Returns:
    - latest_model: 最新模型文件路径
    """
    try:
        model_files = [f for f in os.listdir(models_dir) if f.startswith('model_') and f.endswith('.pkl')]
        
        if not model_files:
            raise FileNotFoundError(f"在 {models_dir} 中没有找到模型文件")
        
        # 按文件名排序（假设文件名包含日期）
        model_files.sort(reverse=True)
        latest_model = os.path.join(models_dir, model_files[0])
        
        logging.info(f"找到最新模型: {latest_model}")
        return latest_model
        
    except Exception as e:
        logging.error(f"查找最新模型失败: {e}")
        raise

async def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(OUTPUT_DIR, LOG_FILE)),
            logging.StreamHandler()
        ]
    )
    
    # 找到最新模型
    try:
        latest_model = find_latest_model()
    except FileNotFoundError:
        logging.error("未找到模型文件，请确保模型文件存在")
        return
    
    # 输出文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(OUTPUT_DIR, f"real_time_predictions_{timestamp}.csv")
    
    # 创建实时交易系统
    trading_system = RealTimeTradingSystem(SYMBOLS, latest_model, output_file)
    
    # 运行系统
    await trading_system.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序运行出错: {e}") 