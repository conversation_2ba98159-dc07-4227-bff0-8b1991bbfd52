#!/usr/bin/env python3
"""
测试wash_trading.py的新功能
"""

import sys
sys.path.append("/home/<USER>/usd_mm/")
import json
import time
from wash_trading import get_current_mm_prices, save_current_mm_prices, check_recent_trades, calculate_wash_qty_from_orderbook

def test_price_sharing():
    """测试价格共享功能"""
    print("=== 测试价格共享功能 ===")
    
    # 测试保存价格
    test_bid = 1.2345
    test_ask = 1.2350
    save_current_mm_prices(test_bid, test_ask)
    print(f"保存测试价格: bid={test_bid}, ask={test_ask}")
    
    # 测试读取价格
    bid, ask = get_current_mm_prices()
    print(f"读取价格: bid={bid}, ask={ask}")
    
    if bid == test_bid and ask == test_ask:
        print("✅ 价格共享功能正常")
    else:
        print("❌ 价格共享功能异常")

def test_trade_detection():
    """测试交易检测功能"""
    print("\n=== 测试交易检测功能 ===")
    
    try:
        need_wash = check_recent_trades("manausdt", 2)
        print(f"交易检测结果: {'需要刷量' if need_wash else '不需要刷量'}")
        print("✅ 交易检测功能正常")
    except Exception as e:
        print(f"❌ 交易检测功能异常: {e}")

def test_qty_calculation():
    """测试数量计算功能"""
    print("\n=== 测试数量计算功能 ===")
    
    try:
        qty = calculate_wash_qty_from_orderbook("manausdt")
        print(f"计算的刷量数量: {qty:.2f}")
        print("✅ 数量计算功能正常")
    except Exception as e:
        print(f"❌ 数量计算功能异常: {e}")

if __name__ == "__main__":
    print("开始测试wash_trading.py的新功能...")
    
    test_price_sharing()
    test_trade_detection()
    test_qty_calculation()
    
    print("\n测试完成！")
