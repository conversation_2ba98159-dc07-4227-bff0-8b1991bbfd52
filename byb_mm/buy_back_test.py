import sys
import os
import time
import json
import logging
import random
import asyncio
import traceback
import inspect
from math import tanh
from datetime import datetime
from collections import defaultdict

import numpy as np
import pandas as pd
import pytz
import backoff
import aiohttp

# 项目内模块导入
sys.path.append(r'/home/<USER>/byb_mm/')
sys.path.append(r'/home/<USER>/byb_mm/production/')
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import con_pri
import config

# ========== 全局配置 ==========
SYMBOL = 'bybusdt'

# ========== 初始化API客户端 ==========
spot_market = Spot()
spot_client = SpotTrade(con_pri.api_key_tsen, con_pri.api_secret_tsen)


def get_pending_orders(symbol: str = SYMBOL, max_retries: int = 3):
    """获取当前未成交的挂单信息，带重试机制

    参数:
        symbol: 交易对符号
        max_retries: 最大重试次数

    返回:
        一个列表，每个元素是一个字典，包含挂单信息
    """
    retry_count = 0
    backoff_time = 0.5  # 初始等待时间（秒）

    while retry_count <= max_retries:
        try:
            result = spot_client.get_open_orders(symbol, pageSize=1000)
            if result is None or 'resultList' not in result:
                if retry_count < max_retries:
                    retry_count += 1
                    time.sleep(backoff_time)
                    backoff_time *= 2
                    print(f"获取未成交订单返回无效结果，第 {retry_count} 次重试")
                    continue
                else:
                    print("获取未成交订单返回无效结果，重试失败")
                    return []
            orders = result['resultList']
            if orders is None:
                if retry_count < max_retries:
                    retry_count += 1
                    time.sleep(backoff_time)
                    backoff_time *= 2
                    print(f"获取未成交订单返回None，第 {retry_count} 次重试")
                    continue
                else:
                    print("获取未成交订单返回None，重试失败")
                    return []
            valid_orders = []
            for order in orders:
                if not isinstance(order, dict):
                    print(f"订单对象不是字典: {type(order)}")
                    continue
                order['order_id'] = order['id']
                if order['status'] in [0, 1, 3]:  # 只保留初始、新订单和部分成交的订单
                    valid_orders.append(order)
            print(f"成功获取 {len(valid_orders)} 个有效订单")
            return valid_orders
        except Exception as e:
            error_str = str(e)
            if "API access too frequent" in error_str and retry_count < max_retries:
                retry_count += 1
                time.sleep(backoff_time)
                backoff_time *= 2
                print(f"API访问过于频繁，等待{backoff_time:.1f}秒后重试（第{retry_count}次）")
                continue
            else:
                print(f"获取未成交订单失败: {error_str}")
                return []

def group_orders_by_price(df_orders, price_col='price', volume_col='volume'):
    """按价格聚合挂单数量"""
    df_orders.loc[:, volume_col] = df_orders[volume_col].astype(float)
    grouped = df_orders.groupby(price_col)[volume_col].sum().reset_index()
    grouped[price_col] = grouped[price_col].astype(float)
    grouped[volume_col] = grouped[volume_col].astype(float)
    return grouped

def split_mm_and_user_orders(orderbook_df, my_grouped, price_col, qty_col):
    """区分做市商和用户订单，返回两个DataFrame"""
    merged = pd.merge(
        orderbook_df,
        my_grouped,
        left_on=price_col,
        right_on='price',
        how='left'
    )
    merged['volume'] = merged['volume'].fillna(0)
    merged['user_qty'] = merged[qty_col] - merged['volume']
    # 用户订单
    df_user = merged[merged['user_qty'] > 0][[price_col, 'user_qty']]
    df_user = df_user.rename(columns={price_col: 'price', 'user_qty': 'qty'})
    # 做市商订单
    df_mm = merged[merged['volume'] > 0][[price_col, 'volume']]
    df_mm = df_mm.rename(columns={price_col: 'price', 'volume': 'qty'})
    return df_mm.reset_index(drop=True), df_user.reset_index(drop=True)

# ========== 主流程 ==========
def main():
    # 获取当前未成交挂单
    df_open_orders = pd.DataFrame(get_pending_orders(SYMBOL))
    df_asks_orders = df_open_orders[df_open_orders['side'] == 'SELL'].sort_values(by='price').reset_index(drop=True)
    df_bids_orders = df_open_orders[df_open_orders['side'] == 'BUY'].sort_values(by='price', ascending=False).reset_index(drop=True)

    # 获取orderbook
    df_asks, df_bids = spot_market.get_orderbook(SYMBOL)

    # 卖单区分
    my_asks_grouped = group_orders_by_price(df_asks_orders, 'price', 'volume')
    df_mm_asks, df_user_asks = split_mm_and_user_orders(df_asks, my_asks_grouped, 'asks_price', 'asks_qty')

    # 买单区分
    my_bids_grouped = group_orders_by_price(df_bids_orders, 'price', 'volume')
    df_mm_bids, df_user_bids = split_mm_and_user_orders(df_bids, my_bids_grouped, 'bids_price', 'bids_qty')

    # 可选：打印或返回结果
    print('做市商卖单:')
    print(df_mm_asks)
    print('用户卖单:')
    print(df_user_asks)
    print('做市商买单:')
    print(df_mm_bids)
    print('用户买单:')
    print(df_user_bids)

if __name__ == '__main__':
    main()
