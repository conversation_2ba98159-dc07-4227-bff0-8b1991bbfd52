#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 buy_order_excess_id 问题的脚本
检查推价函数是否正确返回订单ID
"""

import sys
import os
import logging
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from byb_mm.client_push_price import (
    execute_push_trading,
    place_limit_buy_order,
    place_limit_sell_order,
    get_orders,
    match_orders_with_orderbook,
    get_current_market_price
)
from spot.trade import SpotTrade

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_buy_order_excess_id.log'),
        logging.StreamHandler()
    ]
)

# 初始化交易客户端
try:
    from byb_mm.config import API_KEY, SECRET_KEY, BASE_URL
    spot_client = SpotTrade(API_KEY, SECRET_KEY, BASE_URL)
    logging.info("交易客户端初始化成功")
except Exception as e:
    logging.error(f"交易客户端初始化失败: {e}")
    sys.exit(1)


def test_place_limit_orders():
    """测试限价订单函数"""
    logging.info("=== 测试限价订单函数 ===")
    
    # 获取当前市场价格
    current_mid_price, current_bid, current_ask = get_current_market_price()
    if current_mid_price is None:
        logging.error("无法获取当前市场价格")
        return False
    
    logging.info(f"当前市场价格: {current_mid_price:.4f}")
    
    # 测试买单 - 价格设置得比较低，避免立即成交
    test_buy_price = current_mid_price * 0.95  # 比市价低5%
    test_buy_qty = 10
    
    logging.info(f"测试限价买单: 价格={test_buy_price:.4f}, 数量={test_buy_qty}")
    buy_order = place_limit_buy_order(spot_client, test_buy_price, test_buy_qty, 'bybusdt')
    
    if buy_order:
        logging.info(f"✅ 限价买单测试成功: {buy_order}")
        return buy_order.get('order_id')
    else:
        logging.error("❌ 限价买单测试失败")
        return None


def test_get_orders():
    """测试获取订单函数"""
    logging.info("=== 测试获取订单函数 ===")
    
    orders_df = get_orders(spot_client, 'bybusdt')
    if orders_df is not None and not orders_df.empty:
        logging.info(f"✅ 获取到 {len(orders_df)} 个订单")
        logging.info(f"订单列表:\n{orders_df[['id', 'side', 'price', 'volume', 'status']].head()}")
        return True
    else:
        logging.warning("⚠️ 没有获取到订单或订单为空")
        return False


def test_match_orders_with_orderbook():
    """测试订单与订单簿匹配函数"""
    logging.info("=== 测试订单与订单簿匹配函数 ===")
    
    # 获取订单
    orders_df = get_orders(spot_client, 'bybusdt')
    if orders_df is None or orders_df.empty:
        logging.warning("没有订单可供匹配测试")
        return False
    
    # 匹配订单簿
    matched_orders = match_orders_with_orderbook(orders_df, 'bybusdt')
    if matched_orders is not None and not matched_orders.empty:
        logging.info(f"✅ 匹配成功，找到 {len(matched_orders)} 个匹配订单")
        logging.info(f"匹配结果:\n{matched_orders[['side', 'price', 'my_qty', 'others_qty']].head()}")
        return True
    else:
        logging.warning("⚠️ 订单匹配失败或结果为空")
        return False


def test_execute_push_trading():
    """测试推价交易函数"""
    logging.info("=== 测试推价交易函数 ===")
    
    # 获取当前市场价格
    current_mid_price, current_bid, current_ask = get_current_market_price()
    if current_mid_price is None:
        logging.error("无法获取当前市场价格")
        return None
    
    # 测试参数
    symbol = 'bybusdt'
    strategy = 'push_up'  # 测试推高策略
    target_ret = 0.001    # 0.1% 目标回报率
    user_order_limit = 50  # 较小的限额用于测试
    
    logging.info(f"测试推价交易:")
    logging.info(f"  策略: {strategy}")
    logging.info(f"  目标回报率: {target_ret:.4f}")
    logging.info(f"  当前价格: {current_mid_price:.4f}")
    logging.info(f"  订单限额: ${user_order_limit}")
    
    # 执行推价交易
    buy_order_excess_id = execute_push_trading(
        symbol=symbol,
        strategy=strategy,
        target_ret=target_ret,
        current_price=current_mid_price,
        user_order_limit=user_order_limit
    )
    
    if buy_order_excess_id:
        logging.info(f"✅ 推价交易成功，返回订单ID: {buy_order_excess_id}")
        return buy_order_excess_id
    else:
        logging.warning("⚠️ 推价交易返回 None")
        return None


def main():
    """主测试函数"""
    logging.info("开始测试 buy_order_excess_id 问题")
    logging.info("=" * 50)
    
    # 测试1: 限价订单函数
    test_order_id = test_place_limit_orders()
    time.sleep(2)
    
    # 测试2: 获取订单函数
    test_get_orders()
    time.sleep(2)
    
    # 测试3: 订单匹配函数
    test_match_orders_with_orderbook()
    time.sleep(2)
    
    # 测试4: 推价交易函数
    push_order_id = test_execute_push_trading()
    
    # 总结测试结果
    logging.info("=" * 50)
    logging.info("测试总结:")
    logging.info(f"  限价订单测试: {'✅ 成功' if test_order_id else '❌ 失败'}")
    logging.info(f"  推价交易测试: {'✅ 成功' if push_order_id else '❌ 失败'}")
    
    if push_order_id:
        logging.info(f"  推价订单ID: {push_order_id}")
        logging.info("✅ buy_order_excess_id 问题已修复")
    else:
        logging.error("❌ buy_order_excess_id 仍然为 None，需要进一步调试")
    
    # 清理测试订单（如果有的话）
    if test_order_id:
        try:
            from byb_mm.client_push_price import cancel_order
            cancel_order(spot_client, test_order_id, 'bybusdt')
            logging.info(f"清理测试订单: {test_order_id}")
        except Exception as e:
            logging.warning(f"清理测试订单失败: {e}")


if __name__ == '__main__':
    main()
