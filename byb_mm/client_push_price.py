import sys
sys.path.append("/home/<USER>/byb_mm/")
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import random
import time
import pandas as pd
import numpy as np
import config
import con_pri
import logging
import requests
import pytz
from datetime import datetime
import asyncio
import json
import os
import pymysql
import math

beijing_tz = pytz.timezone('Asia/Shanghai')

# df_asks, df_bids = spot_market.get_orderbook(symbol)

# 记录上次检查交易的时间
last_trade_check_time = time.time()

# 共享价格文件路径
SHARED_PRICES_FILE = 'current_mm_prices.json'
# SHARED_TARGET_PRICE_FILE = 'current_target_prices.json'

host = "127.0.0.1"  # 服务器地址
port = 3306  # MySQL 端口
user = "tsen"  # 数据库用户名
password = "12345678"  # 数据库密码
database = "pricing_model"  # 要连接的交易数据库 - 实盘 以及，记录用户交易需要铺出去的单子
database_trade = "market_making_db"  # 铺盘数据 orders_6767088_bybusdt
table_eaten_order = 'eating_order'

##################################### - simulation & actual translate - #####################################################
spot_market = Spot()
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)  ## T
# spot_client = SpotTrade(con_pri.api_key_hal, con_pri.api_secret_hal) ## H

symbol = 'bybusdt'
# base_url = "https://openapi.100ex.com"## 正式服

# symbol = 'manausdt'
base_url = "https://openapi.100exdemo.com" ## 模拟服
spot_market.BASE_URL = base_url
spot_client.BASE_URL = base_url

max_usdt = 10000
# max_usdt = 100
##########################################################################################


def get_target_return():
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conns.cursor() as cursors:
            # Get last 10 records ordered by id (or timestamp) in descending order
            cursors.execute("SELECT * FROM platform_coin ORDER BY id DESC LIMIT 10")
            results = cursors.fetchall()
            
            colname = ["id", "target_return", "timestamp"]
            dataframes = pd.DataFrame(results, columns=colname)
            # Reverse to get chronological order if needed
            dataframes = dataframes.iloc[::-1].reset_index(drop=True)
            
            logging.debug(f"获取最后10条目标回报数据，共 {len(dataframes)} 条记录")
            return dataframes
            
    except Exception as e:
        logging.error(f"获取目标回报失败: {e}")
        return pd.DataFrame()
    finally:
        conns.close()



def store_user_trade(dataframe, table_name=table_eaten_order):
    # 数据库连接参数（这些应该从配置文件或环境变量读取）
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)  
    try:
        with conns.cursor() as cursor:
            # 准备插入数据的SQL语句
            insert_sql = f"""
            INSERT INTO {table_name} 
            (price, eat_qty, symbol, order_id, timestamp, trade_direction, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                price = VALUES(price),
                eat_qty = VALUES(eat_qty),
                symbol = VALUES(symbol),
                order_id = VALUES(order_id),
                timestamp = VALUES(timestamp),
                trade_direction = VALUES(trade_direction),
                status = VALUES(status)
            """
            # 将DataFrame转换为元组列表
            records = []
            for _, row in dataframe.iterrows():
                record = (
                    float(row['price']),
                    float(row['eat_qty']),
                    str(row['symbol']),
                    str(row['order_id']),
                    row['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row['timestamp'], datetime) else str(row['timestamp']),
                    str(row['trade_direction']),
                    int(0)
                )
                records.append(record)
            
            # 批量插入数据
            if records:
                cursor.executemany(insert_sql, records)
                conns.commit()
                
                inserted_count = cursor.rowcount
                logging.info(f"成功插入/更新 {inserted_count} 条订单记录到数据库")
                
                # # 可选：查询并验证插入的数据
                # cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE order_id IN ({','.join(['%s'] * len(dataframe))})", 
                #              dataframe['order_id'].tolist())
                # count = cursor.fetchone()[0]
                # logging.info(f"数据库中现有 {count} 条相关订单记录")

                return True
            else:
                logging.warning("没有要插入的记录")
                return True
                
    except pymysql.Error as e:
        logging.error(f"数据库操作失败: {e}")
        conns.rollback()
        return False
        
    except Exception as e:
        logging.error(f"存储订单记录时发生错误: {e}")
        conns.rollback()
        return False
        
    finally:
        conns.close()
        logging.info("数据库连接已关闭")



def send_telegram_message(text):
    bot_token = con_pri.bot_token
    chat_id = con_pri.chat_id

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {message}")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logging.basicConfig(
    # filename='client_push_trading_simu.log',  # Log to this file simulation
    filename='client_push_trading.log',  # Log to this file

    # level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    level=logging.INFO,  # set info, just to ignore too many rebundant information
    format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))

def get_current_mm_prices(symbol):
    """
    获取当前做市商生成的ask和bid价格
    Returns:
        tuple: (bid_price, ask_price) 或 (None, None) 如果获取失败
    """
    try:
        # # 尝试从共享文件读取价格
        # if os.path.exists(SHARED_PRICES_FILE):
        #     with open(SHARED_PRICES_FILE, 'r') as f:
        #         price_data = json.load(f)
        #         bid_price = price_data.get('bid_price')
        #         ask_price = price_data.get('ask_price')
        #         timestamp = price_data.get('timestamp', 0)

        #         # 检查价格数据是否过期（超过10秒）
        #         if time.time() - timestamp < 10:
        #             logging.debug(f"从共享文件获取做市商价格: bid={bid_price}, ask={ask_price}")
        #             return bid_price, ask_price
        #         else:
        #             logging.warning("共享价格文件数据过期")

        # 如果文件不存在或数据过期，尝试从订单簿获取当前最优价格
        df_asks, df_bids = spot_market.get_orderbook(symbol)
        
        if not df_asks.empty and not df_bids.empty:
            current_bid = float(df_bids['bids_price'].iloc[0])
            current_ask = float(df_asks['asks_price'].iloc[0])
            logging.info(f"从订单簿获取当前价格: bid={current_bid}, ask={current_ask}")
            return current_bid, current_ask

    except Exception as e:
        logging.error(f"获取做市商价格失败: {e}")

    return None, None


def get_current_self_orderbook():
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database_trade)
    try:
        with conns.cursor() as cursors:
            # Get last 10 records ordered by id (or timestamp) in descending order
            cursors.execute("SELECT * FROM orders_6767088_bybusdt")
            results = cursors.fetchall()
            
            colname = ["id", "side", "clientOrderID", "order_id", "price", "volume", "status", "created_at", "updated_at"]
            dataframes = pd.DataFrame(results, columns=colname)
            # Reverse to get chronological order if needed
            dataframes = dataframes.iloc[::-1].reset_index(drop=True)
            logging.debug(f"获取铺盘数据，共 {len(dataframes)} 条记录")
            return dataframes
            
    except Exception as e:
        logging.error(f"获取目标回报失败: {e}")
        return pd.DataFrame()
    finally:
        conns.close()


def send_telegram_message(text):
    bot_token = con_pri.bot_token
    chat_id = con_pri.chat_id

    message_parts = []
    message_parts.extend([
        f"{text}",
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {message}")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


### use orderbook to get the price
def get_current_market_price(symbol="bybusdt"):
    """
    获取当前市场中间价
    Returns:
        float: 当前市场中间价
    """
    mid_price = 0
    current_bid = 0
    current_ask = 0
    try:
        df_asks, df_bids = spot_market.get_orderbook(symbol)
        if not df_asks.empty and not df_bids.empty:
            current_bid = float(df_bids['bids_price'].iloc[0])
            current_ask = float(df_asks['asks_price'].iloc[0])
            mid_price = (current_bid + current_ask) / 2
            logging.debug(f"当前市场价: bid={current_bid}, ask={current_ask}, mid={mid_price}")
        return mid_price, current_bid, current_ask

    except Exception as e:
        logging.error(f"获取当前市场价格失败: {e}")
        return mid_price, current_bid, current_ask
    

def calculate_price_deviation(current_price, target_ret):
    """
    计算价格偏离度
    Args:
        current_price: 当前市场价格
        target_ret: 目标回报率 (正数表示应该推高，负数表示应该推低)
    Returns:
        float: 偏离百分比
        str: 偏离方向 ('above' 或 'below')
    """
    if current_price is None or target_ret is None:
        return None, None
    
    # target_ret 直接表示需要的价格变动方向和幅度
    deviation = target_ret * 100  # 转换为百分比
    direction = 'above' if target_ret > 0 else 'below'
    
    return deviation, direction


def determine_trading_strategy(deviation, direction, tick=0.0001, current_mid_price=None):
    """
    确定刷价策略
    Args:
        deviation: 价格偏离百分比
        direction: 偏离方向
        threshold: 价格偏离阈值百分比
    Returns:
        str: 'wash' (继续刷量), 'push_up' (推高价格), 'push_down' (推低价格)
    """
    ticks_move = abs(deviation)/100 * current_mid_price

    if deviation is None:
        return 'wash'
    if ticks_move <= tick: ## 价格偏离小于1tick
        return 'wash'  # 偏离很小，继续正常刷量
    elif direction == 'above':
        return 'push_up'  # 目标回报率为正，需要推高价格
    else:
        return 'push_down'  # 目标回报率为负，需要推低价格



def calculate_push_quantity(symbol, strategy, orderbook_depth=5):
    """
    计算推价数量，基于前5-10档的订单簿
    Args:
        symbol: 交易对
        strategy: 'push_up' 或 'push_down'
        orderbook_depth: 订单簿深度
    Returns:
        int: 推价数量
    """
    try:
        df_asks, df_bids = spot_market.get_orderbook(symbol)
        
        if strategy == 'push_up':
            # 推高价格，需要吃卖单
            target_qty = df_asks['asks_qty'].head(orderbook_depth).astype(float).sum()
        else:
            # 推低价格，需要吃买单
            target_qty = df_bids['bids_qty'].head(orderbook_depth).astype(float).sum()
        
        # 随机选择前5-10档总量的20%-60%
        ratio = random.uniform(0.2, 0.6)
        push_qty = target_qty * ratio
        
        # 设置合理上下限
        min_qty = 100.0
        max_qty = 3000.0 + random.uniform(-500, 300)
        
        push_qty = max(min_qty, min(push_qty, max_qty))
        push_qty = int(push_qty)
        
        logging.info(f"推价策略: {strategy}, 前{orderbook_depth}档总量: {target_qty:.2f}, "
                    f"推价比例: {ratio:.1%}, 推价数量: {push_qty}")
        
        return push_qty
        
    except Exception as e:
        logging.error(f"计算推价数量失败: {e}")
        return int(random.uniform(100, 500))



def save_current_mm_prices(bid_price, ask_price):
    """
    保存当前做市商价格到共享文件
    Args:
        bid_price: 买一价格
        ask_price: 卖一价格
    """
    try:
        price_data = {
            'bid_price': bid_price,
            'ask_price': ask_price,
            'timestamp': time.time()
        }
        with open(SHARED_PRICES_FILE, 'w') as f:
            json.dump(price_data, f)
        logging.debug(f"保存做市商价格: bid={bid_price}, ask={ask_price}")
    except Exception as e:
        logging.error(f"保存做市商价格失败: {e}")


def check_recent_trades(symbol="bybusdt", no_trade_threshold=60):
    """
    检查最近是否有交易活动
    Args:
        symbol: 交易对
        no_trade_threshold: 无交易时间阈值（秒）
    Returns:
        bool: True表示最近没有交易（需要刷量），False表示有交易
    """
    try:
        df_trades = spot_market.get_trades(symbol=symbol)
        if df_trades.empty:
            logging.info("无法获取成交记录，默认需要刷量")
            return True

        # 获取最新交易时间（ctime字段是毫秒时间戳）
        latest_trade_time = df_trades['ctime'].max() / 1000  # 转换为秒
        current_time = time.time()
        time_since_last_trade = current_time - latest_trade_time

        # 友好显示时间
        if time_since_last_trade >= 60:
            minutes = time_since_last_trade / 60
            logging.info(f"距离最后一笔交易已过去 {minutes:.1f} 分钟 ({time_since_last_trade:.1f} 秒)")
        else:
            logging.info(f"距离最后一笔交易已过去 {time_since_last_trade:.1f} 秒")

        # 如果距离最后交易超过阈值，则需要刷量
        return time_since_last_trade > no_trade_threshold

    except Exception as e:
        logging.error(f"检查交易记录失败: {e}")
        return True  # 出错时默认需要刷量


def calculate_wash_qty_from_orderbook(symbol="bybusdt", order_side='buy', df_asks=None, df_bids=None):
    """
    根据订单簿1-3档总量计算刷量数量
    Returns:
        float: 刷量数量（1-3档总量的10%-80%，但有合理上限）
    """
    try:
        # df_asks, df_bids = spot_market.get_orderbook(symbol)

        if df_asks.empty or df_bids.empty:
            logging.warning("订单簿为空，使用默认刷量数量")
            return int(random.uniform(50, 200))

        # 计算1-3档总量
        top_3_ask_qty = df_asks['asks_qty'].head(3).astype(float).sum()
        top_3_bid_qty = df_bids['bids_qty'].head(3).astype(float).sum()
        if order_side == "buy":
            total_top_3_qty = top_3_ask_qty
        else:
            total_top_3_qty = top_3_bid_qty

        # 随机选择10%-80%的比例
        ratio = random.uniform(0.1, 0.8)
        wash_qty = total_top_3_qty * ratio

        # 设置合理的上下限，避免数量过大或过小
        min_qty = 50.0   # 最小刷量数量
        max_qty = 2000.0 + random.uniform(-300,200) # 最大刷量数量，避免超过交易所限制

        wash_qty = max(min_qty, min(wash_qty, max_qty))

        # 由于API需要整数，所以转换为整数
        wash_qty = int(wash_qty)

        logging.info(f"1-3档总量: {total_top_3_qty:.2f}, 刷量比例: {ratio:.1%}, 刷量数量: {wash_qty}")

        return wash_qty

    except Exception as e:
        logging.error(f"计算刷量数量失败: {e}")
        # 出错时返回随机数量
        return int(random.uniform(50, 200))


def generate_random_side():
    """
    随机生成交易方向（完全随机）
    Returns:
        str: "buy" 或 "sell"
    """
    return random.choices(["buy", "sell"], [0.5, 0.5])


def generate_random_sleep(random_lower: float=20, random_upper: float=30):
    """
    在范围内随机生成睡眠时间，控制检查频率
    Args:
        random_lower: 最小睡眠时间（秒）
        random_upper: 最大睡眠时间（秒）
    Returns:
        float: 睡眠时间
    """
    return round(random.uniform(random_lower, random_upper), 1)


def get_orders(spot_client, symbol='bybusdt'):
    """
    获取当前挂单
    
    Args:
        spot_client: 交易客户端
        symbol: 交易对
    
    Returns:
        DataFrame: 挂单列表的DataFrame
    """
    orders = []
    try:
        result = spot_client.get_orders(symbol=symbol, pageSize = 100)
        if result and 'orderList' in result:  # Changed from 'resultList' to 'orderList'
            print(f"当前挂单数量: {result['count']}")
            
            for order in result['orderList']:  # Changed from 'resultList' to 'orderList'
                orders.append({
                    'id': order['id'],
                    'side': order['side'],
                    'price': float(order['price']),
                    'volume': float(order['volume']),
                    'remain_volume': float(order['remain_volume']),
                    'status': order['status'],
                    'status_msg': order['status_msg'],
                    'created_at': datetime.fromtimestamp(order['created_at'] / 1000),
                    'source': order['source_msg']
                })
            
            df_orders = pd.DataFrame(orders)
            print(df_orders.head())
            return df_orders
        else:
            print("No orders found or invalid response structure")
            return pd.DataFrame()  # Return empty DataFrame instead of empty list
            
    except Exception as e:
        logging.error(f"获取挂单失败: {e}")
        return pd.DataFrame()  # Return empty DataFrame instead of empty list




def match_orders_with_orderbook(my_orders_df, symbol):
    # Check if my_orders_df is empty or not a DataFrame
    if not isinstance(my_orders_df, pd.DataFrame) or my_orders_df.empty:
        logging.warning("No orders to match or invalid input")

        return pd.DataFrame()
    
    # Get orderbook - handle both DataFrame and tuple returns
    orderbook_data = spot_market.get_orderbook(symbol)

    # Check if it's a tuple (asks, bids) or a DataFrame
    if isinstance(orderbook_data, tuple):
        asks_df, bids_df = orderbook_data
    else:
        # If it's a DataFrame with both asks and bids columns
        asks_df = orderbook_data[['asks_price', 'asks_qty']].dropna()
        bids_df = orderbook_data[['bids_price', 'bids_qty']].dropna()

    # 一档价差检测
    current_ask_price = float(asks_df['asks_price'].iloc[0])
    current_bid_price = float(bids_df['bids_price'].iloc[0])
    current_spread_ratio = current_ask_price / current_bid_price - 1
    if current_spread_ratio > 0.005:
        logging.info(f"一档价差超过千五，跳过拉价 (current_spread_ratio: {current_spread_ratio}, "
                     f"current_ask_price: {current_ask_price}), current_bid_price: {current_bid_price}")
        return pd.DataFrame()
    
    # Initialize result lists
    result_data = []
    
    # Process asks (sell side)
    if isinstance(asks_df, pd.DataFrame):
        for _, row in asks_df.iterrows():
            price = row.get('asks_price', row.get('price', row.iloc[0]))
            total_qty = row.get('asks_qty', row.get('qty', row.get('quantity', row.iloc[1])))
            
            # Find my orders at this exact price (exact match)
            my_orders_at_price = my_orders_df[
                (my_orders_df['side'] == 'SELL') & 
                (my_orders_df['price'] == float(price))
            ]
            
            # Sum only the orders at THIS specific price, not cumulative
            my_qty = my_orders_at_price['remain_volume'].sum() if not my_orders_at_price.empty else 0
            others_qty = max(0, float(total_qty) - my_qty)  # Ensure non-negative
            
            result_data.append({
                'side': 'ASK',
                'price': float(price),
                'total_qty': float(total_qty),
                'my_qty': my_qty,
                'others_qty': others_qty
            })
    
    # Process bids (buy side)
    if isinstance(bids_df, pd.DataFrame):
        for _, row in bids_df.iterrows():
            price = row.get('bids_price', row.get('price', row.iloc[0]))
            total_qty = row.get('bids_qty', row.get('qty', row.get('quantity', row.iloc[1])))
            
            # Find my orders at this exact price (remove tolerance - use exact match)
            my_orders_at_price = my_orders_df[
                (my_orders_df['side'] == 'BUY') & 
                (my_orders_df['price'] == float(price))
            ]
            
            # Sum only the orders at THIS specific price, not cumulative
            my_qty = my_orders_at_price['remain_volume'].sum() if not my_orders_at_price.empty else 0
            others_qty = max(0, float(total_qty) - my_qty)  # Ensure non-negative
            
            result_data.append({
                'side': 'BID',
                'price': float(price),
                'total_qty': float(total_qty),
                'my_qty': my_qty,
                'others_qty': others_qty
            })
    
    # Create DataFrame and sort

    result_df = pd.DataFrame(result_data)
    # logging.info("result df")
    # logging.info(result_df)


    if result_df.empty:
        return result_df
    
    # Sort asks ascending, bids descending
    asks_sorted = result_df[result_df['side'] == 'ASK'].sort_values('price')
    bids_sorted = result_df[result_df['side'] == 'BID'].sort_values('price', ascending=False)
    
    return pd.concat([asks_sorted, bids_sorted], ignore_index=True)


def place_limit_buy_order(spot_client, price, volume, symbol='bybusdt'):
    try:
        logging.info(f"准备下限价买单: 价格={price}, 数量={volume}, 交易对={symbol}")
        
        # 下限价买单 - type=1 (限价单)
        result = spot_client.new_order(
            symbol=symbol,
            price=str(price),   # 限价单需要指定价格
            volume=str(volume),  # 要买多少个币
            side="BUY",
            type=1  # 限价单
        )
        
        if result and result.get("order_id"):
            logging.info(f"✅ 限价买单成功: order_id={result['order_id']}")
            return result
        else:
            logging.error(f"❌ 限价买单失败: {result}")
            return None
            
    except Exception as e:
        logging.error(f"下限价买单失败: {e}")
        return None


def place_limit_sell_order(spot_client, price, volume, symbol='bybusdt'):
    """
    下限价卖单
    
    Args:
        spot_client: 交易客户端
        price: 卖出价格
        volume: 卖出数量（币的数量）
        symbol: 交易对
    
    Returns:
        dict: 订单结果
    """
    try:
        logging.info(f"准备下限价卖单: 价格={price}, 数量={volume}, 交易对={symbol}")
        
        # 下限价卖单 - type=1 (限价单)
        result = spot_client.new_order(
            symbol=symbol,
            price=str(price),   # 限价单需要指定价格
            volume=str(volume),  # 要卖多少个币
            side="SELL",
            type=1  # 限价单
        )
        
        if result and result.get("order_id"):
            logging.info(f"✅ 限价卖单成功: order_id={result['order_id']}")
            return result
        else:
            logging.error(f"❌ 限价卖单失败: {result}")
            return None
            
    except Exception as e:
        logging.error(f"下限价卖单失败: {e}")
        return None



def cancel_order(spot_client, order_id, symbol='usdtusd'):
    """
    撤销订单
    
    Args:
        spot_client: 交易客户端
        order_id: 订单ID
        symbol: 交易对
    
    Returns:
        dict: 撤销结果
    """
    try:
        logging.info(f"准备撤销订单: order_id={order_id}")

        result = spot_client.cancel_order(
            order_id=order_id,
            symbol=symbol
        )
        
        if result:
            logging.info(f"✅ 撤销订单成功: {result}")
            return result
        else:
            logging.warning(f"❌ 撤销订单失败")
            return None
            
    except Exception as e:
        logging.error(f"撤销订单失败: {e}")
        return None



def execute_wash_trading_simulation(symbol="bybusdt"):
    """
    执行正常刷量
    """
    try:
        # 获取做市商价格
        bid_price, ask_price = get_current_mm_prices(symbol)
        
        # Add None check
        if bid_price is None or ask_price is None:
            logging.warning("无法获取做市商价格，跳过刷量")
            return

        # 获取订单簿
        df_asks, df_bids = spot_market.get_orderbook(symbol)
        if df_asks.empty or df_bids.empty:
            logging.warning("订单簿为空，跳过刷量")
            return

        # 随机选择方向 (70% 买, 30% 卖)
        order_side = generate_random_side()[0]
        
        # 计算刷量数量
        if order_side == "buy":
            top_3_qty = df_asks['asks_qty'].head(3).astype(float).sum()
        else:
            top_3_qty = df_bids['bids_qty'].head(3).astype(float).sum()
            
        ratio = random.uniform(0.6, 1.0)
        order_qty = int(max(50, min(top_3_qty * ratio, 2000.0 + random.uniform(-300,200))))

        # 价格匹配检查
        if order_side == "buy":
            current_ask_price = float(df_asks['asks_price'].iloc[0])
            if ask_price is not None and abs(current_ask_price - ask_price) < 0.0001:
                order_price = current_ask_price
            else:
                logging.info(f"卖一档不是做市商价格，跳过买单刷量 (current: {current_ask_price}, mm: {ask_price})")
                return
        else:
            current_bid_price = float(df_bids['bids_price'].iloc[0])
            if bid_price is not None and abs(current_bid_price - bid_price) < 0.0001:
                order_price = current_bid_price
            else:
                logging.info(f"买一档不是做市商价格，跳过卖单刷量 (current: {current_bid_price}, mm: {bid_price})")
                return

        # 一档价差检测
        current_ask_price = float(df_asks['asks_price'].iloc[0])
        current_bid_price = float(df_bids['bids_price'].iloc[0])
        current_spread_ratio = current_ask_price / current_bid_price - 1
        if current_spread_ratio > 0.005:
            logging.info(f"一档价差超过千五，跳过刷量 (current_spread_ratio: {current_spread_ratio}, "
                         f"current_ask_price: {current_ask_price}), current_bid_price: {current_bid_price}")
            return

        # 执行刷量
        logging.info(f"执行刷量: {order_side}, 数量={order_qty}, 价格={order_price:.4f}")
        
        order = spot_client.self_trade(
            symbol=symbol,
            side=order_side,
            volume=order_qty,
            type=1,
            price=str(order_price)
        )
        
        # Updated response handling
        if order is None:
            logging.error("API返回空响应")
            return
        
        # Check for the correct response format
        if isinstance(order, dict):
            if 'code' in order:
                # Handle error response format
                if order['code'] == '0':
                    logging.info(f"刷量成功: {order}")
                else:
                    logging.error(f"刷量失败: {order}")
            elif 'trade_id' in order:
                # Handle successful trade response format
                trade_id = order.get('trade_id', 0)
                if trade_id > 0:
                    logging.info(f"刷量成功: trade_id={trade_id}, response={order}")
                else:
                    logging.warning(f"刷量可能未完全成功: {order}")
            else:
                logging.warning(f"未知响应格式: {order}")
        else:
            logging.error(f"意外的响应类型: {type(order)}, 内容: {order}")
            
    except Exception as e:
        logging.error(f"执行刷量异常: {e}")



def execute_wash_trading(symbol):
    """
    执行正常刷量
    """
    try:
        # 获取做市商价格
        bid_price, ask_price = get_current_mm_prices(symbol)
        
        if bid_price is None or ask_price is None:
            logging.warning("无法获取做市商价格，跳过刷量")
            return
    
        # 获取订单簿
        df_asks, df_bids = spot_market.get_orderbook(symbol)
        if df_asks.empty or df_bids.empty:
            logging.warning("订单簿为空，跳过刷量")
            return

        order_side = generate_random_side()[0]
        
        # 计算刷量数量
        if order_side == "buy":
            top_3_qty = df_asks['asks_qty'].head(3).astype(float).sum()
        else:
            top_3_qty = df_bids['bids_qty'].head(3).astype(float).sum()
            
        ratio = random.uniform(0.1, 0.8)
        order_qty = int(max(50, min(top_3_qty * ratio, 2000.0 + random.uniform(-300,200))))

        # 价格匹配检查
        if order_side == "buy":
            current_ask_price = float(df_asks['asks_price'].iloc[0])
            if abs(current_ask_price - ask_price) < 0.0001:
                order_price = current_ask_price
            else:
                logging.info("卖一档不是做市商价格，跳过买单刷量")
                return
        else:
            current_bid_price = float(df_bids['bids_price'].iloc[0])
            if abs(current_bid_price - bid_price) < 0.0001:
                order_price = current_bid_price
            else:
                logging.info("买一档不是做市商价格，跳过卖单刷量")
                return
        
        # 一档价差检测
        current_ask_price = float(df_asks['asks_price'].iloc[0])
        current_bid_price = float(df_bids['bids_price'].iloc[0])
        current_spread_ratio = current_ask_price / current_bid_price - 1
        if current_spread_ratio > 0.005:
            logging.info(f"一档价差超过千五，跳过刷量 (current_spread_ratio: {current_spread_ratio}, "
                         f"current_ask_price: {current_ask_price}), current_bid_price: {current_bid_price}")
            return

        # 执行刷量
        # order_qty = max(int(random.normalvariate(50, 600)), order_qty)
        logging.info(f"执行刷量: {order_side}, 数量={order_qty}, 价格={order_price:.4f}")
        
        order = spot_client.self_trade(
            symbol=symbol,
            side=order_side,
            volume=order_qty,
            type=1,
            price=str(order_price)
        )
        
        if order and 'code' in order:
            if order['code'] == '0':
                logging.info(f"刷量订单执行成功: {order}")
            else:
                logging.error(f"刷量订单执行失败: {order.get('msg', '未知错误')}")
        else:
            logging.info(f"刷量订单提交: {order}")

        
        # if order and 'code' in order and order['code'] == '0':
        #     logging.info(f"刷量成功: {order}")
        # else:
        #     logging.error(f"刷量失败: {order}")
            
    except Exception as e:
        logging.error(f"执行刷量异常: {e}")



def execute_push_trading(symbol, strategy, target_ret, current_price, user_order_limit=200):
    try:
        orders_records = []
        # 获取自己的挂单
        my_orders_df = get_orders(spot_client, symbol)
        # 匹配订单簿
        matched_orders = match_orders_with_orderbook(my_orders_df, symbol)

        if matched_orders.empty:
            logging.warning("无法获取匹配的订单簿信息")
            return None

        buy_order_excess_id = None
        logging.info(f"开始执行推价策略: {strategy}, 目标回报率: {target_ret:.4f}, 当前价格: {current_price:.4f}")
        
        if strategy == 'push_up':
            # 推高价格，处理卖单
            side = 'buy'
            
            # 计算目标价格
            target_price = current_price * (1 + target_ret)
            target_price = math.ceil(target_price * 10000) / 10000
            
            # 筛选ASK订单
            ask_orders = matched_orders[matched_orders['side'] == 'ASK'].sort_values('price')

            cumulative_amount = 0  # 累计吃单金额

            logging.info(f"Push Up - 目标价格: {target_price:.4f}, 吃单限额: ${user_order_limit}")
            logging.info(f"找到 {len(ask_orders)} 个ASK订单可供推价")
            
            for _, row in ask_orders.iterrows():
                price = row['price']
                my_qty = row['my_qty']
                others_qty = row['others_qty']
                total_qty = my_qty + others_qty  # 该档位总数量

                # 如果价格超过目标价格，停止
                if price > target_price:
                    logging.info(f"达到目标价格 {target_price:.4f}，停止推价")
                    break

                # 如果该档位没有订单，跳过
                if total_qty <= 0:
                    continue

                # 计算该档位总金额
                total_amount = total_qty * price

                # 判断金额是否小于阈值
                if total_amount <= user_order_limit - cumulative_amount:
                    # 金额小于阈值，全部吃掉
                    eat_qty = int(total_qty)
                    cumulative_amount += total_amount
                    logging.info(f"推价买单: 全部吃掉该档位 {eat_qty} @ {price:.4f}, 金额: ${total_amount:.2f}")

                    # 执行限价买单（推价时直接下单，不用self_trade）
                    order = place_limit_buy_order(spot_client, price, eat_qty, symbol)
                    if order:
                        logging.info(f"推价买单成功: order_id={order['order_id']}")
                        orders_records.append({
                            'price': price,
                            'eat_qty': eat_qty,
                            'symbol': symbol,
                            'order_id': order['order_id'],
                            'timestamp': datetime.now(),
                            'trade_direction': 'buy'
                        })
                        time.sleep(0.5)
                        buy_order_excess_id = order['order_id']

                else:
                    # 金额大于阈值，只吃阈值数量
                    remaining_limit = user_order_limit - cumulative_amount
                    if remaining_limit > 0:
                        eat_qty = int(remaining_limit / price)
                        if eat_qty > 0:
                            actual_amount = eat_qty * price
                            cumulative_amount += actual_amount
                            logging.info(f"推价买单: 部分吃掉该档位 {eat_qty} @ {price:.4f}, 金额: ${actual_amount:.2f}")
                            logging.info(f"该档位还剩余订单 {total_qty - eat_qty} @ {price:.4f}，停止推价")

                            # 执行限价买单（推价时直接下单，不用self_trade）
                            order = place_limit_buy_order(spot_client, price, eat_qty, symbol)
                            if order:
                                logging.info(f"推价买单成功: order_id={order['order_id']}")
                                orders_records.append({
                                    'price': price,
                                    'eat_qty': eat_qty,
                                    'symbol': symbol,
                                    'order_id': order['order_id'],
                                    'timestamp': datetime.now(),
                                    'trade_direction': 'buy'
                                })
                        else:
                            logging.info(f"剩余限额不足以吃掉该档位任何订单，停止推价")
                    else:
                        logging.info(f"已达订单限额 ${user_order_limit}，停止推价")
                    break  # 达到限额或部分吃掉后停止推价

            logging.info(f"Push Up完成 - 累计吃单金额: ${cumulative_amount:.2f}")
            
        else:  # push_down
            # 推低价格，处理买单
            side = 'sell'

            # 计算目标价格
            target_price = current_price * (1 + target_ret)
            target_price = math.floor(target_price * 10000) / 10000
            
            # 筛选BID订单
            bid_orders = matched_orders[matched_orders['side'] == 'BID'].sort_values('price', ascending=False)

            cumulative_amount = 0  # 累计吃单金额

            logging.info(f"Push Down - 目标价格: {target_price:.4f}, 吃单限额: ${user_order_limit}")
            logging.info(f"找到 {len(bid_orders)} 个BID订单可供推价")
            
            for _, row in bid_orders.iterrows():
                price = row['price']
                my_qty = row['my_qty']
                others_qty = row['others_qty']
                total_qty = my_qty + others_qty  # 该档位总数量

                # 如果价格低于目标价格，停止
                if price < target_price:
                    logging.info(f"达到目标价格 {target_price:.4f}，停止推价")
                    break

                # 如果该档位没有订单，跳过
                if total_qty <= 0:
                    continue

                # 计算该档位总金额
                total_amount = total_qty * price

                # 判断金额是否小于阈值
                if total_amount <= user_order_limit - cumulative_amount:
                    # 金额小于阈值，全部吃掉
                    eat_qty = int(total_qty)
                    cumulative_amount += total_amount
                    logging.info(f"推价卖单: 全部吃掉该档位 {eat_qty} @ {price:.4f}, 金额: ${total_amount:.2f}")

                    # 执行限价卖单（推价时直接下单，不用self_trade）
                    order = place_limit_sell_order(spot_client, price, eat_qty, symbol)
                    if order:
                        logging.info(f"推价卖单成功: order_id={order['order_id']}")
                        orders_records.append({
                            'price': price,
                            'eat_qty': eat_qty,
                            'symbol': symbol,
                            'order_id': order['order_id'],
                            'timestamp': datetime.now(),
                            'trade_direction': 'sell'
                        })
                        time.sleep(0.5)
                        buy_order_excess_id = order['order_id']  # 保存订单ID用于后续对冲

                else:
                    # 金额大于阈值，只吃阈值数量
                    remaining_limit = user_order_limit - cumulative_amount
                    if remaining_limit > 0:
                        eat_qty = int(remaining_limit / price)
                        if eat_qty > 0:
                            actual_amount = eat_qty * price
                            cumulative_amount += actual_amount
                            logging.info(f"推价卖单: 部分吃掉该档位 {eat_qty} @ {price:.4f}, 金额: ${actual_amount:.2f}")
                            logging.info(f"该档位还剩余订单 {total_qty - eat_qty} @ {price:.4f}，停止推价")

                            # 执行限价卖单（推价时直接下单，不用self_trade）
                            order = place_limit_sell_order(spot_client, price, eat_qty, symbol)
                            if order:
                                logging.info(f"推价卖单成功: order_id={order['order_id']}")
                                orders_records.append({
                                    'price': price,
                                    'eat_qty': eat_qty,
                                    'symbol': symbol,
                                    'order_id': order['order_id'],
                                    'timestamp': datetime.now(),
                                    'trade_direction': 'sell'
                                })
                                buy_order_excess_id = order['order_id']  # 保存订单ID用于后续对冲
                        else:
                            logging.info(f"剩余限额不足以吃掉该档位任何订单，停止推价")
                    else:
                        logging.info(f"已达订单限额 ${user_order_limit}，停止推价")
                    break  # 达到限额或部分吃掉后停止推价

            logging.info(f"Push Down完成 - 累计吃单金额: ${cumulative_amount:.2f}")
        

        
        # 发送推价完成通知
        message = f"推价完成: {strategy}\n"
        message += f"目标价格: {target_price:.4f}\n"
        message += f"吃单消耗: ${cumulative_amount:.2f}/{user_order_limit}"
        send_telegram_message(message)
        if orders_records:
            orders_df = pd.DataFrame(orders_records)
            store_user_trade(orders_df)
        
        logging.info(f"推价策略 {strategy} 执行完成, buy_order_excess_id: {buy_order_excess_id}")
        return buy_order_excess_id

    except Exception as e:
        logging.error(f"执行推价异常: {e}")
        send_telegram_message(f"推价异常: {strategy} - {str(e)}")
        return None



def execute_push_trading_simulaton(symbol, strategy, target_ret, current_price, user_order_limit=200):
    try:
        # 获取自己的挂单
        orders_records = []
        my_orders_df = get_orders(spot_client, symbol)
        # 匹配订单簿
        matched_orders = match_orders_with_orderbook(my_orders_df, symbol)
        
        if matched_orders.empty:
            logging.warning("无法获取匹配的订单簿信息")
            return
        
        if strategy == 'push_up':
            side = 'buy'
            # 计算目标价格
            target_price = current_price * (1 + target_ret)
            target_price = math.ceil(target_price * 10000) / 10000

            # 筛选ASK订单
            ask_orders = matched_orders[matched_orders['side'] == 'ASK'].sort_values('price')
            cumulative_user_amount = 0  # 累计用户订单金额
            logging.info(f"[模拟] Push Up - 目标价格: {target_price:.4f}, 用户ask订单: f{len(ask_orders)}, 用户订单限额: ${user_order_limit}")
            
            for _, row in ask_orders.iterrows():
                price = row['price']
                my_qty = row['my_qty']
                others_qty = row['others_qty']

                logging.info(f"当前订单价格： {price:.4f}，当前我方铺单数量：{my_qty:.4f}，当前价格用户订单量: {others_qty:.4f}，")

                # 如果价格超过目标价格，停止
                if price > target_price:
                    logging.info(f"[模拟] 达到目标价格 {target_price:.4f}，停止推价")
                    break

                # 1. 先判断这一档是否有用户订单
                if others_qty > 0:
                    # 有用户订单，尝试吃掉
                    user_amount = others_qty * price
                    
                    # 检查是否超过限额
                    if cumulative_user_amount + user_amount <= user_order_limit:
                        # 全部吃掉
                        eat_qty = int(others_qty)
                        cumulative_user_amount += user_amount
                        logging.info(f"[模拟] Limit order: 吃掉用户卖单 {eat_qty} @ {price:.4f}, 金额: ${user_amount:.2f}")
                        
                        # 执行限价买单
                        order = place_limit_buy_order(spot_client, price, eat_qty, symbol)
                        if order:
                            logging.info(f"[模拟] 限价买单成功: order_id={order['order_id']}")
                            orders_records.append({
                                'price': price,
                                'eat_qty': eat_qty,
                                'symbol': symbol,
                                'order_id': order['order_id'],
                                'timestamp': datetime.now(),
                                'trade_direction': 'buy'
                            })
                            time.sleep(0.5)
                        
                        # 用户订单已完全吃掉，现在可以self trade
                        if my_qty > 0:
                            logging.info(f"[模拟] Self trade: 吃掉自己的卖单 {my_qty} @ {price:.4f}")
                            order = spot_client.self_trade(
                                symbol=symbol,
                                side='buy',
                                volume=int(my_qty),
                                type=1,
                                price=str(price)
                            )
                            if order and 'trade_id' in order:
                                logging.info(f"[模拟] Self trade成功: trade_id={order['trade_id']}")
                                
                    elif cumulative_user_amount < user_order_limit:
                        # 只能部分吃掉
                        remaining_limit = user_order_limit - cumulative_user_amount
                        eat_qty = int(remaining_limit / price)
                        if eat_qty > 0:
                            cumulative_user_amount += eat_qty * price
                            logging.info(f"[模拟] Limit order: 部分吃掉用户卖单 {eat_qty} @ {price:.4f}, 金额: ${eat_qty * price:.2f}")
                            logging.info(f"[模拟] 该档位还剩余用户订单 {others_qty - eat_qty} @ {price:.4f}，不进行self trade，停止推价")
                            
                            # 执行限价买单
                            order = place_limit_buy_order(spot_client, price, eat_qty, symbol)
                            if order:
                                logging.info(f"[模拟] 限价买单成功: order_id={order['order_id']}")
                                orders_records.append({
                                'price': price,
                                'eat_qty': eat_qty,
                                'symbol': symbol,
                                'order_id': order['order_id'],
                                'timestamp': datetime.now(),
                                'trade_direction': 'buy'})
                        else:
                            logging.info(f"[模拟] 用户订单限额已用完，该档位还有用户订单，停止推价")
                        break  # 有剩余用户订单，停止推价
                    else:
                        # 已达限额，且该档位有用户订单
                        logging.info(f"[模拟] 已达用户订单限额 ${user_order_limit}，该档位还有用户订单，停止推价")
                        break
                        
                else:
                    # 没有用户订单，只有自己的订单，可以直接self trade
                    if my_qty > 0:
                        logging.info(f"[模拟] Self trade: 吃掉自己的卖单 {my_qty} @ {price:.4f}")
                        order = spot_client.self_trade(
                            symbol=symbol,
                            side='buy',
                            volume=int(my_qty),
                            type=1,
                            price=str(price)
                        )
                        if order and 'trade_id' in order:
                            logging.info(f"[模拟] Self trade成功: trade_id={order['trade_id']}")
            
            logging.info(f"[模拟] Push Up完成 - 累计吃掉用户订单金额: ${cumulative_user_amount:.2f}")
            
        else:  # push_down
            # 推低价格，处理买单
            side = 'sell'
            
            # 计算目标价格
            target_price = current_price * (1 + target_ret)
            target_price = math.floor(target_price * 10000) / 10000
            
            # 筛选BID订单
            bid_orders = matched_orders[matched_orders['side'] == 'BID'].sort_values('price', ascending=False)
            logging.info(f"[模拟] Push Down - 目标价格: {target_price:.4f}, 用户bid订单: f{len(bid_orders)}, 用户订单限额: ${user_order_limit}")
            
            cumulative_user_amount = 0  # 累计用户订单金额
            
            logging.info(f"[模拟] Push Down - 目标价格: {target_price:.4f}, 用户订单限额: ${user_order_limit}")
            
            for _, row in bid_orders.iterrows():
                price = row['price']
                my_qty = row['my_qty']
                others_qty = row['others_qty']
                logging.info(f"当前订单价格： {price:.4f}，当前我方铺单数量：{my_qty:.4f}，当前价格用户订单量: {others_qty:.4f}，")
                
                # 如果价格低于目标价格，停止
                if price < target_price:
                    logging.info(f"[模拟] 达到目标价格 {target_price:.4f}，停止推价")
                    break
                
                # 1. 先判断这一档是否有用户订单
                if others_qty > 0:
                    # 有用户订单，尝试吃掉
                    user_amount = others_qty * price
                    
                    # 检查是否超过限额
                    if cumulative_user_amount + user_amount <= user_order_limit:
                        # 全部吃掉
                        eat_qty = int(others_qty)
                        cumulative_user_amount += user_amount
                        logging.info(f"[模拟] Limit order: 吃掉用户买单 {eat_qty} @ {price:.4f}, 金额: ${user_amount:.2f}")
                        
                        # 执行限价卖单
                        order = place_limit_sell_order(spot_client, price, eat_qty, symbol)
                        if order:
                            logging.info(f"[模拟] 限价卖单成功: order_id={order['order_id']}")
                            orders_records.append({
                                'price': price,
                                'eat_qty': eat_qty,
                                'symbol': symbol,
                                'order_id': order['order_id'],
                                'timestamp': datetime.now(),
                                'trade_direction': 'sell'})
                            time.sleep(0.5)
                        
                        # 用户订单已完全吃掉，现在可以self trade
                        if my_qty > 0:
                            logging.info(f"[模拟] Self trade: 吃掉自己的买单 {my_qty} @ {price:.4f}")
                            order = spot_client.self_trade(
                                symbol=symbol,
                                side='sell',
                                volume=int(my_qty),
                                type=1,
                                price=str(price)
                            )
                            if order and 'trade_id' in order:
                                logging.info(f"[模拟] Self trade成功: trade_id={order['trade_id']}")
                                
                    elif cumulative_user_amount < user_order_limit:
                        # 只能部分吃掉
                        remaining_limit = user_order_limit - cumulative_user_amount
                        eat_qty = int(remaining_limit / price)
                        if eat_qty > 0:
                            cumulative_user_amount += eat_qty * price
                            logging.info(f"[模拟] Limit order: 部分吃掉用户买单 {eat_qty} @ {price:.4f}, 金额: ${eat_qty * price:.2f}")
                            logging.info(f"[模拟] 该档位还剩余用户订单 {others_qty - eat_qty} @ {price:.4f}，不进行self trade，停止推价")
                            
                            # 执行限价卖单
                            order = place_limit_sell_order(spot_client, price, eat_qty, symbol)
                            if order:
                                logging.info(f"[模拟] 限价卖单成功: order_id={order['order_id']}")
                                orders_records.append({
                                'price': price,
                                'eat_qty': eat_qty,
                                'symbol': symbol,
                                'order_id': order['order_id'],
                                'timestamp': datetime.now(),
                                'trade_direction': 'sell'})
                        else:
                            logging.info(f"[模拟] 用户订单限额已用完，该档位还有用户订单，停止推价")
                        break  # 有剩余用户订单，停止推价
                    else:
                        # 已达限额，且该档位有用户订单
                        logging.info(f"[模拟] 已达用户订单限额 ${user_order_limit}，该档位还有用户订单，停止推价")
                        break
                        
                else:
                    # 没有用户订单，只有自己的订单，可以直接self trade
                    if my_qty > 0:
                        logging.info(f"[模拟] Self trade: 吃掉自己的买单 {my_qty} @ {price:.4f}")
                        order = spot_client.self_trade(
                            symbol=symbol,
                            side='sell',
                            volume=int(my_qty),
                            type=1,
                            price=str(price)
                        )
                        if order and 'trade_id' in order:
                            logging.info(f"[模拟] Self trade成功: trade_id={order['trade_id']}")
            
            logging.info(f"[模拟] Push Down完成 - 累计吃掉用户订单金额: ${cumulative_user_amount:.2f}")
        
        # 发送推价完成通知
        message = f"[模拟] 推价完成: {strategy}\n"
        message += f"目标价格: {target_price:.4f}\n"
        message += f"用户订单消耗: ${cumulative_user_amount:.2f}/{user_order_limit}"
        send_telegram_message(message)
        if orders_records:
            orders_df = pd.DataFrame(orders_records)
            store_user_trade(orders_df)
            
        
    except Exception as e:
        logging.error(f"[模拟] 执行推价异常: {e}")
        send_telegram_message(f"[模拟] 推价异常: {strategy} - {str(e)}")



from collections import OrderedDict

################################################# - change order function - ###########################################################
def main(symbol, user_limit_dollar=200):
    logging.info("价格推动/刷量程序启动...")
    logging.info(f"监控交易对: {symbol}")
    logging.info("策略: 目标价格偏离阈值0.1%, 偏离时执行推价, 接近时正常刷量")

    temp_ret = 0.0
    processed_ids = OrderedDict()
    max_ids = 20  # 最大保存的ID数量
    last_id = None  # 记录上次的id
    same_id_count = 0  # 相同id的计数器
    alert_threshold = 15  # 连续相同id的报警阈值
    buy_order_excess_id = None  # 初始化变量
    hedge_order_excess_id = None  # 初始化对冲订单变量
    
    while True:
        try:
            # 等待10-20秒
            sleep_time = generate_random_sleep()
            time.sleep(sleep_time)
            
            # 获取目标价格
            target_ret_df = get_target_return()
            latest_record = target_ret_df.iloc[-1]
            id_record = latest_record['id']
            target_ret = latest_record['target_return']
            timestamp = latest_record['timestamp']
            
            target_ret = float(target_ret) * 10  # ✅ Convert decimal.Decimal to float
            
            # 检查ID是否变化，用于报警监控
            if last_id == id_record:
                same_id_count += 1
            else:
                same_id_count = 0
                last_id = id_record
            
            # 如果连续多次ID没有变化，发送报警
            if same_id_count >= alert_threshold:
                alert_message = f"⚠️ 数据更新异常报警\n交易对: {symbol}\nID: {id_record} 已连续 {same_id_count} 次未更新\n时间: {timestamp}"
                send_telegram_message(alert_message)
                logging.warning(f"数据更新异常: ID {id_record} 已连续 {same_id_count} 次未更新")
                same_id_count = 0
            
            # 检查是否已经处理过这个id
            if id_record not in processed_ids:
                temp_ret += target_ret
                processed_ids[id_record] = True  # 记录ID
                
                # 手动维护最大长度
                if len(processed_ids) > max_ids:
                    # 删除最老的ID (FIFO)
                    oldest_id = next(iter(processed_ids))
                    del processed_ids[oldest_id]
                    logging.debug(f"清理最老ID: {oldest_id}, 当前保存: {len(processed_ids)}个")
                
                logging.info(f"新记录ID: {id_record}, 累计回报率: {temp_ret:.4f}")
            else:
                logging.debug(f"ID {id_record} 已处理，跳过累计")
            
            logging.debug(f"检查ID {id_record} 是否在processed_ids中: {id_record in processed_ids}")
            logging.debug(f"当前processed_ids内容: {list(processed_ids.keys())}")

            if temp_ret is None:
                logging.debug("无法获取目标价格，执行正常刷量")
                execute_wash_trading(symbol) ## 正式
                # execute_wash_trading_simulation(symbol) ## 模拟
                continue
            
            # 获取当前市场价格
            current_mid_price, current_bid, current_ask = get_current_market_price()
            if current_mid_price is None:
                logging.warning("无法获取当前市场价格，跳过本次操作")
                continue
            
            # 计算价格偏离
            deviation, direction = calculate_price_deviation(current_mid_price, temp_ret)
            
            if deviation is None:
                logging.warning("无法计算价格偏离，执行正常刷量")
                execute_wash_trading(symbol) ## 正式
                # execute_wash_trading_simulation(symbol) ## 模拟
                continue
            
            # 确定交易策略，0.1% 以外的价差就开始刷价
            strategy = determine_trading_strategy(deviation, direction, tick=0.0001, current_mid_price = current_mid_price)
            
            logging.info(f"目标回报率: {temp_ret:.4f}, 当前价格: {current_mid_price:.4f}, "
                        f"偏离: {deviation:.2f}% ({direction}), 策略: {strategy}")
            
            # 执行相应策略
            if strategy == 'wash':
                logging.info("价格接近目标，执行正常刷量")
                execute_wash_trading(symbol) ## 正式
                # execute_wash_trading_simulation(symbol) ## 模拟
            
            # 取消之前的推价订单和对冲订单
            if buy_order_excess_id:
                logging.info(f"取消之前的推价订单: {buy_order_excess_id}")
                cancel_order(spot_client, buy_order_excess_id, symbol)
                buy_order_excess_id = None
                if hedge_order_excess_id:
                    logging.info(f"取消之前的对冲订单: {hedge_order_excess_id}")
                    cancel_order(spot_client, hedge_order_excess_id, symbol)
                    hedge_order_excess_id = None

            elif strategy == 'push_up':
                logging.info("当前价格低于目标，执行推高操作")
                limit_buy_money= user_limit_dollar + 200
                buy_order_excess_id = execute_push_trading(symbol, strategy, temp_ret, current_mid_price, user_order_limit=limit_buy_money)  # 设置正常 1w 美元限额
                logging.info(f'{buy_order_excess_id=}')
                if buy_order_excess_id:
                    hedge_order_excess_id = None
                    hedge_price = round(math.ceil(current_mid_price * (1 + temp_ret) * 10000) / 10000 + 0.0002, 4)
                    logging.info(f'对冲挂单{hedge_price=}')
                    hedge_order = place_limit_sell_order(spot_client, hedge_price, volume=200,  symbol=symbol)
                    if hedge_order:
                        hedge_order_excess_id = hedge_order['order_id']
                # execute_push_trading_simulaton(symbol, strategy, temp_ret, current_mid_price, user_order_limit=user_limit_dollar)
                temp_ret = 0
                same_id_count = 0
                last_id = None

            elif strategy == 'push_down':
                logging.info("当前价格高于目标，执行推低操作")
                pushdown_dollar = user_limit_dollar / 10 + 100
                buy_order_excess_id = execute_push_trading(symbol, strategy, temp_ret, current_mid_price, user_order_limit=pushdown_dollar)  # 设置 更小，1000美元限额
                logging.info(f'{buy_order_excess_id=}')
                if buy_order_excess_id:
                    hedge_order_excess_id = None
                    hedge_price = round(math.floor(current_mid_price * (1 + temp_ret) * 10000)/10000 - 0.0002, 4)
                    logging.info(f'对冲挂单{hedge_price=}')
                    hedge_order = place_limit_buy_order(spot_client, hedge_price, volume=100,  symbol=symbol)
                    if hedge_order:
                        hedge_order_excess_id = hedge_order['order_id']
                # execute_push_trading_simulaton(symbol, strategy, temp_ret, current_mid_price, user_order_limit=user_limit_dollar)
                temp_ret = 0
                same_id_count = 0
                last_id = None

        except Exception as e:
            logging.error(f"主循环异常: {e}")
            time.sleep(10)



if __name__ == '__main__':
    main(symbol, max_usdt)
    




# def main():
#     """
#     主刷量循环：检测交易活动，在无交易时进行刷量
#     """
#     symbol = 'bybusdt'
#     logging.info("刷量程序启动，开始监控交易活动...")
#     logging.info(f"监控交易对: {symbol}")
#     logging.info("刷量策略: 检测1-4分钟无交易时进行刷量，数量为1-3档总量的10%-80%，买卖方向随机")
#     logging.info("价格联动: 与byb_mm.py生成的ask/bid档位联动，只在做市商档位上刷量")

#     while True:
#         try:
#             # 随机等待30-60秒后检查交易活动
#             sleep_time = generate_random_sleep()
#             time.sleep(sleep_time)

#             # # 检查最近是否有交易活动
#             # no_trade_threshold = random.uniform(30, 120)  # 随机1-2分钟阈值（30-120秒）
#             # need_wash_trade = check_recent_trades(symbol, no_trade_threshold)

#             # if not need_wash_trade:
#             #     logging.info("最近有交易活动，暂不刷量")
#             #     continue

#             # 获取当前做市商生成的ask和bid价格
#             current_bid, current_ask = get_current_mm_prices()

#             if current_bid is None or current_ask is None:
#                 logging.warning("无法获取做市商价格，跳过本次刷量")
#                 continue

#             p_buy = current_bid
#             p_sell = current_ask

#             # 获取订单簿数据
#             df_asks, df_bids = spot_market.get_orderbook(symbol)
#             if df_asks.empty or df_bids.empty:
#                 logging.warning("订单簿为空，跳过本次刷量")
#                 continue

#             # 随机选择交易方向
#             order_side = generate_random_side()[0]

#             # 根据订单簿计算刷量数量
#             order_qty = calculate_wash_qty_from_orderbook(symbol, order_side, df_asks, df_bids)

#             # 确定交易价格和数量
#             if order_side == "buy":
#                 # 买单：检查卖一价格是否为用户铺单
#                 current_ask_price = float(df_asks['asks_price'].iloc[0])
#                 if abs(current_ask_price - p_sell) < 0.0001:  # 使用浮点数比较
#                     order_price = current_ask_price
#                     # 限制订单量不超过买一档位的50%，更保守
#                     max_qty = max(int(float(df_bids['bids_qty'].iloc[0]) * 0.5), int(500/order_price))
#                     order_qty = min(order_qty, max_qty)

#                     # 确保数量不小于最小值
#                     if order_qty < 10:
#                         logging.info(f"计算的刷量数量过小({order_qty})，跳过本次刷量")
#                         continue
#                 else:
#                     logging.info(f"卖一档价格({current_ask_price})不是用户铺单价格({p_sell})，跳过买单刷量")
#                     continue
#             else:
#                 # 卖单：检查买一价格是否为用户铺单
#                 current_bid_price = float(df_bids['bids_price'].iloc[0])
#                 if abs(current_bid_price - p_buy) < 0.0001:  # 使用浮点数比较
#                     order_price = current_bid_price
#                     # 限制订单量不超过买一档位的50%，更保守
#                     max_qty = max(int(float(df_bids['bids_qty'].iloc[0]) * 0.5), int(500/order_price))
#                     order_qty = min(order_qty, max_qty)

#                     # 确保数量不小于最小值
#                     if order_qty < 10:
#                         logging.info(f"计算的刷量数量过小({order_qty})，跳过本次刷量")
#                         continue
#                 else:
#                     logging.info(f"买一档价格({current_bid_price})不是用户铺单价格({p_buy})，跳过卖单刷量")
#                     continue

#             # 最终数量和价格处理
#             order_qty = int(order_qty)  # 确保是整数
#             order_price = round(order_price, 4)  # 价格保留4位小数

#             # 执行刷量交易
#             logging.info(f"准备执行刷量: 方向={order_side}, 数量={order_qty}, 价格={order_price:.4f}")
#             logging.info(f"做市商价格: bid={p_buy:.4f}, ask={p_sell:.4f}")

#             try:
#                 # 验证订单参数
#                 if order_qty <= 0:
#                     logging.error(f"订单数量无效: {order_qty}")
#                     continue

#                 if order_price <= 0:
#                     logging.error(f"订单价格无效: {order_price}")
#                     continue

#                 # # 生成客户端订单ID
#                 # client_order_id = f"wash_{int(time.time() * 1000)}"

#                 # 执行自成交 - volume需要是int类型
#                 order = spot_client.self_trade(
#                     symbol=symbol,
#                     side=order_side,
#                     volume=order_qty,  # 已经是整数
#                     type=1,
#                     price=str(order_price),
#                     # clientOrderId=client_order_id
#                 )

#                 if order and 'code' in order:
#                     if order['code'] == '0':
#                         logging.info(f"刷量订单执行成功: {order}")
#                     else:
#                         logging.error(f"刷量订单执行失败: {order.get('msg', '未知错误')}")
#                 else:
#                     logging.info(f"刷量订单提交: {order}")

#             except Exception as e:
#                 logging.error(f"刷量订单执行异常: {e}")
#                 # 如果是API错误，等待更长时间再继续
#                 if "API" in str(e) or "failed" in str(e).lower():
#                     logging.info("检测到API错误，等待30秒后继续...")
#                     time.sleep(30)
#                 continue

#         except Exception as e:
#             logging.error(f"刷量循环出现错误: {e}")
#             # 出错时等待一段时间再继续
#             time.sleep(10)

