import sys
import os
# 添加包含 error.py 的目录到 Python 路径
sys.path.append('/Users/<USER>/Desktop/tsen-100ex服务器/API')
import requests
import time
import hashlib
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import random
import logging
spot_market = Spot()
from typing import Dict, Optional, List
import pandas as pd
import logging
import traceback
from typing import Union, List
import numpy as np
base_url = "https://openapi.100ex.com"## 正式服
# base_url = "https://openapi.100exdemo.com" ## 模拟服

spot_market.BASE_URL = base_url
mm_coin = 'byb' + 'usdt'

def get_kline_price(symbol='bybusdt', interval=1):
    dataframe = spot_market.get_kline(symbol=symbol, interval=interval)
    return dataframe

class NoiseFactorGenerator:
    """噪音生成"""
    def __init__(self, df: pd.DataFrame, noise_weight: float = 0.05):
        self.df = df.copy()
        self.noise_weight = noise_weight

    def add_pure_random_noise(self, scale: float = 1.0) -> pd.Series:
        returns = self.df['open'].pct_change()
        rolling_std = returns.rolling(window=10).std()
        random_noise = np.zeros(len(self.df))
        for i in range(len(self.df)):
            if pd.notna(rolling_std.iloc[i]) and rolling_std.iloc[i] > 0:
                random_noise[i] = np.random.normal(0, rolling_std.iloc[i] * scale)
            else: # 前10个点或标准差为0时，使用小的固定值
                random_noise[i] = np.random.normal(0, 0.0001 * scale)
        
        return pd.Series(random_noise, index=self.df.index)
    
    def add_cta_noise(self, indicators: List[str] = None) -> pd.Series:
        if indicators is None:
            indicators = ['MA_CROSS', 'Z_score', 'MOMENTUM']
        
        selected_indicator = np.random.choice(indicators)
        
        if selected_indicator == 'MA_CROSS':
            fast = np.random.choice([5, 10, 15])
            slow = np.random.choice([20, 30, 60])
            ma_fast = self.df['open'].rolling(window=fast).mean()
            ma_slow = self.df['open'].rolling(window=slow).mean()
            indicator_value = (ma_fast - ma_slow) / ma_slow
            
        elif selected_indicator == 'Z_score':
            period = np.random.choice([10, 20, 30])
            sma = self.df['open'].rolling(window=period).mean()
            std = self.df['open'].rolling(window=period).std()
            zscore = (self.df['open'] - sma) / std
            indicator_value = zscore * (std/self.df['open'])
            
        else: ## momentum, just pct change
            period = np.random.choice([5, 10, 20])
            momentum = self.df['open'].pct_change(period)
            momentum_std = momentum.rolling(period).std().fillna(0)
            indicator_value = momentum + np.random.normal(loc=0, scale=momentum_std, size=len(momentum))
        
        indicator_series = pd.Series(indicator_value, index=self.df.index)
        indicator_series = indicator_series.fillna(0)
        
        return indicator_series

    def calculate_returns_with_noise(self, noise_type: str = 'mixed') -> pd.DataFrame:
        self.df['returns'] = self.df['close'].pct_change()
        if noise_type == 'random':
            noise = self.add_pure_random_noise()
        elif noise_type == 'cta':
            noise = self.add_cta_noise()
        else:  # use mixed
            random_noise = self.add_pure_random_noise()
            cta_noise = self.add_cta_noise()
            w1, w2 = np.random.dirichlet([1, 1])
            noise = w1 * random_noise + w2 * cta_noise
        self.df['noise_factor'] = noise
        self.df['returns_with_noise'] = self.df['returns'] + self.noise_weight * noise
        
        return self.df

def apply_noise_to_kline(df: pd.DataFrame, noise_weight: float = 0.05):
    generator = NoiseFactorGenerator(df, noise_weight)    
    result = generator.calculate_returns_with_noise(noise_type='mixed')
    return result

print('get kline data')
df = get_kline_price(mm_coin, 1)
print(df)
noisedf = apply_noise_to_kline(df, noise_weight=0.1)
print(noisedf)





