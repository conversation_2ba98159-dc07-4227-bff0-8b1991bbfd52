#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试间隔计算问题
分析为什么实际等待时间比动态最大间隔要大
"""

import random
import logging
from datetime import datetime, timedelta
import pytz

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

def get_trading_config():
    """获取交易配置"""
    return {
        "symbol": "ongusdt",
        "side": "BUY",
        "type": 2,
        "min_order_size": 100.0,
        "max_order_size": 10000.0,
        "min_interval": 60,
        "max_interval": 3600,  # 默认最大间隔1小时
    }

def calculate_dynamic_max_interval(days: float, estimated_orders: int, min_interval: int = 60) -> int:
    """计算动态最大时间间隔"""
    try:
        # 计算总可用时间（秒）
        total_seconds = int(days * 24 * 3600)
        
        # 基础计算：总时间除以订单数，确保订单能均匀分布
        base_interval = total_seconds / max(estimated_orders, 1)
        
        # 添加多重随机性因子，增加反检测能力
        # 1. 基础随机因子：在0.7-1.6倍之间随机
        random_factor = random.uniform(0.7, 1.6)
        
        # 2. 时间分布不均匀因子：模拟真实交易行为的不规律性
        distribution_factor = random.uniform(1.1, 2.2)
        
        # 3. 市场活跃度因子：模拟不同时段的交易活跃度变化
        market_activity_factor = random.uniform(0.9, 1.4)
        
        # 4. 波动性因子：增加间隔的波动性
        volatility_factor = random.uniform(0.8, 1.3)
        
        # 5. 基于当前时间的微调因子（增加时间相关性）
        current_hour = datetime.now(beijing_tz).hour
        time_factor = 1.0
        if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
            # 交易活跃时段，间隔稍短
            time_factor = random.uniform(0.9, 1.1)
        elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
            # 午休或晚餐时段，间隔稍长
            time_factor = random.uniform(1.1, 1.3)
        else:
            # 其他时段，正常间隔
            time_factor = random.uniform(0.95, 1.15)
        
        # 综合计算动态最大间隔
        dynamic_max_interval = int(base_interval * random_factor * distribution_factor * 
                                 market_activity_factor * volatility_factor * time_factor)
        
        # 设置合理的上下限，考虑不同时间范围的需求
        min_max_interval = min_interval * 2  # 最小最大间隔为最小间隔的2倍
        
        # 根据总时间动态调整最大间隔上限
        if days <= 1:
            # 1天内：最大不超过1小时
            max_max_interval = min(total_seconds // 6, 3600)
        elif days <= 3:
            # 1-3天：最大不超过2小时
            max_max_interval = min(total_seconds // 5, 7200)
        elif days <= 7:
            # 3-7天：最大不超过4小时
            max_max_interval = min(total_seconds // 4, 14400)
        else:
            # 7天以上：最大不超过6小时
            max_max_interval = min(total_seconds // 3, 21600)
        
        # 确保在合理范围内
        dynamic_max_interval = max(min_max_interval, min(dynamic_max_interval, max_max_interval))
        
        # 添加最后的随机微调（±5%）
        final_adjustment = random.uniform(0.95, 1.05)
        dynamic_max_interval = int(dynamic_max_interval * final_adjustment)
        
        logging.info(f"动态最大间隔计算详情:")
        logging.info(f"  总时间: {total_seconds}秒 ({days}天)")
        logging.info(f"  预估订单: {estimated_orders}")
        logging.info(f"  基础间隔: {base_interval:.1f}秒")
        logging.info(f"  随机因子: {random_factor:.3f}")
        logging.info(f"  分布因子: {distribution_factor:.3f}")
        logging.info(f"  市场活跃度因子: {market_activity_factor:.3f}")
        logging.info(f"  波动性因子: {volatility_factor:.3f}")
        logging.info(f"  时间因子: {time_factor:.3f} (当前小时: {current_hour})")
        logging.info(f"  最终调整: {final_adjustment:.3f}")
        logging.info(f"  最终最大间隔: {dynamic_max_interval}秒")
        
        return dynamic_max_interval
        
    except Exception as e:
        logging.error(f"计算动态最大间隔失败: {str(e)}")
        return 3600

def generate_orders_with_interval(total_amount: float, each_amount: float, current_price: float,
                                 start_time: datetime, end_time: datetime, trading_config: dict,
                                 max_interval: int) -> list:
    """使用指定最大间隔生成订单计划"""
    
    # 确定单次下单量范围
    if each_amount is not None and each_amount > 0:
        max_order_size_base = each_amount
        min_order_size_base = min(trading_config["min_order_size"], max_order_size_base/current_price * 0.5)
    else:
        max_order_size_base = trading_config["max_order_size"]
        min_order_size_base = trading_config["min_order_size"]

    # 分解订单
    remaining_amount = total_amount
    schedule = []
    current_time = start_time

    while remaining_amount > 0 and current_time < end_time:
        # 随机订单大小
        if each_amount and each_amount > remaining_amount:
            order_amount = int(remaining_amount)
        elif int(min_order_size_base*current_price) < int(min(each_amount or max_order_size_base, remaining_amount)):
            order_amount = random.randint(
                int(min_order_size_base*current_price),
                int(min(each_amount or max_order_size_base, remaining_amount))
            )
        else:
            order_amount = int(remaining_amount)
        
        logging.info(f"当次金额：{order_amount:.2f}, 剩余金额: {remaining_amount:.2f}")

        # 随机时间间隔
        interval = random.uniform(
            trading_config["min_interval"],
            max_interval
        )
        
        logging.info(f"  使用最大间隔: {max_interval}秒")
        logging.info(f"  生成随机间隔: {interval:.1f}秒")

        # 确保不超过结束时间
        next_time = current_time + timedelta(seconds=interval)
        if next_time > end_time:
            next_time = end_time

        order = {
            'scheduled_time': next_time,
            'amount': order_amount,
            'symbol': trading_config["symbol"],
            'side': trading_config["side"],
            'type': trading_config["type"],
            'status': 'pending'
        }

        schedule.append(order)
        remaining_amount -= order_amount
        current_time = next_time

        # 防止无限循环
        if len(schedule) > 10000:
            logging.warning("订单数量过多，停止生成")
            break
    
    return schedule

def test_interval_issue():
    """测试间隔问题"""
    print("=" * 60)
    print("测试间隔计算问题".center(60))
    print("=" * 60)
    
    # 模拟参数
    days = 1.0
    total_amount = 380.0
    each_amount = 38.0
    current_price = 1.0
    
    # 获取配置
    trading_config = get_trading_config()
    
    # 计算时间范围
    start_time = datetime.now(beijing_tz)
    end_time = start_time + timedelta(days=days)
    
    print(f"测试参数:")
    print(f"  总时间: {days}天")
    print(f"  总金额: {total_amount}")
    print(f"  单次最大: {each_amount}")
    print(f"  默认最大间隔: {trading_config['max_interval']}秒")
    print()
    
    # 第一次生成：使用默认最大间隔
    print("第一次生成订单（使用默认最大间隔3600秒）:")
    schedule1 = generate_orders_with_interval(
        total_amount, each_amount, current_price, start_time, end_time,
        trading_config, trading_config["max_interval"]
    )
    
    print(f"生成订单数量: {len(schedule1)}")
    
    # 计算动态最大间隔
    print("\n计算动态最大间隔:")
    dynamic_max_interval = calculate_dynamic_max_interval(days, len(schedule1), trading_config["min_interval"])
    
    # 检查是否需要重新生成订单计划
    default_max_interval = trading_config["max_interval"]
    interval_diff_ratio = abs(dynamic_max_interval - default_max_interval) / default_max_interval
    
    print(f"间隔差异比例: {interval_diff_ratio:.2%}")
    
    # 检查实际生成的订单间隔是否都符合动态最大间隔
    need_regenerate = False
    if len(schedule1) > 1:
        for i in range(1, len(schedule1)):
            prev_time = schedule1[i-1]['scheduled_time']
            current_time = schedule1[i]['scheduled_time']
            actual_interval = (current_time - prev_time).total_seconds()
            if actual_interval > dynamic_max_interval:
                print(f"订单{i+1}间隔{actual_interval:.1f}秒超过动态最大间隔{dynamic_max_interval}秒")
                need_regenerate = True
    
    # 如果间隔差异较大或有订单超过动态最大间隔，重新生成
    if interval_diff_ratio > 0.2 or need_regenerate:
        reason = f"间隔差异{interval_diff_ratio:.2%}" if interval_diff_ratio > 0.2 else "有订单超过动态最大间隔"
        print(f"需要重新生成订单计划: {reason}")
        schedule2 = generate_orders_with_interval(
            total_amount, each_amount, current_price, start_time, end_time,
            trading_config, dynamic_max_interval
        )
        print(f"重新生成后订单数量: {len(schedule2)}")
        final_schedule = schedule2
    else:
        print("间隔差异不大且无订单超过动态最大间隔，使用原计划")
        final_schedule = schedule1
    
    # 分析等待时间
    print("\n分析等待时间:")
    for i, order in enumerate(final_schedule):
        if i == 0:
            # 第一个订单等待60秒
            wait_seconds = 60
            print(f"订单{i+1}: 等待60秒（第一个订单固定等待）")
        else:
            # 计算等待时间
            prev_time = final_schedule[i-1]['scheduled_time']
            current_time = order['scheduled_time']
            wait_seconds = (current_time - prev_time).total_seconds()
            print(f"订单{i+1}: 等待{wait_seconds:.1f}秒")
            
            # 检查是否超过最大间隔
            if wait_seconds > dynamic_max_interval:
                print(f"  ⚠️ 警告：等待时间{wait_seconds:.1f}秒超过动态最大间隔{dynamic_max_interval}秒")
            elif wait_seconds > trading_config["max_interval"]:
                print(f"  ⚠️ 警告：等待时间{wait_seconds:.1f}秒超过默认最大间隔{trading_config['max_interval']}秒")

if __name__ == "__main__":
    test_interval_issue() 