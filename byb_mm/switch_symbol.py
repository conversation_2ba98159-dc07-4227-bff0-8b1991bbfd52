#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的交易对切换工具
直接修改源文件中的 get_current_symbol() 函数
"""

import os
import sys
import re

def get_files_to_modify():
    """获取需要修改的文件列表"""
    return [
        "byb_mm/byb_buy_back.py",
        "byb_mm/byb_order_algorithm.py"
    ]

def get_current_symbol_from_file(file_path):
    """从文件中获取当前的交易对设置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 get_current_symbol 函数中的 return 语句
        pattern = r'def get_current_symbol\(\):[^}]*?return\s+"([^"]+)"'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            return match.group(1)
        else:
            return None
            
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {str(e)}")
        return None

def update_symbol_in_file(file_path, new_symbol):
    """更新文件中的交易对设置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 get_current_symbol 函数中的 return 语句
        pattern = r'(def get_current_symbol\(\):[^}]*?return\s+)"[^"]+"'
        replacement = f'\\1"{new_symbol}"'
        
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            return True
        else:
            print(f"⚠️ 在文件 {file_path} 中未找到需要替换的内容")
            return False
            
    except Exception as e:
        print(f"更新文件 {file_path} 失败: {str(e)}")
        return False

def show_current_config():
    """显示当前配置"""
    print("=" * 60)
    print("当前交易对配置")
    print("=" * 60)
    
    files = get_files_to_modify()
    symbols = {}
    
    for file_path in files:
        if os.path.exists(file_path):
            symbol = get_current_symbol_from_file(file_path)
            symbols[file_path] = symbol
            print(f"{file_path:<30} {symbol or '未找到'}")
        else:
            print(f"{file_path:<30} 文件不存在")
    
    # 检查一致性
    unique_symbols = set(s for s in symbols.values() if s)
    if len(unique_symbols) == 1:
        current_symbol = list(unique_symbols)[0]
        print(f"\n✅ 当前统一使用交易对: {current_symbol.upper()}")
        
        # 显示解析结果
        if current_symbol.endswith("usdt"):
            base = current_symbol[:-4]
            quote = "usdt"
        elif current_symbol.endswith("btc"):
            base = current_symbol[:-3]
            quote = "btc"
        elif current_symbol.endswith("eth"):
            base = current_symbol[:-3]
            quote = "eth"
        else:
            base = "unknown"
            quote = "unknown"
        
        print(f"   基础货币: {base.upper()}")
        print(f"   计价货币: {quote.upper()}")
        
    elif len(unique_symbols) > 1:
        print(f"\n⚠️ 配置不一致，发现多个交易对: {', '.join(unique_symbols)}")
    else:
        print(f"\n❌ 未找到有效的交易对配置")
    
    print("=" * 60)

def switch_symbol(new_symbol):
    """切换交易对"""
    new_symbol = new_symbol.lower().strip()
    
    print(f"正在切换到交易对: {new_symbol.upper()}")
    print("-" * 40)
    
    files = get_files_to_modify()
    success_count = 0
    
    for file_path in files:
        if os.path.exists(file_path):
            print(f"更新文件: {file_path}")
            if update_symbol_in_file(file_path, new_symbol):
                print(f"  ✅ 更新成功")
                success_count += 1
            else:
                print(f"  ❌ 更新失败")
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    print("-" * 40)
    
    if success_count == len(files):
        print(f"🎉 成功切换到交易对: {new_symbol.upper()}")
        print("\n验证结果:")
        show_current_config()
    else:
        print(f"⚠️ 部分文件更新失败 ({success_count}/{len(files)})")

def list_supported_symbols():
    """列出支持的交易对"""
    print("=" * 60)
    print("支持的交易对")
    print("=" * 60)
    
    predefined = [
        ("bybusdt", "BYB/USDT", "100-800", "0.1"),
        ("manausdt", "MANA/USDT", "50-500", "0.5"),
        ("btcusdt", "BTC/USDT", "0.001-0.01", "50000"),
        ("ethusdt", "ETH/USDT", "0.01-1.0", "3000"),
    ]
    
    print("预设交易对:")
    print(f"{'交易对':<12} {'货币对':<10} {'订单量范围':<15} {'默认价格'}")
    print("-" * 50)
    
    for symbol, pair, order_range, price in predefined:
        print(f"{symbol.upper():<12} {pair:<10} {order_range:<15} {price}")
    
    print("\n支持格式:")
    print("- xxxusdt (任何币种对USDT)")
    print("- xxxbtc  (任何币种对BTC)")
    print("- xxxeth  (任何币种对ETH)")
    print("\n如果交易对不在预设列表中，会使用默认配置")
    print("=" * 60)

def interactive_mode():
    """交互式模式"""
    while True:
        print("\n" + "=" * 60)
        print("交易对切换工具")
        print("=" * 60)
        print("1. 显示当前配置")
        print("2. 切换交易对")
        print("3. 列出支持的交易对")
        print("0. 退出")
        print("=" * 60)
        
        choice = input("请选择操作 (0-3): ").strip()
        
        if choice == '0':
            print("👋 退出程序")
            break
        elif choice == '1':
            show_current_config()
        elif choice == '2':
            list_supported_symbols()
            symbol = input("\n请输入要切换的交易对 (如 bybusdt): ").strip()
            if symbol:
                switch_symbol(symbol)
            else:
                print("❌ 交易对不能为空")
        elif choice == '3':
            list_supported_symbols()
        else:
            print("❌ 无效选择，请输入0-3之间的数字")
        
        if choice != '0':
            input("\n按回车键继续...")

def main():
    """主程序"""
    if len(sys.argv) == 1:
        # 无参数，进入交互模式
        interactive_mode()
    elif len(sys.argv) == 2:
        command = sys.argv[1].lower()
        if command == "show":
            show_current_config()
        elif command == "list":
            list_supported_symbols()
        else:
            # 当作交易对名称处理
            switch_symbol(command)
    else:
        print("用法:")
        print("  python switch_symbol.py           # 交互模式")
        print("  python switch_symbol.py show      # 显示当前配置")
        print("  python switch_symbol.py list      # 列出支持的交易对")
        print("  python switch_symbol.py bybusdt   # 切换到指定交易对")

if __name__ == "__main__":
    main()
