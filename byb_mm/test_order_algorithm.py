#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BYB智能回购下单算法测试脚本
功能：测试回购算法的核心逻辑，不涉及数据库读取和真实交易
"""

import asyncio
import logging
import json
import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import pytz

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_order_algorithm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

class MockBYBOrderAlgorithm:
    """模拟BYB智能下单算法类 - 仅用于测试"""
    
    def __init__(self):
        """初始化测试算法"""
        # 算法状态
        self.is_running = False
        self.total_executed = 0
        self.orders_executed = 0
        self.start_time = None
        self.end_time = None

        # 订单计划
        self.order_schedule = []
        self.execution_log = []

        # 测试配置
        self.test_config = {
            "symbol": "bybusdt",
            "side": "BUY",
            "type": 2,
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "min_interval": 60,
            "max_interval": 3600,
            "default_price": 0.1
        }

        logging.info("测试算法初始化完成")
    
    def get_mock_buy_back_params(self) -> Dict:
        """获取模拟回购参数"""
        # 模拟数据库参数
        mock_params = {
            'id': 1,
            'days': 1.0,  # 1天
            'total_amount': 5000.0,  # 5000 USDT
            'each_amount': 500.0,  # 单次最大500 USDT
            'created_at': datetime.now(beijing_tz),
            'status': 1  # 可执行状态
        }
        logging.info(f"获取模拟参数: {mock_params}")
        return mock_params

    def get_mock_current_price(self) -> float:
        """获取模拟当前价格"""
        # 模拟价格波动
        base_price = 0.1
        variation = random.uniform(-0.01, 0.01)  # ±1%波动
        mock_price = base_price + variation
        logging.info(f"模拟当前价格: {mock_price:.6f} USDT")
        return mock_price

    def generate_order_schedule(self, days: float, total_amount: float, each_amount: float = None) -> List[Dict]:
        """生成订单时间表 - 测试版本"""
        try:
            current_price = self.get_mock_current_price()
            
            # 计算时间范围
            start_time = datetime.now(beijing_tz)
            end_time = start_time + timedelta(days=days)
            total_seconds = int(days * 24 * 3600)

            # 确定单次下单量范围
            if each_amount is not None and each_amount > 0:
                max_order_size_base = each_amount
                min_order_size_base = min(self.test_config["min_order_size"], max_order_size_base/current_price * 0.5)
            else:
                max_order_size_base = self.test_config["max_order_size"]
                min_order_size_base = self.test_config["min_order_size"]

            logging.info(f"生成订单计划: 开始时间={start_time}, 结束时间={end_time}, 总金额={total_amount:.2f} USDT")
            logging.info(f"单次下单量范围: {min_order_size_base/current_price:.2f} - {max_order_size_base/current_price:.2f} BYB")
            logging.info(f"当前BYB价格: {current_price:.6f} USDT")

            # 估算订单数量
            estimated_orders = self.estimate_order_count(total_amount, each_amount, current_price)
            logging.info(f"预估订单数量: {estimated_orders}")

            # 短期交易使用等间隔+微扰动策略
            if days <= 3:
                schedule = []
                remaining_amount = total_amount
                
                # 计算实际需要的订单数量
                if each_amount and each_amount > 0:
                    actual_orders_needed = int(total_amount / each_amount)
                    if total_amount % each_amount > 0:
                        actual_orders_needed += 1
                else:
                    actual_orders_needed = int(total_amount / max_order_size_base)
                    if total_amount % max_order_size_base > 0:
                        actual_orders_needed += 1
                
                actual_orders_needed = max(1, min(actual_orders_needed, estimated_orders * 2))
                
                # 根据订单数量调整策略
                if actual_orders_needed < 5:
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.0
                elif actual_orders_needed < 10:
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.005
                else:
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.01
                
                current_time = start_time
                
                # 生成订单
                for i in range(actual_orders_needed):
                    if remaining_amount <= 0:
                        break
                    
                    # 计算订单时间
                    if i == actual_orders_needed - 1:
                        # 最后一个订单特殊处理
                        used_time = 0
                        for j in range(1, len(schedule)):
                            prev_time = schedule[j-1]['scheduled_time']
                            curr_time = schedule[j]['scheduled_time']
                            used_time += (curr_time - prev_time).total_seconds()
                        
                        remaining_time = total_seconds - used_time
                        
                        if remaining_time > 0:
                            target_offset = remaining_time * random.uniform(0.6, 0.9)
                            target_time = current_time + timedelta(seconds=int(target_offset))
                            
                            if remaining_time < base_interval * 0.5:
                                target_time = end_time - timedelta(seconds=random.randint(60, 300))
                        else:
                            target_time = end_time - timedelta(seconds=random.randint(60, 180))
                        
                        if target_time > end_time:
                            target_time = end_time - timedelta(seconds=random.randint(30, 180))
                        if target_time <= current_time:
                            target_time = current_time + timedelta(seconds=random.randint(30, 120))
                        
                        logging.info(f"最后一个订单时间控制: 剩余时间={remaining_time:.1f}秒, 目标时间={target_time.strftime('%H:%M:%S')}")
                    else:
                        # 其他订单
                        progress_ratio = i / (actual_orders_needed - 1)
                        target_progress_time = start_time + timedelta(seconds=int(total_seconds * progress_ratio))
                        
                        if perturbation_range > 0:
                            perturbation = random.uniform(-perturbation_range, perturbation_range)
                            perturbation_seconds = int(base_interval * perturbation)
                            target_time = target_progress_time + timedelta(seconds=perturbation_seconds)
                        else:
                            target_time = target_progress_time
                        
                        if target_time <= current_time:
                            target_time = current_time + timedelta(seconds=60)
                        if target_time > end_time:
                            target_time = end_time - timedelta(seconds=base_interval)
                    
                    # 计算订单金额
                    if each_amount and each_amount > 0:
                        order_amount = min(each_amount, remaining_amount)
                    else:
                        order_amount = min(max_order_size_base, remaining_amount)
                    
                    order = {
                        'scheduled_time': target_time,
                        'amount': order_amount,
                        'symbol': self.test_config["symbol"],
                        'side': self.test_config["side"],
                        'type': self.test_config["type"],
                        'status': 'pending'
                    }
                    schedule.append(order)
                    remaining_amount -= order_amount
                    current_time = target_time
                
                # 处理剩余金额
                if remaining_amount > 0 and schedule:
                    schedule[-1]['amount'] += remaining_amount
                
                logging.info(f"生成订单计划完成: 共 {len(schedule)} 个订单")
                return schedule

        except Exception as e:
            logging.error(f"生成订单计划失败: {str(e)}")
            raise

    def estimate_order_count(self, total_amount: float, each_amount: float, current_price: float) -> int:
        """估算订单数量 - 测试版本"""
        try:
            if each_amount and each_amount > 0:
                base_orders = total_amount / each_amount
            else:
                avg_order_size = (self.test_config["min_order_size"] + self.test_config["max_order_size"]) / 2
                base_orders = total_amount / (avg_order_size * current_price)
            
            # 简化版本，减少随机性
            base_variation = random.uniform(0.9, 1.1)
            final_orders = base_orders * base_variation
            estimated_orders = max(1, int(final_orders))
            
            logging.info(f"订单数量估算: 基础订单数={base_orders:.1f}, 最终预估={estimated_orders}")
            return estimated_orders
            
        except Exception as e:
            logging.error(f"估算订单数量失败: {str(e)}")
            return 10

    async def execute_mock_order(self, order: Dict) -> bool:
        """执行模拟订单 - 不真实交易"""
        try:
            logging.info(f"执行模拟订单: {order['amount']} {order['symbol']} {order['side']}")
            
            # 模拟API调用延迟
            await asyncio.sleep(random.uniform(0.1, 0.5))
            
            # 模拟成功率（90%成功，10%失败）
            success_rate = 0.9
            if random.random() < success_rate:
                order['status'] = 'completed'
                order['result'] = {
                    'order_id': f'mock_order_{int(time.time() * 1000)}',
                    'status': 'success'
                }
                order['executed_time'] = datetime.now(beijing_tz)
                self.total_executed += order['amount']
                self.orders_executed += 1
                
                logging.info(f"模拟订单执行成功: 订单ID={order['result']['order_id']}, 金额={order['amount']}")
                return True
            else:
                order['status'] = 'failed'
                order['error'] = '模拟API调用失败'
                logging.warning(f"模拟订单执行失败: API调用失败")
                return False
                
        except Exception as e:
            order['status'] = 'failed'
            order['error'] = str(e)
            logging.error(f"模拟订单执行异常: {str(e)}")
            return False

    def save_execution_log(self):
        """保存执行日志到文件 - 测试版本"""
        try:
            log_file = f"test_order_execution_{datetime.now(beijing_tz).strftime('%Y%m%d_%H%M%S')}.json"
            log_data = {
                'test_mode': True,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': datetime.now(beijing_tz).isoformat(),
                'total_executed': self.total_executed,
                'orders_executed': self.orders_executed,
                'total_orders': len(self.order_schedule),
                'execution_log': []
            }
            
            for order in self.order_schedule:
                log_entry = order.copy()
                if 'scheduled_time' in log_entry:
                    log_entry['scheduled_time'] = log_entry['scheduled_time'].isoformat()
                if 'executed_time' in log_entry:
                    log_entry['executed_time'] = log_entry['executed_time'].isoformat()
                log_data['execution_log'].append(log_entry)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"测试执行日志已保存到: {log_file}")
            
        except Exception as e:
            logging.error(f"保存执行日志失败: {str(e)}")

    async def run_test_algorithm(self):
        """运行测试算法主程序"""
        try:
            # 获取模拟参数
            params = self.get_mock_buy_back_params()

            # 检查状态是否允许执行
            if params.get('status', 0) != 1:
                logging.info(f"参数状态不允许执行: status={params.get('status', 0)}, 需要status=1")
                return

            days = params['days']
            total_amount = params['total_amount']
            each_amount = params.get('each_amount', None)
            param_id = params['id']

            # 获取模拟价格
            current_price = self.get_mock_current_price()

            # 发送启动通知
            start_msg = (
                f"🧪 测试算法启动\n"
                f"交易对: {self.test_config['symbol'].upper()}\n"
                f"总时间范围: {days} 天\n"
                f"总下单量: {total_amount:,.2f} USDT\n"
                f"当前BYB价格: {current_price:.6f} USDT\n"
            )
            if each_amount:
                each_amount_base = each_amount / current_price
                start_msg += f"单次最大USDT: {each_amount:,.2f} USDT (约 {each_amount_base:.2f} BYB)\n"
            else:
                start_msg += f"单次量范围: {self.test_config['min_order_size']}-{self.test_config['max_order_size']} BYB\n"

            start_msg += f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            logging.info(start_msg)

            # 生成订单计划
            self.order_schedule = self.generate_order_schedule(days, total_amount, each_amount)
            self.start_time = datetime.now(beijing_tz)
            self.is_running = True

            logging.info(f"开始执行订单计划，共 {len(self.order_schedule)} 个订单")

            # 执行订单
            failed_orders = []
            for i, order in enumerate(self.order_schedule):
                if not self.is_running:
                    logging.info("算法被停止")
                    break

                # 等待到预定时间（测试版本缩短等待时间）
                now = datetime.now(beijing_tz)
                if order['scheduled_time'] > now:
                    if i == 0:
                        logging.info(f"第一个订单将在 5 秒后执行")
                        await asyncio.sleep(5)
                    else:
                        # 测试版本：缩短等待时间，最多等待10秒
                        wait_seconds = min((order['scheduled_time'] - now).total_seconds(), 10)
                        if wait_seconds > 0:
                            logging.info(f"等待 {wait_seconds:.1f} 秒执行第 {i+1} 个订单")
                            await asyncio.sleep(wait_seconds)

                # 执行订单
                success = await self.execute_mock_order(order)
                
                if not success:
                    failed_orders.append(order)
                    logging.warning(f"订单执行失败: {order.get('error', '未知错误')}")

                # 记录进度
                progress = (i + 1) / len(self.order_schedule) * 100
                logging.info(f"订单执行进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})")

                # 每5个订单发送一次进度通知
                if (i + 1) % 5 == 0 or not success:
                    progress_msg = (
                        f"📊 测试进度更新\n"
                        f"交易对: {self.test_config['symbol'].upper()}\n"
                        f"进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})\n"
                        f"已执行金额: {self.total_executed:,.2f} USDT\n"
                        f"成功订单: {self.orders_executed}\n"
                        f"失败订单: {len(failed_orders)}\n"
                        f"当前时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                    if not success:
                        progress_msg += f"\n⚠️ 订单执行失败: {order.get('error', '未知错误')}"

                    logging.info(progress_msg)

                # 短暂休息
                await asyncio.sleep(0.5)

            # 处理失败的订单
            if failed_orders:
                logging.info(f"发现 {len(failed_orders)} 个失败订单，开始处理...")
                await self._handle_failed_orders(failed_orders, total_amount, each_amount, current_price)

            # 最终金额校验
            await self._validate_total_amount(total_amount, param_id)

            # 完成通知
            completion_msg = (
                f"✅ 测试算法完成\n"
                f"交易对: {self.test_config['symbol'].upper()}\n"
                f"总订单数: {len(self.order_schedule)}\n"
                f"成功订单: {self.orders_executed}\n"
                f"失败订单: {len(failed_orders)}\n"
                f"执行金额: {self.total_executed:,.2f} USDT\n"
                f"目标金额: {total_amount:,.2f} USDT\n"
                f"完成率: {(self.total_executed/total_amount*100):.1f}%\n"
                f"成功率: {(self.orders_executed/len(self.order_schedule)*100):.1f}%\n"
                f"完成时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            logging.info(completion_msg)

            # 保存执行日志
            self.save_execution_log()

        except Exception as e:
            error_msg = f"测试算法执行异常: {str(e)}"
            logging.error(f"{error_msg}")
        finally:
            self.is_running = False

    def stop_algorithm(self):
        """停止算法执行"""
        self.is_running = False
        logging.info("测试算法停止信号已发送")

    def get_status(self) -> Dict:
        """获取算法执行状态"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'total_orders': len(self.order_schedule),
            'orders_executed': self.orders_executed,
            'total_executed': self.total_executed,
            'progress': (self.orders_executed / len(self.order_schedule) * 100) if self.order_schedule else 0
        }

    async def _handle_failed_orders(self, failed_orders: List[Dict], total_amount: float, 
                                   each_amount: float, current_price: float):
        """处理失败的订单 - 测试版本"""
        try:
            logging.info(f"开始处理 {len(failed_orders)} 个失败订单")
            
            remaining_amount = total_amount - self.total_executed
            logging.info(f"还需要执行金额: {remaining_amount:,.2f} USDT")
            
            if remaining_amount <= 0:
                logging.info("已执行金额已达到目标，无需处理失败订单")
                return
            
            # 重试失败的订单
            retry_success_count = 0
            for order in failed_orders:
                if not self.is_running:
                    break
                    
                logging.info(f"重试执行失败订单: {order['amount']} USDT")
                
                await asyncio.sleep(1)
                
                success = await self.execute_mock_order(order)
                if success:
                    retry_success_count += 1
                    logging.info(f"重试成功: {order['amount']} USDT")
                else:
                    logging.warning(f"重试失败: {order.get('error', '未知错误')}")
            
            logging.info(f"重试结果: {retry_success_count}/{len(failed_orders)} 个订单重试成功")
            
            # 如果还有未完成的金额，创建补偿订单
            remaining_amount = total_amount - self.total_executed
            if remaining_amount > 0:
                logging.info(f"重试后仍有未完成金额: {remaining_amount:,.2f} USDT，创建补偿订单")
                await self._create_compensation_orders(remaining_amount, each_amount, current_price)
            
        except Exception as e:
            logging.error(f"处理失败订单异常: {str(e)}")

    async def _create_compensation_orders(self, remaining_amount: float, each_amount: float, current_price: float):
        """创建补偿订单 - 测试版本"""
        try:
            logging.info(f"创建补偿订单，剩余金额: {remaining_amount:,.2f} USDT")
            
            if each_amount and each_amount > 0:
                compensation_orders = int(remaining_amount / each_amount)
                if remaining_amount % each_amount > 0:
                    compensation_orders += 1
            else:
                max_order_size = self.test_config["max_order_size"]
                compensation_orders = int(remaining_amount / max_order_size)
                if remaining_amount % max_order_size > 0:
                    compensation_orders += 1
            
            logging.info(f"需要创建 {compensation_orders} 个补偿订单")
            
            current_time = datetime.now(beijing_tz)
            compensation_success_count = 0
            
            for i in range(compensation_orders):
                if not self.is_running:
                    break
                
                if each_amount and each_amount > 0:
                    order_amount = min(each_amount, remaining_amount)
                else:
                    order_amount = min(self.test_config["max_order_size"], remaining_amount)
                
                compensation_order = {
                    'scheduled_time': current_time + timedelta(seconds=i * 30),  # 每30秒一个
                    'amount': order_amount,
                    'symbol': self.test_config["symbol"],
                    'side': self.test_config["side"],
                    'type': self.test_config["type"],
                    'status': 'pending',
                    'is_compensation': True
                }
                
                logging.info(f"执行补偿订单 {i+1}: {order_amount:,.2f} USDT")
                
                success = await self.execute_mock_order(compensation_order)
                if success:
                    compensation_success_count += 1
                    remaining_amount -= order_amount
                    logging.info(f"补偿订单执行成功: {order_amount:,.2f} USDT")
                else:
                    logging.warning(f"补偿订单执行失败: {compensation_order.get('error', '未知错误')}")
                
                await asyncio.sleep(1)
            
            logging.info(f"补偿订单执行完成: {compensation_success_count}/{compensation_orders} 个成功")
            
            if remaining_amount > 0:
                warning_msg = (
                    f"⚠️ 补偿订单后仍有未完成金额\n"
                    f"剩余金额: {remaining_amount:,.2f} USDT\n"
                    f"建议手动处理剩余金额"
                )
                logging.warning(warning_msg)
                
        except Exception as e:
            logging.error(f"创建补偿订单异常: {str(e)}")

    async def _validate_total_amount(self, target_amount: float, param_id: int):
        """验证总金额是否达到目标 - 测试版本"""
        try:
            completion_rate = (self.total_executed / target_amount) * 100
            deviation = abs(self.total_executed - target_amount)
            deviation_rate = (deviation / target_amount) * 100
            
            logging.info(f"金额校验结果:")
            logging.info(f"  目标金额: {target_amount:,.2f} USDT")
            logging.info(f"  实际执行: {self.total_executed:,.2f} USDT")
            logging.info(f"  完成率: {completion_rate:.1f}%")
            logging.info(f"  偏差金额: {deviation:,.2f} USDT")
            logging.info(f"  偏差率: {deviation_rate:.1f}%")
            
            validation_msg = (
                f"📊 测试金额校验结果\n"
                f"交易对: {self.test_config['symbol'].upper()}\n"
                f"目标金额: {target_amount:,.2f} USDT\n"
                f"实际执行: {self.total_executed:,.2f} USDT\n"
                f"完成率: {completion_rate:.1f}%\n"
                f"偏差率: {deviation_rate:.1f}%\n"
                f"参数ID: {param_id}"
            )
            
            if completion_rate >= 95:
                validation_msg += "\n✅ 测试基本完成"
            elif completion_rate >= 80:
                validation_msg += "\n⚠️ 测试部分完成，建议检查"
            else:
                validation_msg += "\n❌ 测试完成率过低，需要手动处理"
            
            logging.info(validation_msg)
            
        except Exception as e:
            logging.error(f"金额校验异常: {str(e)}")


async def main():
    """主程序入口"""
    try:
        print("=" * 60)
        print("BYB智能回购下单算法测试脚本".center(60))
        print("=" * 60)
        print("功能: 测试回购算法核心逻辑，不涉及数据库和真实交易")
        print("测试参数: 1天，5000 USDT，单次最大500 USDT")
        print("=" * 60)

        # 创建测试算法实例
        algorithm = MockBYBOrderAlgorithm()

        # 运行测试算法
        await algorithm.run_test_algorithm()

    except KeyboardInterrupt:
        logging.info("测试程序被用户中断")
        if 'algorithm' in locals():
            algorithm.stop_algorithm()
    except Exception as e:
        logging.error(f"测试主程序异常: {str(e)}")


if __name__ == "__main__":
    # 运行测试程序
    asyncio.run(main())
