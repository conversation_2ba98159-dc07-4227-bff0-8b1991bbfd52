import numpy as np
import pandas as pd
import random
import time
from math import sqrt, tanh
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import copy
import asyncio
# import sys
# import byex
# from byex.spot.market import Spot
# from byex.spot.trade import SpotTrade
# import pytz
# import signal
# from datetime import datetime, timedelta
# import con_pri


#
# beijing_tz = pytz.timezone("Asia/Shanghai")
# # 自定义时间格式器，确保日志时间是北京时区时间
# class BeijingTimeFormatter(logging.Formatter):
#     def converter(self, timestamp):
#         # 设置时区为北京时间
#         tz = beijing_tz
#         dt = datetime.fromtimestamp(timestamp, tz)
#         return dt.timetuple()
#
# logging.basicConfig(
#     filename='usd_mm.log',  # Log to this file
#     level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
#     format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
#     datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
# )
#
# # 自定义 Formatter 并应用
# logger = logging.getLogger()
# for handler in logger.handlers:
#     handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))
#
#
# def handle_exit(signum, frame):
#     logging.info(f"收到信号 {signum}, 进行清理...")
#     # 取消所有订单
#     try:
#         spot_client.cancel_all_orders_by_symbol('usdtusd')
#     except Exception as e:
#         logging.error(f"撤单失败: {e}")
#     # 关闭事件循环
#     loop = asyncio.get_event_loop()
#     loop.stop()
#     logging.info("程序即将退出...")
#     sys.exit(0)
#
# # 监听 Ctrl+C (SIGINT) 和 kill (SIGTERM)
# signal.signal(signal.SIGINT, handle_exit)
# signal.signal(signal.SIGTERM, handle_exit)
#
# spot_market = Spot()
# spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
#
# first_run = True


class BybMarketMaker:
    def __init__(self,
                 base_order_size=1.0,  # 基础订单量
                 num_levels=50,  # 订单簿层数
                 price_step_ratio=0.0001,  # 价格步长比例
                 inventory_limit=200000.0,  # 库存限制
                 max_inventory=500000.0,  # 最大库存量
                 price_precision=4,  # 价格精度
                 size_precision=3,  # 数量精度
                 total_side_qty=2500000,  # 单边做市总量
                 risk_aversion=0.7,  # 风险厌恶系数
                 max_leverage=8.0,  # 最大杠杆
                 adapt_window=200):  # 自适应窗口大小
        """初始化做市商参数"""
        # 设置固定随机种子以确保结果可重现
        random.seed(42)
        np.random.seed(42)

        # 基础参数
        self.inventory = 0.0  # 当前库存
        self.mid_price = None  # 中间价
        self.pnl = 0.0  # 当前盈亏
        self.inventory_lots = []  # 跟踪持仓批次
        self.base_order_size = float(base_order_size)
        self.num_levels = int(num_levels)
        self.price_step_ratio = float(price_step_ratio)
        self.inventory_limit = float(inventory_limit)
        self.max_inventory = float(max_inventory)
        self.price_precision = int(price_precision)
        self.size_precision = int(size_precision)
        self.total_side_qty = float(total_side_qty)

        # 风险控制参数
        self.risk_aversion = risk_aversion
        self.max_leverage = max_leverage
        self.adapt_window = adapt_window
        self.position_penalty = 0.0
        self.dynamic_inventory_limit = inventory_limit

        # 库存管理参数
        self.inventory_target = 0.0  # 目标库存水平
        self.inventory_speed = 0.1  # 库存调整速度
        self.inventory_penalty = 0.2  # 库存惩罚因子
        self.inventory_deviation_limit = 0.2  # 库存偏离限制
        self.inventory_reversion_speed = 0.15  # 库存回归速度
        self.inventory_penalty_factor = 0.2  # 库存惩罚因子

        # 风险窗口参数
        self.inventory_risk_window = 50
        self.execution_window = 50
        self.order_imbalance_window = 50
        self.service_window = 50
        self.orderbook_window = 50
        self.orderflow_window = 50
        self.dynamic_risk_window = 50
        self.adaptation_threshold = 20

        # 风险敏感度参数
        self.inventory_risk_aversion = 0.2
        self.execution_risk_aversion = 0.2
        self.depth_aversion = 0.2
        self.liquidity_aversion = 0.2
        self.risk_sensitivity = 0.2
        self.adaptation_rate = 0.1
        self.spread_sensitivity = 0.2
        self.spread_adjustment = 0.1
        self.orderflow_sensitivity = 0.2

        # 服务成本参数
        self.service_cost = 0.0001

        # 历史数据初始化 - 全部初始化为空列表
        self.pnl_history = []  # 盈亏历史
        self.inventory_history = []  # 库存历史
        self.mid_price_history = []  # 中间价历史
        self.vol_history = []  # 波动率历史
        self.recent_prices = []  # 最近价格
        self.execution_history = []  # 成交历史
        self.inventory_risk_history = []  # 库存风险历史
        self.execution_risk_history = []  # 执行风险历史
        self.imbalance_history = []  # 订单失衡历史
        self.imbalance_ma = []  # 订单失衡移动平均
        self.depth_history = []  # 深度历史
        self.liquidity_history = []  # 流动性历史
        self.service_history = []  # 服务成本历史
        self.information_history = []  # 信息不对称历史
        self.orderbook_history = []  # 订单簿历史
        self.orderflow_history = []  # 订单流历史
        self.risk_history = []  # 风险历史
        self.adaptation_history = []  # 自适应历史
        self.spread_history = []  # 价差历史
        self.price_history = []  # 价格历史
        self.trade_history = []  # 成交历史
        self.trade_id = 0  # 成交ID计数器

        # 订单簿状态
        self.orderbook = {'bids': [], 'asks': []}
        self.last_fill_price = None
        self.unfilled_quote_counter = {}
        self.inventory_cost = 0.0
        self.last_unfilled_quotes = None

        # 趋势分析参数
        self.trend_window = 100  # 趋势判断窗口
        self.trend_threshold = 0.01  # 趋势判断阈值
        self.trend_direction = 0  # 趋势方向
        self.price_ma = []  # 价格移动平均
        self.trend_strength = 0.0  # 趋势强度
        self.volatility_ma = []  # 波动率移动平均
        self.price_slope = 0.0  # 价格斜率

        # 交易控制参数
        self.profit_target = 0.0005  # 单笔交易目标利润
        self.loss_limit = -0.0003  # 单笔交易止损限制
        self.position_adjust_threshold = 0.1  # 仓位调整阈值
        self.price_adjust_factor = 0.003  # 价格调整因子
        self.trend_inventory_factor = 0.15  # 趋势库存调整因子
        self.volatility_threshold = 0.01  # 波动率阈值
        self.slope_threshold = 0.0005  # 斜率阈值
        self.volatility_window = 20  # 波动率计算窗口
        self.slope_window = 30  # 斜率计算窗口
        self.max_position_ratio = 0.3  # 最大仓位比例
        self.position_decay = 0.85  # 仓位衰减因子

        # 订单控制参数
        self.min_order_size = 100.0  # 最小挂单量
        self.max_order_size = 100000.0  # 最大挂单量
        self.order_size_decay = 0.95  # 挂单量衰减因子
        self.depth_factor = 0.3  # 深度影响因子

        # 价差控制参数
        self.min_spread = 0.0002  # 最小价差
        self.max_spread = 0.001  # 最大价差

        # AS模型参数
        self.as_gamma = 0.15  # 库存厌恶系数
        self.as_kappa = 0.2  # 订单到达率
        self.as_sigma = 0.15  # 价格波动率
        self.as_alpha = 0.2  # 库存调整速度
        self.as_beta = 0.15  # 价格调整速度
        self.as_inventory_target = 0.0  # 目标库存水平
        self.as_reservation_price = None  # 保留价格
        self.as_spread = None  # 最优价差

    async def _calculate_inventory_risk(self):
        """计算库存风险 (基于Guéant et al., 2012)"""
        if len(self.inventory_history) < self.inventory_risk_window:
            return 0.0

        # 计算库存波动率
        recent_inventory = np.array(self.inventory_history[-self.inventory_risk_window:])
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算库存风险
        inventory_risk = self.inventory_risk_aversion * inventory_volatility * abs(self.inventory)
        self.inventory_risk_history.append(inventory_risk)

        return inventory_risk

    async def _calculate_execution_risk(self):
        """计算执行风险 (基于Cartea & Jaimungal, 2015)"""
        if len(self.execution_history) < self.execution_window:
            return 0.0

        # 计算执行成本
        recent_executions = np.array(self.execution_history[-self.execution_window:])
        execution_costs = np.diff(recent_executions, axis=0)
        execution_risk = self.execution_risk_aversion * np.std(execution_costs)

        self.execution_risk_history.append(execution_risk)
        return execution_risk

    async def _calculate_order_imbalance(self):
        """计算订单失衡 (基于Avellaneda & Stoikov, 2008)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 0.0

        # 计算订单失衡
        imbalance = (bid_depth - ask_depth) / total_depth
        self.imbalance_history.append(imbalance)

        # 计算移动平均
        if len(self.imbalance_history) >= self.order_imbalance_window:
            ma = np.mean(self.imbalance_history[-self.order_imbalance_window:])
            self.imbalance_ma.append(ma)

        return imbalance

    async def _calculate_depth_risk(self):
        """计算订单簿深度风险 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 1.0

        # 计算深度风险
        depth_ratio = min(bid_depth, ask_depth) / max(bid_depth, ask_depth)
        depth_risk = 1.0 - self.depth_aversion * depth_ratio

        self.depth_history.append(depth_risk)
        return depth_risk

    async def _calculate_liquidity_risk(self):
        """计算流动性风险 (基于Garman, 1976)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘流动性
        bid_liquidity = sum(size for _, size in self.orderbook['bids'])
        ask_liquidity = sum(size for _, size in self.orderbook['asks'])
        total_liquidity = bid_liquidity + ask_liquidity

        if total_liquidity == 0:
            return 1.0

        # 计算流动性风险
        liquidity_ratio = min(bid_liquidity, ask_liquidity) / total_liquidity
        liquidity_risk = 1.0 - self.liquidity_aversion * liquidity_ratio

        self.liquidity_history.append(liquidity_risk)
        return liquidity_risk

    async def _calculate_service_cost(self):
        """计算做市商服务成本 (基于Stoll, 1978，添加时间衰减，值域范围)"""
        if len(self.execution_history) < self.service_window:
            return self.service_cost

        # 获取最近的成交记录
        recent_executions = self.execution_history[-self.service_window:]
        service_costs = []

        # 计算时间衰减权重（使用较慢的衰减）
        time_weights = np.exp(-0.05 * np.arange(len(recent_executions)))
        time_weights = time_weights / np.sum(time_weights)

        for i, (_, price, _) in enumerate(recent_executions):
            if self.mid_price is not None:
                # 基础成本计算（只考虑价格偏离）
                base_cost = abs(price - self.mid_price) / self.mid_price
                
                # 应用时间衰减
                final_cost = base_cost * time_weights[i]
                
                service_costs.append(final_cost)

        if not service_costs:
            return self.service_cost

        # 使用加权平均计算最终服务成本
        weighted_avg_cost = np.average(service_costs, weights=time_weights)
        
        # 应用平滑处理（更保守的平滑）
        if len(self.service_history) > 0:
            last_cost = self.service_history[-1]
            smoothed_cost = 0.8 * last_cost + 0.2 * weighted_avg_cost
        else:
            smoothed_cost = weighted_avg_cost

        # 确保服务成本在更保守的范围内
        min_cost = self.service_cost * 0.7  # 最小不低于基础成本的70%
        max_cost = self.service_cost * 2  # 最大不超过基础成本的200%
        final_cost = max(min_cost, min(max_cost, smoothed_cost))

        self.service_history.append(final_cost)
        return final_cost

    async def _calculate_information_asymmetry(self):
        """计算信息不对称程度 (基于O'Hara, 1995)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘价差
        best_bid = self.orderbook['bids'][0][0]
        best_ask = self.orderbook['asks'][0][0]
        spread = best_ask - best_bid

        if self.mid_price is None:
            return 0.0

        # 计算信息不对称指标
        spread_ratio = spread / self.mid_price
        imbalance = await self._calculate_order_imbalance()

        # 结合价差和订单失衡计算信息不对称
        information_asymmetry = spread_ratio * (1 + abs(imbalance))
        self.information_history.append(information_asymmetry)

        return information_asymmetry

    async def _calculate_orderbook_dynamics(self):
        """计算订单簿动态 (基于Foucault et al., 2005)"""
        if len(self.orderbook_history) < self.orderbook_window:
            return 0.0

        # 计算订单簿变化率
        recent_changes = []
        for i in range(1, len(self.orderbook_history)):
            prev = self.orderbook_history[i - 1]
            curr = self.orderbook_history[i]
            change = abs(curr - prev) / (prev + 1e-8)
            recent_changes.append(change)

        if not recent_changes:
            return 0.0

        # 计算订单簿动态指标
        dynamics = np.mean(recent_changes)
        self.orderbook_history.append(dynamics)

        return dynamics

    async def _calculate_orderflow(self):
        """计算订单流 (基于Cont et al., 2010)"""
        if len(self.execution_history) < self.orderflow_window:
            return 0.0

        # 计算订单流指标
        recent_executions = self.execution_history[-self.orderflow_window:]
        orderflow = []

        for i in range(1, len(recent_executions)):
            prev_price = recent_executions[i - 1][1]
            curr_price = recent_executions[i][1]
            flow = (curr_price - prev_price) / prev_price
            orderflow.append(flow)

        if not orderflow:
            return 0.0

        # 计算订单流强度
        flow_strength = np.mean(orderflow)
        self.orderflow_history.append(flow_strength)

        return flow_strength

    async def _calculate_dynamic_risk(self):
        """计算动态风险 (基于Baldacci et al., 2021)"""
        if len(self.inventory_history) < self.dynamic_risk_window:
            return 0.0

        # 计算库存波动率
        recent_inventory = np.array(self.inventory_history[-self.dynamic_risk_window:])
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算价格波动率
        if len(self.price_history) >= self.dynamic_risk_window:
            recent_prices = np.array(self.price_history[-self.dynamic_risk_window:])
            price_volatility = np.std(recent_prices) / np.mean(recent_prices)
        else:
            price_volatility = 0.0

        # 计算综合风险
        risk = self.risk_sensitivity * (inventory_volatility + price_volatility)
        self.risk_history.append(risk)

        return risk

    async def _calculate_adaptation_factor(self):
        """计算自适应因子 (基于Cartea et al., 2021)"""
        if len(self.risk_history) < self.adaptation_threshold:
            return 1.0

        # 计算风险趋势
        recent_risk = np.array(self.risk_history[-self.adaptation_threshold:])
        risk_trend = np.mean(np.diff(recent_risk))

        # 计算自适应因子
        adaptation = 1.0 - self.adaptation_rate * abs(risk_trend)
        self.adaptation_history.append(adaptation)

        return max(0.5, min(1.5, adaptation))

    async def _calculate_inventory_balance(self):
        """计算库存平衡调整 (基于Guéant et al., 2012)"""
        if len(self.inventory_history) < 2:
            return 0.0

        # 计算累计库存
        cumulative_inventory = np.cumsum(self.inventory_history)
        current_cumulative = cumulative_inventory[-1] if len(cumulative_inventory) > 0 else 0

        # 计算库存偏离
        inventory_deviation = current_cumulative - self.inventory_target

        # 计算调整量
        adjustment = -self.inventory_speed * inventory_deviation

        # 应用惩罚因子
        if abs(inventory_deviation) > self.inventory_limit:
            adjustment *= (1 + self.inventory_penalty)

        return adjustment

    async def _calculate_spread_adjustment(self):
        """计算价差调整 (基于Avellaneda & Stoikov, 2008)"""
        if len(self.spread_history) < 2:
            return 0.0

        # 计算累计库存
        cumulative_inventory = np.cumsum(self.inventory_history)
        current_cumulative = cumulative_inventory[-1] if len(cumulative_inventory) > 0 else 0

        # 计算库存影响
        inventory_ratio = current_cumulative / self.max_inventory
        inventory_impact = self.spread_sensitivity * inventory_ratio

        # 计算风险影响
        risk_factor = await self._calculate_dynamic_risk()
        risk_impact = self.spread_adjustment * risk_factor

        # 计算总调整
        adjustment = inventory_impact + risk_impact
        self.spread_history.append(adjustment)

        return adjustment

    async def _calculate_orderflow_impact(self):
        """计算订单流影响 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderflow_history) < self.orderflow_window:
            return 0.0

        # 计算订单流趋势
        recent_flow = np.array(self.orderflow_history[-self.orderflow_window:])
        flow_trend = np.mean(recent_flow)

        # 计算影响因子
        impact = self.orderflow_sensitivity * flow_trend

        return impact

    async def _update_dynamic_limits(self):
        """更新动态限制 (综合多个模型)"""
        # 计算各项指标
        dynamic_risk = await self._calculate_dynamic_risk()
        adaptation_factor = await self._calculate_adaptation_factor()
        inventory_balance = await self._calculate_inventory_balance()
        spread_adjustment = await self._calculate_spread_adjustment()
        orderflow_impact = await self._calculate_orderflow_impact()
        trend_adjustment = await self._calculate_trend_inventory_adjustment()

        # 计算累计库存
        cumulative_inventory = np.cumsum(self.inventory_history)
        current_cumulative = cumulative_inventory[-1] if len(cumulative_inventory) > 0 else 0

        # 计算库存偏离
        inventory_deviation = abs(current_cumulative) / self.max_inventory

        # 如果库存偏离过大，强制调整
        if inventory_deviation > self.inventory_deviation_limit:
            # 计算需要调整的量
            adjustment = -np.sign(current_cumulative) * self.inventory_reversion_speed * self.max_inventory
            # 应用调整
            self.inventory += adjustment

        # 计算综合调整
        total_adjustment = (
                inventory_balance * adaptation_factor +
                spread_adjustment * (1 + dynamic_risk) +
                orderflow_impact +
                trend_adjustment  # 加入趋势调整因子
        )

        # 更新动态限制
        self.dynamic_inventory_limit = max(
            self.inventory_limit * 0.5,
            min(
                self.inventory_limit * 1.5,
                self.inventory_limit * (1 + total_adjustment)
            )
        )

        # 确保累计库存不超过限制
        if abs(current_cumulative) > self.dynamic_inventory_limit:
            self.inventory = np.sign(current_cumulative) * (self.dynamic_inventory_limit - abs(current_cumulative))

        # 更新历史数据
        self.risk_history.append(dynamic_risk)
        self.adaptation_history.append(adaptation_factor)
        self.spread_history.append(spread_adjustment)
        self.orderflow_history.append(orderflow_impact)

    async def _calculate_position_penalty(self):
        """计算仓位惩罚系数 (基于Avellaneda & Stoikov, 2008)"""
        # 计算累计仓位
        cumulative_inventory = np.cumsum(self.inventory_history)
        current_cumulative = cumulative_inventory[-1] if len(cumulative_inventory) > 0 else 0

        position_ratio = abs(current_cumulative) / (self.dynamic_inventory_limit + 1e-8)

        # 计算库存风险
        inventory_risk = await self._calculate_inventory_risk()

        # 计算执行风险
        execution_risk = await self._calculate_execution_risk()

        # 计算综合风险惩罚
        risk_penalty = (inventory_risk + execution_risk) * self.risk_aversion

        # 使用更陡峭的惩罚函数
        self.position_penalty = tanh(position_ratio ** 3) * self.risk_aversion * 3 + risk_penalty

        # 波动率调整
        if len(self.vol_history) > 5:
            vol_factor = np.mean(self.vol_history[-5:]) * 150
            self.position_penalty *= 1 + vol_factor

        # 接近限制时更快增加惩罚
        if position_ratio > 0.5:
            self.position_penalty *= 3

    async def estimate_volatility(self, recent_prices):
        """估计价格波动率"""
        return np.std(recent_prices) if len(recent_prices) >= 2 else 0.0

    async def update_mid_price(self, fill_price):
        """更新中间价"""
        self.mid_price = fill_price

    async def _calculate_order_size(self, base_size, level, is_bid):
        """计算订单量，确保不会出现0挂单量"""
        # 基础衰减
        decayed_size = base_size * (self.order_size_decay ** level)

        # 确保最小挂单量
        min_size = max(self.min_order_size, self.base_order_size)
        decayed_size = max(min_size, decayed_size)

        # 考虑市场深度影响
        if len(self.orderbook['bids']) > 0 and len(self.orderbook['asks']) > 0:
            total_depth = sum(size for _, size in self.orderbook['bids']) + sum(
                size for _, size in self.orderbook['asks'])
            depth_ratio = total_depth / (self.total_side_qty * 2)
            depth_adjustment = 1.0 + self.depth_factor * (1 - depth_ratio)
            decayed_size *= depth_adjustment

        # 考虑仓位影响
        inventory_ratio = abs(self.inventory) / self.max_inventory
        if inventory_ratio > 0.5:
            if (is_bid and self.inventory > 0) or (not is_bid and self.inventory < 0):
                decayed_size *= 0.5
            else:
                decayed_size *= 1.5

        # 确保在合理范围内
        decayed_size = min(max(decayed_size, min_size), self.max_order_size)

        return round(decayed_size, self.size_precision)

    async def generate_layered_quotes(self):
        """生成分层报价"""
        # 设置最低服务成本
        current_service_cost = await self._calculate_service_cost()
        self.service_cost = max(0.0001, current_service_cost)

        if self.mid_price is None:
            return [], []

        # 更新动态参数
        await self._update_dynamic_limits()
        await self._calculate_position_penalty()

        # 计算趋势调整因子
        trend_adjustment = await self._calculate_trend_inventory_adjustment()
        trend_price_adjust = abs(trend_adjustment) * 0.5  # 趋势对价格的影响因子
        trend_size_adjust = abs(trend_adjustment) * 0.3   # 趋势对订单量的影响因子

        # 计算各层权重
        levels = np.arange(1, self.num_levels + 1)
        weights = np.exp(-0.05 * levels)
        weights /= weights.sum()

        # 计算目标挂单量
        target_sizes = self.total_side_qty * weights
        bids = []
        asks = []

        # 生成各层报价
        for i in range(self.num_levels):
            # 计算报价价格
            service_cost_factor = 1 + self.service_cost
            
            # 根据趋势方向调整价格
            if self.trend_direction == 1:  # 上升趋势
                bid_price = self.mid_price * (1 - self.price_step_ratio * (i + 1) * service_cost_factor * (1 + trend_price_adjust))
                ask_price = self.mid_price * (1 + self.price_step_ratio * (i + 1) * service_cost_factor * (1 - trend_price_adjust))
            elif self.trend_direction == -1:  # 下降趋势
                bid_price = self.mid_price * (1 - self.price_step_ratio * (i + 1) * service_cost_factor * (1 - trend_price_adjust))
                ask_price = self.mid_price * (1 + self.price_step_ratio * (i + 1) * service_cost_factor * (1 + trend_price_adjust))
            else:  # 无趋势
                bid_price = self.mid_price * (1 - self.price_step_ratio * (i + 1) * service_cost_factor)
                ask_price = self.mid_price * (1 + self.price_step_ratio * (i + 1) * service_cost_factor)

            # 添加随机波动
            bid_random_factor = 1.0 + random.uniform(-0.001, 0.001)
            ask_random_factor = 1.0 + random.uniform(-0.001, 0.001)

            bid_price *= bid_random_factor
            ask_price *= ask_random_factor

            # 确保价格为正且合理
            min_price = self.mid_price * 0.5  # 最低价格为中间价的50%
            max_price = self.mid_price * 1.5  # 最高价格为中间价的150%

            bid_price = max(min_price, min(bid_price, self.mid_price))
            ask_price = min(max_price, max(ask_price, self.mid_price))

            # 价格精度处理
            bid_price = round(bid_price, self.price_precision)
            ask_price = round(ask_price, self.price_precision)

            # 确保价格单调性
            if i > 0:
                if bid_price >= bids[-1][0]:
                    bid_price = round(bids[-1][0] * 0.999, self.price_precision)
                if ask_price <= asks[-1][0]:
                    ask_price = round(asks[-1][0] * 1.001, self.price_precision)

            # 计算挂单量
            bid_size = await self._calculate_order_size(target_sizes[i], i, True)
            ask_size = await self._calculate_order_size(target_sizes[i], i, False)

            # 根据趋势调整订单量
            if self.trend_direction == 1:  # 上升趋势
                bid_size *= (1 - trend_size_adjust)  # 减少买单量
                ask_size *= (1 + trend_size_adjust)  # 增加卖单量
            elif self.trend_direction == -1:  # 下降趋势
                bid_size *= (1 + trend_size_adjust)  # 增加买单量
                ask_size *= (1 - trend_size_adjust)  # 减少卖单量

            # 添加随机波动，确保买卖档位量不完全相同
            bid_random_factor = 1.0 + random.uniform(-0.1, 0.1)
            ask_random_factor = 1.0 + random.uniform(-0.1, 0.1)

            # 确保随机因子不会导致订单量为0
            bid_random_factor = max(0.8, min(1.2, bid_random_factor))
            ask_random_factor = max(0.8, min(1.2, ask_random_factor))

            bid_size = round(bid_size * bid_random_factor, self.size_precision)
            ask_size = round(ask_size * ask_random_factor, self.size_precision)

            bids.append((bid_price, bid_size))
            asks.append((ask_price, ask_size))

        # 确保单边做市总量不超过total_side_qty
        total_bid = sum(s for _, s in bids)
        total_ask = sum(s for _, s in asks)

        # 计算调整比例
        bid_scale = min(1.0, self.total_side_qty / (total_bid + 1e-8))
        ask_scale = min(1.0, self.total_side_qty / (total_ask + 1e-8))

        # 应用调整
        bids = [(price, round(size * bid_scale, self.size_precision)) for price, size in bids]
        asks = [(price, round(size * ask_scale, self.size_precision)) for price, size in asks]

        # 再次检查总量
        total_bid = sum(s for _, s in bids)
        total_ask = sum(s for _, s in asks)

        # 如果调整后总量仍然超过限制，按比例缩减
        if total_bid > self.total_side_qty:
            scale = self.total_side_qty / (total_bid + 1e-8)
            bids = [(price, round(size * scale, self.size_precision)) for price, size in bids]

        if total_ask > self.total_side_qty:
            scale = self.total_side_qty / (total_ask + 1e-8)
            asks = [(price, round(size * scale, self.size_precision)) for price, size in asks]

        self.orderbook = {'bids': bids, 'asks': asks}
        return bids, asks

    async def _generate_trade_record(self, side, price, size, timestamp=None):
        """生成单条成交记录"""
        if timestamp is None:
            timestamp = time.time()

        self.trade_id += 1

        # 判断是maker还是taker
        is_maker = False
        if side == 'buy':  # 用户买入，做市商卖出
            for ask_price, ask_size in self.orderbook['asks']:
                if price >= ask_price:  # 如果成交价格高于或等于卖价，说明是taker
                    is_maker = True
                    break
        else:  # 用户卖出，做市商买入
            for bid_price, bid_size in self.orderbook['bids']:
                if price <= bid_price:  # 如果成交价格低于或等于买价，说明是taker
                    is_maker = True
                    break

        # 从做市商角度定义side和role
        maker_side = 'sell' if side == 'buy' else 'buy'  # 用户买入时做市商卖出，用户卖出时做市商买入
        maker_role = 'maker' if is_maker else 'taker'  # 做市商角色

        trade_record = {
            'trade_id': self.trade_id,
            'timestamp': timestamp,
            'side': maker_side,  # 从做市商角度定义方向
            'price': round(price, self.price_precision),
            'size': round(size, self.size_precision),
            'mid_price': round(self.mid_price, self.price_precision) if self.mid_price is not None else None,
            'inventory': round(self.inventory, self.size_precision),  # 使用当前实际仓位
            'pnl': round(self.pnl, self.price_precision),
            'role': maker_role,  # 从做市商角度定义角色
            'inventory_cost': round(self.inventory_cost, self.price_precision),
            # 添加原始数值字段用于验证
            '_raw_price': price,
            '_raw_size': size
        }
        return trade_record

    async def simulate_fill(self, market_price):
        """模拟市场成交"""
        # 更新历史数据
        if self.mid_price is not None:
            self.mid_price_history.append(self.mid_price)
            self.price_history.append(self.mid_price)

        self.recent_prices.append(market_price)
        if len(self.recent_prices) > 50:
            self.recent_prices.pop(0)

        # 更新趋势分析
        await self._update_trend_analysis()

        volatility = await self.estimate_volatility(self.recent_prices)
        self.vol_history.append(volatility)

        # 记录当前状态 - 确保所有历史数据同步记录
        self.pnl_history.append(self.pnl)
        self.inventory_history.append(self.inventory)

        # 生成报价并更新订单簿
        bids, asks = await self.generate_layered_quotes()
        self.orderbook = {'bids': bids, 'asks': asks}
        self.execution_history.append((time.time(), market_price, self.inventory))

        # 计算市场深度
        market_depth = {
            'bid': sum(size for _, size in self.orderbook['bids']),
            'ask': sum(size for _, size in self.orderbook['asks'])
        }

        # 计算成交概率
        base_fill_prob = 0.5
        volatility = await self.estimate_volatility(self.recent_prices[-10:]) if len(self.recent_prices) >= 10 else 0.0
        vol_adjust = 1.0 + min(volatility * 100, 1.0)

        # 处理成交
        executions = await self._process_executions(market_price, market_depth, base_fill_prob, vol_adjust)

        if not executions:
            self.last_unfilled_quotes = {'bids': bids, 'asks': asks}
            return

        # 处理成交结果
        for side, qty, fill_price in executions:
            # 先更新仓位和盈亏
            if side == 'buy':  # 用户买入，做市商卖出
                await self._process_sell_execution(qty, fill_price)
            elif side == 'sell':  # 用户卖出，做市商买入
                await self._process_buy_execution(qty, fill_price)

            # 更新中间价
            await self._update_mid_price(fill_price)
            self.last_fill_price = fill_price

            # 生成成交记录（在更新仓位和盈亏之后）
            trade_record = await self._generate_trade_record(side, fill_price, qty)
            self.trade_history.append(trade_record)

    async def _process_executions(self, market_price, market_depth, base_fill_prob, vol_adjust):
        """处理成交逻辑"""
        buy_candidates = []
        sell_candidates = []

        # 处理买单成交
        for bid_price, bid_size in self.orderbook['bids']:
            if market_price <= bid_price and bid_size > 0:
                if await self._should_fill(bid_price, market_price, market_depth['bid'], base_fill_prob, vol_adjust):
                    fill_price = await self._calculate_fill_price(bid_price, 'bid')
                    sell_candidates.append(
                        ('sell', await self._calculate_execution_size(bid_size, fill_price, market_price), fill_price))

        # 处理卖单成交
        for ask_price, ask_size in self.orderbook['asks']:
            if market_price >= ask_price and ask_size > 0:
                if await self._should_fill(ask_price, market_price, market_depth['ask'], base_fill_prob, vol_adjust):
                    fill_price = await self._calculate_fill_price(ask_price, 'ask')
                    buy_candidates.append(
                        ('buy', await self._calculate_execution_size(ask_size, fill_price, market_price), fill_price))

        # 选择成交
        max_executions = min(3, max(1, int(5)))
        executions = []

        if buy_candidates and sell_candidates:
            buy_exec = random.choice(buy_candidates)
            sell_exec = random.choice(sell_candidates)
            executions.extend([buy_exec, sell_exec])
        else:
            candidates = buy_candidates + sell_candidates
            if candidates:
                executions = random.sample(candidates, min(len(candidates), max_executions))

        return executions

    async def _should_fill(self, order_price, market_price, depth, base_fill_prob, vol_adjust):
        """判断是否应该成交"""
        price_diff = abs(order_price - market_price) / market_price
        depth_ratio = depth / (self.total_side_qty + 1e-8)
        inventory_ratio = self.inventory / self.max_inventory

        if abs(inventory_ratio) > 0.2:
            position_adjust = 0.5 if (inventory_ratio > 0.2 and order_price < market_price) or \
                                     (inventory_ratio < -0.2 and order_price > market_price) else 1.5
        else:
            position_adjust = 1.0

        fill_prob = base_fill_prob * np.exp(-price_diff * 100) * vol_adjust * (1 - depth_ratio) * position_adjust
        return random.random() < fill_prob

    async def _calculate_fill_price(self, order_price, side):
        """计算成交价格"""
        slippage = (0.5 + random.uniform(0, 0.5)) * (10 ** -self.price_precision)
        return order_price - slippage if side == 'bid' else order_price + slippage

    async def _calculate_execution_size(self, order_size, fill_price, market_price):
        """计算成交数量"""
        base_qty = random.uniform(50, 50000)
        price_diff = abs(fill_price - market_price) / market_price
        size_factor = 1.0 - price_diff * 2
        return max(10.0, min(base_qty * size_factor, order_size))

    async def _update_orderbook_state(self, executions):
        """更新订单簿状态"""
        self.last_unfilled_quotes = self.orderbook.copy()
        current_bids = set(round(price, self.price_precision) for price, _ in self.orderbook['bids'])
        current_asks = set(round(price, self.price_precision) for price, _ in self.orderbook['asks'])
        all_prices = current_bids.union(current_asks)

        for p in all_prices:
            self.unfilled_quote_counter[p] = self.unfilled_quote_counter.get(p, 0) + 1

        for side, _, fill_price in executions:
            fill_price_rounded = round(fill_price, self.price_precision)
            if fill_price_rounded in self.unfilled_quote_counter:
                del self.unfilled_quote_counter[fill_price_rounded]

    async def _process_sell_execution(self, qty, fill_price):
        """处理卖出成交（做市商卖出）"""
        # 先更新仓位
        self.inventory = round(self.inventory - qty, 8)
        
        # 计算已实现盈亏
        remaining_qty = round(qty, 8)  # 确保高精度处理
        realized_pnl = 0.0
        while remaining_qty > 1e-8 and self.inventory_lots:  # 使用精确比较
            lot_qty, lot_cost = self.inventory_lots[0]
            lot_qty = round(lot_qty, 8)
            if lot_qty > remaining_qty:
                realized_pnl += round(remaining_qty * (fill_price - lot_cost), 8)
                new_lot_qty = round(lot_qty - remaining_qty, 8)
                if new_lot_qty < 1e-8:  # 严格截断极小值
                    self.inventory_lots.pop(0)
                else:
                    self.inventory_lots[0] = (new_lot_qty, lot_cost)
                remaining_qty = 0
            else:
                realized_pnl += round(lot_qty * (fill_price - lot_cost), 8)
                remaining_qty = round(remaining_qty - lot_qty, 8)
                self.inventory_lots.pop(0)
        
        # 更新盈亏
        self.pnl = round(self.pnl + realized_pnl, 8)
        
        # 更新库存成本
        total_qty = round(sum(round(q, 8) for q, _ in self.inventory_lots), 8)
        total_cost = round(sum(round(q * p, 8) for q, p in self.inventory_lots), 8)
        self.inventory_cost = round(total_cost / total_qty, 8) if total_qty != 0 else 0.0

    async def _process_buy_execution(self, qty, fill_price):
        """处理买入成交（做市商买入）"""
        # 先更新仓位
        self.inventory = round(self.inventory + qty, 8)
        
        # 记录当前批次
        self.inventory_lots.append((qty, fill_price))
        
        # 更新库存成本
        total_qty = sum(q for q, _ in self.inventory_lots)
        total_cost = sum(q * p for q, p in self.inventory_lots)
        self.inventory_cost = total_cost / total_qty if total_qty != 0 else 0.0

    async def _update_mid_price(self, fill_price):
        """更新中间价"""
        if self.orderbook['bids'] and self.orderbook['asks']:
            best_bid = self.orderbook['bids'][0][0]
            best_ask = self.orderbook['asks'][0][0]
            book_mid = (best_bid + best_ask) / 2
            combined_mid = 0.3 * fill_price + 0.7 * book_mid
            self.mid_price = round(combined_mid, self.price_precision)  # 最终四舍五入
        else:
            self.mid_price = round(fill_price, self.price_precision)

    async def _print_execution_details(self, executions):
        """打印成交详情"""
        for side, qty, fill_price in executions:
            direction = "买入" if side == "buy" else "卖出"
            print(f"模拟市价单: {direction} | 数量: {qty:.2f} | 价格: {fill_price:.4f}")

    async def show_quotes(self, top_n=50):
        """显示订单簿"""
        bids, asks = await self.generate_layered_quotes()
        bid_data = bids[:top_n]
        ask_data = asks[:top_n]

        bid_data = sorted(bid_data, key=lambda x: -x[0])
        ask_data = sorted(ask_data, key=lambda x: x[0])

        print(f"\n{'买价':>12} {'买量':>10} | {'卖价':>10} {'卖量':>10}")
        print("-" * 48)

        for i in range(top_n):
            bid_price, bid_size = bid_data[i] if i < len(bid_data) else ('', '')
            ask_price, ask_size = ask_data[i] if i < len(ask_data) else ('', '')

            bid_price_str = f"{bid_price:.4f}" if bid_price != '' else ''
            bid_size_str = f"{bid_size:.3f}" if bid_size != '' else ''
            ask_price_str = f"{ask_price:.4f}" if ask_price != '' else ''
            ask_size_str = f"{ask_size:.3f}" if ask_size != '' else ''

            print(f"{bid_price_str:>12} {bid_size_str:>10} | {ask_price_str:>10} {ask_size_str:>10}")

    async def show_pnl_and_inventory(self):
        """显示做市商盈亏和仓位历史"""
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # 设置中文字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        # 创建4个子图
        fig = plt.figure(figsize=(15, 12))
        gs = GridSpec(4, 1, height_ratios=[2, 1, 1, 1])

        # 1. 盈亏分析图
        ax1 = fig.add_subplot(gs[0])

        # 只使用已实现盈亏
        realized_pnl = self.pnl_history  # 已实现盈亏

        # 确保所有历史数据长度一致
        min_length = min(len(self.pnl_history), len(self.inventory_history),
                         len(self.mid_price_history), len(self.vol_history))

        print(f"数据长度检查: pnl={len(self.pnl_history)}, inv={len(self.inventory_history)}, "
              f"mid={len(self.mid_price_history)}, vol={len(self.vol_history)}")

        # 如果发现数据长度不一致，打印警告
        if len(self.inventory_history) != len(self.pnl_history):
            print(
                f"警告：库存历史数据长度({len(self.inventory_history)})与其他历史数据长度({len(self.pnl_history)})不一致！")
            # 使用最短长度作为显示范围
            min_length = min(len(self.pnl_history), len(self.inventory_history),
                             len(self.mid_price_history), len(self.vol_history))

        # 使用实际数据长度作为x轴
        x_axis = np.arange(min_length)

        # 绘制盈亏曲线
        ax1.plot(x_axis, realized_pnl[:min_length], color='tab:blue', label="已实现盈亏")
        ax1.set_title("做市商盈亏分析")
        ax1.set_xlabel("迭代次数")
        ax1.set_ylabel("盈亏")
        ax1.grid(True)
        ax1.legend()

        # 2. 实时仓位
        ax2 = fig.add_subplot(gs[1])
        # 计算实时仓位
        ax2.plot(x_axis, self.inventory_history[:min_length], color='tab:red', label="做市商实时仓位")
        # 添加仓位限制线
        ax2.axhline(y=self.max_inventory, color='r', linestyle='--', alpha=0.3, label="最大多头仓位")
        ax2.axhline(y=-self.max_inventory, color='r', linestyle='--', alpha=0.3, label="最大空头仓位")
        ax2.set_title("做市商仓位变化")
        ax2.set_xlabel("迭代次数")
        ax2.set_ylabel("实时仓位")
        ax2.grid(True)
        ax2.legend()

        # 3. 价格走势
        ax3 = fig.add_subplot(gs[2])
        ax3.plot(x_axis, self.mid_price_history[:min_length], color='tab:purple', label="中间价")
        ax3.set_title("市场价格走势")
        ax3.set_xlabel("迭代次数")
        ax3.set_ylabel("价格")
        ax3.grid(True)
        ax3.legend()

        # 4. 波动率变化
        ax4 = fig.add_subplot(gs[3])
        ax4.plot(x_axis, self.vol_history[:min_length], color='tab:brown', label="波动率")
        ax4.set_title("市场波动率变化")
        ax4.set_xlabel("迭代次数")
        ax4.set_ylabel("波动率")
        ax4.grid(True)
        ax4.legend()

        # 设置x轴范围
        ax1.set_xlim(0, min_length)
        ax2.set_xlim(0, min_length)
        ax3.set_xlim(0, min_length)
        ax4.set_xlim(0, min_length)

        # 计算最终盈亏 - 只显示已实现盈亏
        final_realized_pnl = realized_pnl[-1] if len(realized_pnl) > 0 else 0

        # 打印详细的盈亏信息
        print("\n盈亏计算详情:")
        print(f"累计已实现盈亏: {final_realized_pnl}")
        print(f"当前仓位: {self.inventory}")
        print(f"当前中间价: {self.mid_price if self.mid_price is not None else 0}")
        print(f"当前库存成本: {self.inventory_cost}")

        stats_text = f"总迭代次数: {min_length}\n" \
                     f"最终仓位: {self.inventory:.2f}\n" \
                     f"已实现盈亏: {final_realized_pnl:.2f}"

        plt.figtext(0.02, 0.02, stats_text, fontsize=10, bbox=dict(facecolor='white', alpha=0.5))

        # 调整布局以适应更多数据点
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)  # 为统计信息留出更多空间

        # 保存图表
        plt.savefig('market_maker_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    async def show_trade_history(self, limit=20):
        """显示最近的成交记录"""
        if not self.trade_history:
            print("暂无成交记录")
            return

        print("\n=== 最近成交记录 ===")
        print(
            f"{'成交ID':<8} {'时间':<15} {'方向':<6} {'角色':<6} {'价格':<10} {'数量':<10} {'中间价':<10} {'仓位':<10} {'盈亏':<10}")
        print("-" * 100)

        for trade in self.trade_history[-limit:]:
            print(f"{trade['trade_id']:<8} "
                  f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(trade['timestamp'])):<15} "
                  f"{trade['side']:<6} "
                  f"{trade['role']:<6} "
                  f"{trade['price']:<10.4f} "
                  f"{trade['size']:<10.3f} "
                  f"{trade['mid_price']:<10.4f} "
                  f"{trade['inventory']:<10.3f} "
                  f"{trade['pnl']:<10.4f}")

        print("\n成交统计:")
        total_trades = len(self.trade_history)
        buy_trades = [t for t in self.trade_history if t['side'] == 'buy']
        sell_trades = [t for t in self.trade_history if t['side'] == 'sell']
        maker_trades = [t for t in self.trade_history if t['role'] == 'maker']
        taker_trades = [t for t in self.trade_history if t['role'] == 'taker']

        # 计算买入统计
        buy_count = len(buy_trades)
        buy_volume = sum(t['size'] for t in buy_trades)
        buy_avg_price = sum(t['price'] * t['size'] for t in buy_trades) / buy_volume if buy_volume > 0 else 0

        # 计算卖出统计
        sell_count = len(sell_trades)
        sell_volume = sum(t['size'] for t in sell_trades)
        sell_avg_price = sum(t['price'] * t['size'] for t in sell_trades) / sell_volume if sell_volume > 0 else 0

        # 计算maker/taker统计
        maker_count = len(maker_trades)
        taker_count = len(taker_trades)
        maker_volume = sum(t['size'] for t in maker_trades)
        taker_volume = sum(t['size'] for t in taker_trades)

        # 计算总体统计
        total_volume = buy_volume + sell_volume
        avg_price = sum(t['price'] * t['size'] for t in self.trade_history) / total_volume if total_volume > 0 else 0

        print(f"总成交笔数: {total_trades}")
        print(f"买入笔数: {buy_count}")
        print(f"卖出笔数: {sell_count}")
        print(f"Maker笔数: {maker_count}")
        print(f"Taker笔数: {taker_count}")
        print(f"买入成交量: {buy_volume:.3f}")
        print(f"卖出成交量: {sell_volume:.3f}")
        print(f"Maker成交量: {maker_volume:.3f}")
        print(f"Taker成交量: {taker_volume:.3f}")
        print(f"总成交量: {total_volume:.3f}")
        print(f"买入均价: {buy_avg_price:.4f}")
        print(f"卖出均价: {sell_avg_price:.4f}")
        print(f"总体均价: {avg_price:.4f}")
        print(f"最大成交量: {max(t['size'] for t in self.trade_history):.3f}")
        print(f"最小成交量: {min(t['size'] for t in self.trade_history):.3f}")
        print(f"最高成交价: {max(t['price'] for t in self.trade_history):.4f}")
        print(f"最低成交价: {min(t['price'] for t in self.trade_history):.4f}")


    async def _update_trend_analysis(self):
        """更新趋势分析"""
        if len(self.recent_prices) < self.trend_window:
            return

        # 计算移动平均 (使用指数加权移动平均)
        prices = np.array(self.recent_prices[-self.trend_window:])
        ma = np.mean(prices)
        self.price_ma.append(ma)

        # 计算价格斜率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.slope_window:
            recent_prices = np.array(self.recent_prices[-self.slope_window:])
            x = np.arange(len(recent_prices))
            slope, _ = np.polyfit(x, recent_prices, 1)
            self.price_slope = slope / recent_prices[0]  # 归一化斜率

        # 计算波动率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.volatility_window:
            recent_prices = np.array(self.recent_prices[-self.volatility_window:])
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            self.volatility_ma.append(volatility)

        # 判断趋势方向 (更严格的判断条件)
        if len(self.volatility_ma) > 0:
            current_vol = self.volatility_ma[-1]
            if self.price_slope > self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = 1
                self.trend_strength = min(1.0, self.price_slope / self.slope_threshold)
            elif self.price_slope < -self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = -1
                self.trend_strength = min(1.0, abs(self.price_slope) / self.slope_threshold)
            else:
                self.trend_direction = 0
                self.trend_strength = 0.0

    async def _calculate_trend_inventory_adjustment(self):
        """计算基于趋势的库存调整"""
        if self.trend_direction == 0 or self.mid_price is None or len(self.volatility_ma) == 0:
            return 0.0

        # 计算基础调整量 (考虑波动率影响)
        current_vol = self.volatility_ma[-1]
        vol_factor = max(0.3, 1 - current_vol / self.volatility_threshold)  # 更保守的波动率调整
        base_adjustment = self.trend_inventory_factor * self.trend_strength * vol_factor

        # 根据趋势方向和波动率调整
        if self.trend_direction == 1:  # 上升趋势
            # 在低波动率且斜率较大时增加库存
            if current_vol < self.volatility_threshold and self.price_slope > self.slope_threshold:
                return base_adjustment * 1.1  # 更保守的调整
            else:
                return base_adjustment
        else:  # 下降趋势
            # 在低波动率且斜率较大时减少库存
            if current_vol < self.volatility_threshold and self.price_slope < -self.slope_threshold:
                return -base_adjustment * 1.1  # 更保守的调整
            else:
                return -base_adjustment

    async def export_trade_history(self, filename='trade_history.csv'):
        """导出交易记录到CSV文件"""
        if not self.trade_history:
            print("暂无成交记录可导出")
            return

        # 将成交记录转换为DataFrame
        df = pd.DataFrame(self.trade_history)

        # 格式化时间戳为可读格式
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

        # 重命名列以更友好
        column_names = {
            'trade_id': '成交ID',
            'timestamp': '成交时间',
            'side': '方向',
            'price': '成交价格',
            'size': '成交数量',
            'mid_price': '中间价',
            'inventory': '当前仓位',
            'pnl': '当前盈亏',
            'role': '角色',
            'inventory_cost': '库存成本'
        }
        df = df.rename(columns=column_names)

        # 设置列的顺序
        columns_order = ['成交ID', '成交时间', '方向', '成交价格', '成交数量',
                         '中间价', '当前仓位', '当前盈亏', '角色', '库存成本']
        df = df[columns_order]

        # 导出到CSV文件
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"成交记录已导出到文件: {filename}")

        # 打印基本统计信息
        print("\n交易统计信息:")
        print(f"总交易笔数: {len(df)}")
        print(f"买入笔数: {len(df[df['方向'] == 'buy'])}")
        print(f"卖出笔数: {len(df[df['方向'] == 'sell'])}")
        print(f"Maker笔数: {len(df[df['角色'] == 'maker'])}")
        print(f"Taker笔数: {len(df[df['角色'] == 'taker'])}")
        print(f"平均成交价格: {df['成交价格'].mean():.4f}")
        print(f"平均成交数量: {df['成交数量'].mean():.3f}")
        print(f"最大成交数量: {df['成交数量'].max():.3f}")
        print(f"最小成交数量: {df['成交数量'].min():.3f}")


async def main():
    # 初始化做市商
    mm = BybMarketMaker(
        base_order_size=0.001,
        num_levels=50,
        price_step_ratio=0.001,
        inventory_limit=200000.0,
        max_inventory=500000.0,
        price_precision=4,
        size_precision=3,
        risk_aversion=0.5,
        max_leverage=10.0,
        adapt_window=200,
        total_side_qty=2500000
    )

    # 设置初始价格
    initial_price = 0.1
    await mm.update_mid_price(initial_price)

    # 设置模拟参数
    num_iterations = 5000
    up_ratio = 0.5
    down_ratio = 0.3

    # 生成趋势序列
    all_indices = list(range(num_iterations))
    random.shuffle(all_indices)
    up_trend_range = set(all_indices[:int(num_iterations * up_ratio)])
    down_trend_range = set(all_indices[int(num_iterations * up_ratio):int(num_iterations * (up_ratio + down_ratio))])

    # 价格趋势参数
    trend_strength = 0.0002  # 趋势强度
    mean_reversion = 0.0001  # 均值回归强度
    volatility = 0.0005  # 基础波动率
    last_price = initial_price

    # 开始模拟
    for i in range(num_iterations):
        # 计算趋势方向
        if i in up_trend_range:
            trend_direction = 1
        elif i in down_trend_range:
            trend_direction = -1
        else:
            trend_direction = 0

        # 计算价格变化
        trend_component = trend_direction * trend_strength
        mean_reversion_component = mean_reversion * (initial_price - last_price)
        random_component = random.gauss(0, volatility)

        # 计算新价格
        price_change = trend_component + mean_reversion_component + random_component
        simulated_market_price = last_price + price_change

        # 确保价格为正
        simulated_market_price = max(0.0001, simulated_market_price)

        # 更新价格
        last_price = simulated_market_price

        # 模拟成交
        await mm.simulate_fill(simulated_market_price)

        # 每100次迭代显示一次状态
        if (i + 1) % 100 == 0:
            print(f"\n--- 迭代 {i + 1} ---")
            print(f"当前已实现盈亏: {mm.pnl:.4f}")
            await mm.show_quotes(top_n=50)
            print("\n" + "=" * 40 + "\n")

    # 显示最终结果
    print(f"\n模拟完成，共进行 {num_iterations} 次迭代")
    print(f"最终仓位: {mm.inventory:.2f}")
    print(f"最终盈亏: {mm.pnl:.2f}")
    await mm.show_pnl_and_inventory()
    await mm.show_trade_history()
    await mm.export_trade_history()


if __name__ == "__main__":
    asyncio.run(main())