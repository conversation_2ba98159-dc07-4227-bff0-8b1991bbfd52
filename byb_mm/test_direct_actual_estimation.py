#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接使用实际订单数量计算间隔的简化流程
"""

import sys
import os
import random
import time
from datetime import datetime, timedelta
import pytz

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm, get_trading_config, get_current_symbol

def test_direct_actual_estimation():
    """测试直接使用实际订单数量计算间隔"""
    print("=" * 80)
    print("直接使用实际订单数量计算间隔测试".center(80))
    print("=" * 80)
    
    # 创建算法实例
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数组合
    test_cases = [
        {"days": 1, "total_amount": 5000, "each_amount": 500, "name": "小额短期"},
        {"days": 3, "total_amount": 15000, "each_amount": 1000, "name": "中等中期"},
        {"days": 7, "total_amount": 30000, "each_amount": 2000, "name": "大额长期"},
        {"days": 2, "total_amount": 8000, "each_amount": None, "name": "默认配置"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试用例 {i}: {case['name']}")
        print(f"{'='*60}")
        print(f"时间范围: {case['days']} 天")
        print(f"总金额: {case['total_amount']:,.2f}")
        print(f"单次最大: {case['each_amount'] if case['each_amount'] else '默认配置'}")
        
        try:
            # 生成订单计划
            schedule = algorithm.generate_order_schedule(
                case['days'], 
                case['total_amount'], 
                case['each_amount']
            )
            
            print(f"\n结果分析:")
            print(f"  实际生成订单数: {len(schedule)}")
            
            # 分析时间间隔
            if len(schedule) > 1:
                intervals = []
                for j in range(1, min(10, len(schedule))):  # 只分析前10个订单
                    prev_time = schedule[j-1]['scheduled_time']
                    curr_time = schedule[j]['scheduled_time']
                    interval = (curr_time - prev_time).total_seconds()
                    intervals.append(interval)
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    min_interval = min(intervals)
                    max_interval = max(intervals)
                    
                    print(f"  平均间隔: {avg_interval/60:.1f} 分钟")
                    print(f"  最小间隔: {min_interval/60:.1f} 分钟")
                    print(f"  最大间隔: {max_interval/60:.1f} 分钟")
                    print(f"  间隔范围: {min_interval/60:.1f} - {max_interval/60:.1f} 分钟")
                    
                    # 计算间隔的变异系数
                    variance = sum((x - avg_interval) ** 2 for x in intervals) / len(intervals)
                    std_dev = variance ** 0.5
                    cv = (std_dev / avg_interval) * 100
                    print(f"  间隔标准差: {std_dev/60:.1f} 分钟")
                    print(f"  变异系数: {cv:.1f}%")
            
            # 分析订单金额分布
            amounts = [order['amount'] for order in schedule]
            total_generated = sum(amounts)
            avg_amount = total_generated / len(schedule)
            
            print(f"  总生成金额: {total_generated:,.2f}")
            print(f"  平均订单金额: {avg_amount:,.2f}")
            print(f"  最小订单金额: {min(amounts):,.2f}")
            print(f"  最大订单金额: {max(amounts):,.2f}")
            
            # 验证金额匹配度
            amount_diff = abs(total_generated - case['total_amount'])
            amount_diff_ratio = amount_diff / case['total_amount'] * 100
            print(f"  金额匹配度: {amount_diff_ratio:.2f}% 差异")
            
            if amount_diff_ratio < 1:
                print(f"  ✅ 金额匹配良好")
            elif amount_diff_ratio < 5:
                print(f"  ⚠️  金额匹配可接受")
            else:
                print(f"  ❌ 金额匹配偏差较大")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 80)

def test_interval_optimization_comparison():
    """测试间隔优化效果对比"""
    print("\n间隔优化效果对比测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 3
    total_amount = 15000
    each_amount = 1000
    
    print(f"测试参数:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    
    try:
        # 生成订单计划
        schedule = algorithm.generate_order_schedule(days, total_amount, each_amount)
        
        print(f"\n生成的订单计划:")
        print(f"  总订单数: {len(schedule)}")
        
        # 分析时间分布
        if len(schedule) > 1:
            intervals = []
            for i in range(1, len(schedule)):
                prev_time = schedule[i-1]['scheduled_time']
                curr_time = schedule[i]['scheduled_time']
                interval = (curr_time - prev_time).total_seconds()
                intervals.append(interval)
            
            print(f"\n时间间隔分析:")
            print(f"  总间隔数: {len(intervals)}")
            print(f"  平均间隔: {sum(intervals)/len(intervals)/60:.1f} 分钟")
            print(f"  最小间隔: {min(intervals)/60:.1f} 分钟")
            print(f"  最大间隔: {max(intervals)/60:.1f} 分钟")
            
            # 计算间隔分布
            short_intervals = [i for i in intervals if i < 3600]  # 小于1小时
            medium_intervals = [i for i in intervals if 3600 <= i < 7200]  # 1-2小时
            long_intervals = [i for i in intervals if i >= 7200]  # 大于2小时
            
            print(f"\n间隔分布:")
            print(f"  短间隔 (<1小时): {len(short_intervals)} 个 ({len(short_intervals)/len(intervals)*100:.1f}%)")
            print(f"  中等间隔 (1-2小时): {len(medium_intervals)} 个 ({len(medium_intervals)/len(intervals)*100:.1f}%)")
            print(f"  长间隔 (>2小时): {len(long_intervals)} 个 ({len(long_intervals)/len(intervals)*100:.1f}%)")
            
            # 检查间隔的合理性
            total_time = days * 24 * 3600  # 总时间（秒）
            theoretical_avg_interval = total_time / len(schedule)
            actual_avg_interval = sum(intervals) / len(intervals)
            
            interval_ratio = actual_avg_interval / theoretical_avg_interval
            print(f"\n间隔合理性:")
            print(f"  理论平均间隔: {theoretical_avg_interval/60:.1f} 分钟")
            print(f"  实际平均间隔: {actual_avg_interval/60:.1f} 分钟")
            print(f"  间隔比例: {interval_ratio:.2f}")
            
            if 0.5 <= interval_ratio <= 2.0:
                print(f"  ✅ 间隔分布合理")
            else:
                print(f"  ⚠️  间隔分布需要调整")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 80)

def test_performance_comparison():
    """测试性能对比"""
    print("\n性能对比测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 2
    total_amount = 8000
    each_amount = 800
    
    print(f"测试参数:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    
    # 多次运行测试性能
    run_times = []
    order_counts = []
    
    for run in range(5):
        print(f"\n运行 {run + 1}:")
        
        try:
            start_time = time.time()
            schedule = algorithm.generate_order_schedule(days, total_amount, each_amount)
            end_time = time.time()
            
            execution_time = (end_time - start_time) * 1000  # 转换为毫秒
            order_count = len(schedule)
            
            run_times.append(execution_time)
            order_counts.append(order_count)
            
            print(f"  执行时间: {execution_time:.2f} 毫秒")
            print(f"  生成订单数: {order_count}")
            
        except Exception as e:
            print(f"  ❌ 运行失败: {str(e)}")
    
    # 统计分析
    if run_times:
        print(f"\n性能统计:")
        print(f"  平均执行时间: {sum(run_times)/len(run_times):.2f} 毫秒")
        print(f"  最快执行时间: {min(run_times):.2f} 毫秒")
        print(f"  最慢执行时间: {max(run_times):.2f} 毫秒")
        
        if order_counts:
            print(f"  平均订单数: {sum(order_counts)/len(order_counts):.1f}")
            print(f"  订单数标准差: {((sum((x - sum(order_counts)/len(order_counts))**2 for x in order_counts) / len(order_counts))**0.5):.1f}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    print("开始测试直接使用实际订单数量计算间隔的简化流程...")
    print(f"当前时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前交易对: {get_current_symbol()}")
    
    # 运行测试
    test_direct_actual_estimation()
    test_interval_optimization_comparison()
    test_performance_comparison()
    
    print("\n测试完成！") 