import pandas as pd
import pymysql
import numpy as np
import random
import time
import asyncio
import logging
import traceback
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import pytz
import sys
sys.path.append(r'/home/<USER>/byb_mm/')
import backoff
import aiohttp

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri

# ==================== 全局交易对配置 ====================
def get_current_symbol():
    """获取当前交易对 - 在这里修改交易对"""
    return "bybusdt"  # 修改这里来切换交易对: bybusdt, manausdt, btcusdt 等

def get_base_currency():
    """获取基础货币"""
    symbol = get_current_symbol()
    if symbol == "bybusdt":
        return "byb"
    elif symbol == "ongusdt":
        return "ong"
    elif symbol == "btcusdt":
        return "btc"
    elif symbol == "ethusdt":
        return "eth"
    else:
        # 自动解析：去掉usdt后缀
        if symbol.endswith("usdt"):
            return symbol[:-4]
        return "unknown"

def get_quote_currency():
    """获取计价货币"""
    symbol = get_current_symbol()
    if symbol.endswith("usdt"):
        return "usdt"
    elif symbol.endswith("btc"):
        return "btc"
    elif symbol.endswith("eth"):
        return "eth"
    else:
        return "usdt"  # 默认USDT

def get_symbol_config():
    """获取当前交易对的配置"""
    symbol = get_current_symbol()
    base = get_base_currency()
    quote = get_quote_currency()

    # 根据不同交易对返回不同配置
    configs = {
        "bybusdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        },
        "ongusdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        }
    }

    return configs.get(symbol, {
        "min_order_size": 1.0,
        "max_order_size": 100.0,
        "default_price": 1.0
    })
# ==================== 全局交易对配置结束 ====================

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_order_algorithm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接信息
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}

# 交易配置
def get_trading_config():
    """获取当前交易对的交易配置"""
    symbol_config = get_symbol_config()
    return {
        "symbol": get_current_symbol(),
        "side": "BUY",        # 买单
        "type": 2,            # 市价单
        "min_order_size": symbol_config["min_order_size"],
        "max_order_size": symbol_config["max_order_size"],
        "min_interval": 60,     # 最小下单间隔（秒）
        "max_interval": 3600,   # 最大下单间隔（秒）
    }


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB下单算法\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='info'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            task = loop.create_task(send_lark(msg, level))
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class BYBOrderAlgorithm:
    """BYB智能下单算法类"""
    
    def __init__(self):
        """初始化下单算法"""
        # base_url = "https://openapi.100exdemo.com"
        self.spot_market = Spot()
        self.spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
        self.db_config = DB_CONFIG

        # 算法状态
        self.is_running = False
        self.total_executed = 0
        self.orders_executed = 0
        self.start_time = None
        self.end_time = None

        # 订单计划
        self.order_schedule = []
        self.execution_log = []

        # 获取当前交易对信息
        current_symbol = get_current_symbol()
        base_currency = get_base_currency()
        logging.info(f"智能下单算法初始化完成 - 交易对: {current_symbol}, 基础货币: {base_currency}")
    
    def get_buy_back_params(self) -> Dict:
        """从数据库获取回购参数"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM buy_back_params ORDER BY created_at DESC LIMIT 1;")
                result = cursor.fetchone()
                if result:
                    params = {
                        'id': result[0],
                        'days': float(result[1]),
                        'total_amount': float(result[2]),
                        'each_amount': float(result[3]),
                        'created_at': result[4],
                        'status': result[5]
                    }
                    logging.info(f"获取数据库参数: {params}")
                    return params
                else:
                    raise ValueError("数据库中没有找到回购参数")
        except Exception as e:
            logging.error(f"获取数据库参数失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def get_current_price(self) -> float:
        """获取当前交易对价格（计价货币计价）"""
        try:
            current_symbol = get_current_symbol()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()
            symbol_config = get_symbol_config()
            default_price = symbol_config["default_price"]

            # 方法1: 尝试从get_trade_price获取最新成交价
            try:
                trade_prices_df = self.spot_market.get_trade_price()
                symbol_data = trade_prices_df[trade_prices_df['symbol'] == current_symbol]
                if not symbol_data.empty:
                    price = float(symbol_data['price'].iloc[0])
                    logging.info(f"从成交价获取{base_currency.upper()}价格: {price:.6f} {quote_currency.upper()}")
                    return price
            except Exception as e:
                logging.warning(f"从成交价获取{base_currency.upper()}价格失败: {str(e)}")

            # 方法2: 从订单簿获取中间价
            try:
                asks_df, bids_df = self.spot_market.get_orderbook(current_symbol)
                if not asks_df.empty and not bids_df.empty:
                    best_bid = float(bids_df['bids_price'].iloc[0])
                    best_ask = float(asks_df['asks_price'].iloc[0])
                    mid_price = (best_bid + best_ask) / 2
                    logging.info(f"从订单簿获取{base_currency.upper()}中间价: {mid_price:.6f} {quote_currency.upper()} (bid={best_bid:.6f}, ask={best_ask:.6f})")
                    return mid_price
            except Exception as e:
                logging.warning(f"从订单簿获取{base_currency.upper()}价格失败: {str(e)}")

            # 方法3: 使用默认估算价格
            logging.warning(f"无法获取{base_currency.upper()}实时价格，使用默认估算价格: {default_price} {quote_currency.upper()}")
            return default_price

        except Exception as e:
            logging.error(f"获取价格异常: {str(e)}")
            return get_symbol_config()["default_price"]  # 返回默认价格

    def update_params_status(self, param_id: int, status: int):
        """更新数据库参数状态"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("UPDATE buy_back_params SET status = %s WHERE id = %s;", (status, param_id))
                conn.commit()
                logging.info(f"更新参数状态: ID={param_id}, status={status}")
        except Exception as e:
            logging.error(f"更新参数状态失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_order_schedule(self, days: float, total_amount: float, each_amount: float = None) -> List[Dict]:
        """生成订单时间表

        Args:
            days: 总时间范围（天）
            total_amount: 总下单量（BYB）
            each_amount: 单次最大下单量（USDT金额），如果为None则使用默认配置

        Returns:
            订单时间表列表
        """
        try:
            # 获取当前交易对配置和价格
            trading_config = get_trading_config()
            current_price = self.get_current_price()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()

            # 计算时间范围
            start_time = datetime.now(beijing_tz)
            end_time = start_time + timedelta(days=days)
            total_seconds = int(days * 24 * 3600)

            # 确定单次下单量范围（转换计价货币金额为基础货币数量）
            if each_amount is not None and each_amount > 0:
                # each_amount是计价货币金额，需要转换为基础货币数量
                max_order_size_base = each_amount
                min_order_size_base = min(trading_config["min_order_size"], max_order_size_base/current_price * 0.5)  # 最小为max的50%
                logging.info(f"单次最大{quote_currency.upper()}金额: {each_amount:.2f} {quote_currency.upper()}, 按当前价格 {current_price:.6f} {quote_currency.upper()} 转换为 {max_order_size_base/current_price:.2f} {base_currency.upper()}")
            else:
                max_order_size_base = trading_config["max_order_size"]
                min_order_size_base = trading_config["min_order_size"]
                logging.info(f"使用默认配置的{base_currency.upper()}数量范围")

            logging.info(f"生成订单计划: 开始时间={start_time}, 结束时间={end_time}, 总金额={total_amount:.2f} {quote_currency.upper()}")
            logging.info(f"单次下单量范围: {min_order_size_base/current_price:.2f} - {max_order_size_base/current_price:.2f} {base_currency.upper()}")
            logging.info(f"当前{base_currency.upper()}价格: {current_price:.6f} {quote_currency.upper()}")

            # 直接生成订单计划（使用默认最大间隔）
            schedule = self._generate_orders_with_interval(
                total_amount, each_amount, current_price, start_time, end_time,
                trading_config, trading_config["max_interval"], base_currency, quote_currency
            )

            # 基于实际生成的订单数量计算动态最大间隔
            actual_orders = len(schedule)
            logging.info(f"实际生成订单数量: {actual_orders}")

            # 计算基于实际订单数量的动态最大间隔
            dynamic_max_interval = self.calculate_dynamic_max_interval(days, actual_orders, trading_config["min_interval"])
            logging.info(f"基于实际订单数量的动态最大时间间隔: {dynamic_max_interval}秒")

            # 如果动态间隔与默认间隔差异较大，重新生成订单计划
            default_max_interval = trading_config["max_interval"]
            interval_diff_ratio = abs(dynamic_max_interval - default_max_interval) / default_max_interval
            
            if interval_diff_ratio > 0.2:  # 如果差异超过20%，重新生成
                logging.info(f"间隔差异较大({interval_diff_ratio:.2%})，使用动态间隔重新生成订单计划")
                schedule = self._generate_orders_with_interval(
                    total_amount, each_amount, current_price, start_time, end_time,
                    trading_config, dynamic_max_interval, base_currency, quote_currency
                )
                logging.info(f"重新生成后订单数量: {len(schedule)}")

            logging.info(f"生成订单计划完成: 共 {len(schedule)} 个订单")
            schedule_lark_message(f"生成订单计划完成: 预计共 {len(schedule)} 个订单", level='info')
            return schedule
            
        except Exception as e:
            logging.error(f"生成订单计划失败: {str(e)}")
            schedule_lark_message(f"生成订单计划失败: {str(e)}", level='error')
            raise

    def _generate_orders_with_interval(self, total_amount: float, each_amount: float, current_price: float,
                                     start_time: datetime, end_time: datetime, trading_config: Dict,
                                     max_interval: int, base_currency: str, quote_currency: str) -> List[Dict]:
        """使用指定最大间隔生成订单计划
        
        Args:
            total_amount: 总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            start_time: 开始时间
            end_time: 结束时间
            trading_config: 交易配置
            max_interval: 最大时间间隔
            base_currency: 基础货币
            quote_currency: 计价货币
            
        Returns:
            订单计划列表
        """
        # 确定单次下单量范围
        if each_amount is not None and each_amount > 0:
            max_order_size_base = each_amount
            min_order_size_base = min(trading_config["min_order_size"], max_order_size_base/current_price * 0.5)
        else:
            max_order_size_base = trading_config["max_order_size"]
            min_order_size_base = trading_config["min_order_size"]

        # 分解订单
        remaining_amount = total_amount
        schedule = []
        current_time = start_time

        while remaining_amount > 0 and current_time < end_time:
            # 随机订单大小（基础货币数量）
            if each_amount and each_amount > remaining_amount:
                order_amount = int(remaining_amount)
            elif int(min_order_size_base*current_price) < int(min(each_amount or max_order_size_base, remaining_amount)):
                order_amount = random.randint(
                    int(min_order_size_base*current_price),
                    int(min(each_amount or max_order_size_base, remaining_amount))
                )
            else:
                order_amount = int(remaining_amount)
            
            logging.info(f"当次金额：{order_amount:.2f} {quote_currency.upper()}, 剩余金额: {remaining_amount:.2f} {quote_currency.upper()}, ")

            # 随机时间间隔
            interval = random.uniform(
                trading_config["min_interval"],
                max_interval
            )

            # 确保不超过结束时间
            next_time = current_time + timedelta(seconds=interval)
            if next_time > end_time:
                next_time = end_time

            order = {
                'scheduled_time': next_time,
                'amount': order_amount,
                'symbol': trading_config["symbol"],
                'side': trading_config["side"],
                'type': trading_config["type"],
                'status': 'pending'
            }

            schedule.append(order)
            remaining_amount -= order_amount
            current_time = next_time

            # 防止无限循环
            if len(schedule) > 10000:
                logging.warning("订单数量过多，停止生成")
                break
        
        # 处理剩余金额
        if remaining_amount > 0:
            if schedule:
                last_order = schedule[-1]
                max_order_size_check = max_order_size_base if each_amount is not None else max_order_size_base
                if each_amount is not None and (last_order['amount'] + remaining_amount) > max_order_size_check:
                    # 创建新订单处理剩余金额
                    while remaining_amount > 0:
                        new_order_amount = min(remaining_amount, each_amount)
                        new_order = {
                            'scheduled_time': end_time,
                            'amount': round(new_order_amount, 2),
                            'symbol': trading_config["symbol"],
                            'side': trading_config["side"],
                            'type': trading_config["type"],
                            'status': 'pending'
                        }
                        schedule.append(new_order)
                        remaining_amount -= new_order_amount
                        logging.info(f"创建新订单处理剩余金额: {new_order_amount:.2f} {base_currency.upper()}")
                else:
                    # 添加到最后一个订单
                    schedule[-1]['amount'] += remaining_amount
                    logging.info(f"剩余金额 {remaining_amount:.2f} {base_currency.upper()} 添加到最后一个订单")
            else:
                # 没有现有订单，创建新订单
                while remaining_amount > 0:
                    new_order_amount = min(remaining_amount, max_order_size_base)
                    new_order = {
                        'scheduled_time': end_time,
                        'amount': int(new_order_amount),
                        'symbol': trading_config["symbol"],
                        'side': trading_config["side"],
                        'type': trading_config["type"],
                        'status': 'pending'
                    }
                    schedule.append(new_order)
                    remaining_amount -= new_order_amount
                    logging.info(f"创建订单处理剩余金额: {new_order_amount:.2f} {base_currency.upper()}")

        return schedule
    
    async def execute_order(self, order: Dict) -> bool:
        """执行单个订单
        
        Args:
            order: 订单信息
            
        Returns:
            是否执行成功
        """
        try:
            logging.info(f"执行订单: {order['amount']} {order['symbol']} {order['side']}")
            
            # 执行下单
            params = {'symbol':order['symbol'], 'side':order['side'], 'type':order['type'], 'volume':order['amount']}
            logging.info(f"下单参数：{params}")
            required_fields = ['symbol', 'side', 'type', 'amount']
            for field in required_fields:
                if order.get(field) is None:
                    logging.error(f"订单参数 {field} 为 None，订单内容: {order}")
                    order['status'] = 'failed'
                    order['error'] = f"参数 {field} 为 None"
                    return False
            result = await self.spot_client.async_new_order(
                symbol=order['symbol'],
                side=order['side'],
                type=order['type'],
                volume=order['amount'],
                clientOrderId=f'byb_buy_back_order_{order["scheduled_time"].strftime("%Y%m%d_%H%M%S_%f")}'
            )

            if result:
                order['status'] = 'completed'
                order['result'] = result
                order['executed_time'] = datetime.now(beijing_tz)
                self.total_executed += order['amount']
                self.orders_executed += 1
                
                logging.info(f"订单执行成功: 订单ID={result.get('order_id', 'N/A')}, 金额={order['amount']}")
                return True
            else:
                order['status'] = 'failed'
                order['error'] = 'API返回空结果'
                logging.error(f"订单执行失败: API返回空结果")
                return False
                
        except Exception as e:
            order['status'] = 'failed'
            order['error'] = str(e)
            logging.error(f"订单执行异常: {str(e)}\n{traceback.format_exc()}")
            return False
    
    def save_execution_log(self):
        """保存执行日志到文件"""
        try:
            log_file = f"byb_order_execution_{datetime.now(beijing_tz).strftime('%Y%m%d_%H%M%S')}.json"
            log_data = {
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': datetime.now(beijing_tz).isoformat(),
                'total_executed': self.total_executed,
                'orders_executed': self.orders_executed,
                'total_orders': len(self.order_schedule),
                'execution_log': []
            }
            
            for order in self.order_schedule:
                log_entry = order.copy()
                # 转换时间为字符串
                if 'scheduled_time' in log_entry:
                    log_entry['scheduled_time'] = log_entry['scheduled_time'].isoformat()
                if 'executed_time' in log_entry:
                    log_entry['executed_time'] = log_entry['executed_time'].isoformat()
                log_data['execution_log'].append(log_entry)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"执行日志已保存到: {log_file}")
            
        except Exception as e:
            logging.error(f"保存执行日志失败: {str(e)}")

    def calculate_dynamic_max_interval(self, days: float, estimated_orders: int, min_interval: int = 60) -> int:
        """计算动态最大时间间隔
        
        Args:
            days: 总时间范围（天）
            estimated_orders: 预估订单数量
            min_interval: 最小时间间隔（秒）
            
        Returns:
            动态计算的最大时间间隔（秒）
        """
        try:
            # 计算总可用时间（秒）
            total_seconds = int(days * 24 * 3600)
            
            # 基础计算：总时间除以订单数，确保订单能均匀分布
            base_interval = total_seconds / max(estimated_orders, 1)
            
            # 添加多重随机性因子，增加反检测能力
            # 1. 基础随机因子：在0.4-1.1倍之间随机
            random_factor = random.uniform(0.4, 1.1)
            
            # 2. 时间分布不均匀因子：模拟真实交易行为的不规律性
            distribution_factor = random.uniform(1.1, 2.2)
            
            # 3. 市场活跃度因子：模拟不同时段的交易活跃度变化
            market_activity_factor = random.uniform(0.9, 1.4)
            
            # 4. 波动性因子：增加间隔的波动性
            volatility_factor = random.uniform(0.8, 1.3)
            
            # 5. 基于当前时间的微调因子（增加时间相关性）
            current_hour = datetime.now(beijing_tz).hour
            time_factor = 1.0
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                # 交易活跃时段，间隔稍短
                time_factor = random.uniform(0.9, 1.1)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                # 午休或晚餐时段，间隔稍长
                time_factor = random.uniform(1.1, 1.3)
            else:
                # 其他时段，正常间隔
                time_factor = random.uniform(0.95, 1.15)
            
            # 综合计算动态最大间隔
            dynamic_max_interval = int(base_interval * random_factor * distribution_factor * 
                                     market_activity_factor * volatility_factor * time_factor)
            
            # 设置合理的上下限，考虑不同时间范围的需求
            min_max_interval = min_interval * 2  # 最小最大间隔为最小间隔的2倍
            
            # 根据总时间动态调整最大间隔上限
            if days <= 1:
                # 1天内：最大不超过1小时
                max_max_interval = min(total_seconds // 6, 3600)
            elif days <= 3:
                # 1-3天：最大不超过2小时
                max_max_interval = min(total_seconds // 5, 7200)
            elif days <= 7:
                # 3-7天：最大不超过4小时
                max_max_interval = min(total_seconds // 4, 14400)
            else:
                # 7天以上：最大不超过6小时
                max_max_interval = min(total_seconds // 3, 21600)
            
            # 确保在合理范围内
            dynamic_max_interval = max(min_max_interval, min(dynamic_max_interval, max_max_interval))
            
            # 添加最后的随机微调（±5%）
            final_adjustment = random.uniform(0.95, 1.05)
            dynamic_max_interval = int(dynamic_max_interval * final_adjustment)
            
            logging.info(f"动态最大间隔计算详情:")
            logging.info(f"  总时间: {total_seconds}秒 ({days}天)")
            logging.info(f"  预估订单: {estimated_orders}")
            logging.info(f"  基础间隔: {base_interval:.1f}秒")
            logging.info(f"  随机因子: {random_factor:.3f}")
            logging.info(f"  分布因子: {distribution_factor:.3f}")
            logging.info(f"  市场活跃度因子: {market_activity_factor:.3f}")
            logging.info(f"  波动性因子: {volatility_factor:.3f}")
            logging.info(f"  时间因子: {time_factor:.3f} (当前小时: {current_hour})")
            logging.info(f"  最终调整: {final_adjustment:.3f}")
            logging.info(f"  最终最大间隔: {dynamic_max_interval}秒")
            
            return dynamic_max_interval
            
        except Exception as e:
            logging.error(f"计算动态最大间隔失败: {str(e)}")
            # 返回默认值
            return 3600

    def estimate_order_count(self, total_amount: float, each_amount: float, current_price: float) -> int:
        """估算订单数量
        
        Args:
            total_amount: 总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            
        Returns:
            预估订单数量
        """
        try:
            # 基础订单数量计算
            if each_amount and each_amount > 0:
                # 如果指定了each_amount，按此计算
                base_orders = total_amount / each_amount
            else:
                # 否则使用默认配置估算
                symbol_config = get_symbol_config()
                avg_order_size = (symbol_config["min_order_size"] + symbol_config["max_order_size"]) / 2
                base_orders = total_amount / (avg_order_size * current_price)
            
            # 添加多重随机性因子，模拟真实交易行为
            # 1. 基础随机变化：±20%
            base_variation = random.uniform(0.8, 1.2)
            
            # 2. 基于总金额的调整因子
            if total_amount > 10000:
                # 大额订单，数量可能稍少（更集中）
                amount_factor = random.uniform(0.9, 1.1)
            elif total_amount < 1000:
                # 小额订单，数量可能稍多（更分散）
                amount_factor = random.uniform(1.1, 1.3)
            else:
                # 中等金额，正常范围
                amount_factor = random.uniform(0.95, 1.15)
            
            # 3. 基于当前时间的调整因子
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                # 交易活跃时段，订单数量可能稍多
                time_factor = random.uniform(1.05, 1.15)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                # 休息时段，订单数量可能稍少
                time_factor = random.uniform(0.9, 1.05)
            else:
                # 其他时段，正常范围
                time_factor = random.uniform(0.95, 1.1)
            
            # 4. 市场波动性因子
            volatility_factor = random.uniform(0.85, 1.15)
            
            # 5. 基于星期几的微调（模拟周内交易模式）
            current_weekday = datetime.now(beijing_tz).weekday()
            if current_weekday in [0, 4]:  # 周一、周五
                weekday_factor = random.uniform(1.05, 1.15)  # 周初和周末可能更活跃
            elif current_weekday in [2, 3]:  # 周三、周四
                weekday_factor = random.uniform(0.95, 1.05)  # 周中相对稳定
            else:
                weekday_factor = random.uniform(0.9, 1.1)  # 其他时间正常
            
            # 综合计算最终订单数量
            final_orders = base_orders * base_variation * amount_factor * time_factor * volatility_factor * weekday_factor
            
            # 确保订单数量在合理范围内
            estimated_orders = max(1, int(final_orders))
            
            # 添加最后的随机微调（±3%）
            final_adjustment = random.uniform(0.97, 1.03)
            estimated_orders = max(1, int(estimated_orders * final_adjustment))
            
            logging.info(f"订单数量估算详情:")
            logging.info(f"  总金额: {total_amount:,.2f}")
            logging.info(f"  单次最大: {each_amount if each_amount else '默认配置'}")
            logging.info(f"  当前价格: {current_price:.6f}")
            logging.info(f"  基础订单数: {base_orders:.1f}")
            logging.info(f"  基础变化: {base_variation:.3f}")
            logging.info(f"  金额因子: {amount_factor:.3f}")
            logging.info(f"  时间因子: {time_factor:.3f} (当前小时: {current_hour})")
            logging.info(f"  波动因子: {volatility_factor:.3f}")
            logging.info(f"  星期因子: {weekday_factor:.3f} (星期{current_weekday + 1})")
            logging.info(f"  最终调整: {final_adjustment:.3f}")
            logging.info(f"  最终预估订单数: {estimated_orders}")
            
            return estimated_orders
            
        except Exception as e:
            logging.error(f"估算订单数量失败: {str(e)}")
            return 10  # 默认返回10个订单

    async def run_algorithm(self):
        """运行下单算法主程序"""
        try:
            # 获取数据库参数
            params = self.get_buy_back_params()

            # 检查状态是否允许执行
            if params.get('status', 0) != 1:
                logging.info(f"参数状态不允许执行: status={params.get('status', 0)}, 需要status=1")
                schedule_lark_message(f"⚠️ 下单算法跳过执行\n参数ID: {params['id']}\n状态: {params.get('status', 0)} (需要为1)", level='warning')
                return

            days = params['days']
            total_amount = params['total_amount']
            each_amount = params.get('each_amount', None)
            param_id = params['id']

            # 获取当前交易对配置和价格
            trading_config = get_trading_config()
            current_price = self.get_current_price()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()

            # 发送启动通知
            start_msg = (
                f"🚀 智能下单算法启动\n"
                f"交易对: {trading_config['symbol'].upper()}\n"
                f"总时间范围: {days} 天\n"
                f"总下单量: {total_amount:,.2f} {quote_currency.upper()}\n"
                f"当前{base_currency.upper()}价格: {current_price:.6f} {quote_currency.upper()}\n"
            )
            if each_amount:
                each_amount_base = each_amount / current_price
                start_msg += f"单次最大{quote_currency.upper()}: {each_amount:,.2f} {quote_currency.upper()} (约 {each_amount_base:.2f} {base_currency.upper()})\n"
            else:
                start_msg += f"单次量范围: {trading_config['min_order_size']}-{trading_config['max_order_size']} {base_currency.upper()}\n"

            start_msg += (
                f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(start_msg, level='info')

            # 生成订单计划
            self.order_schedule = self.generate_order_schedule(days, total_amount, each_amount)
            self.start_time = datetime.now(beijing_tz)
            self.is_running = True

            logging.info(f"开始执行订单计划，共 {len(self.order_schedule)} 个订单")

            # 执行订单
            for i, order in enumerate(self.order_schedule):
                if not self.is_running:
                    logging.info("算法被停止")
                    break

                # 等待到预定时间
                now = datetime.now(beijing_tz)
                if order['scheduled_time'] > now:
                    if i == 0:
                        logging.info(f"第一个订单将在 60 秒后执行")
                        await asyncio.sleep(60)
                    else:
                        wait_seconds = (order['scheduled_time'] - now).total_seconds()
                        logging.info(f"等待 {wait_seconds:.1f} 秒执行第 {i+1} 个订单")
                        await asyncio.sleep(wait_seconds)

                # 执行订单
                success = await self.execute_order(order)

                # 记录进度
                progress = (i + 1) / len(self.order_schedule) * 100
                logging.info(f"订单执行进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})")

                # 每10个订单发送一次进度通知
                if (i + 1) % 10 == 0 or not success:
                    base_currency = get_base_currency()
                    progress_msg = (
                        f"📊 下单进度更新\n"
                        f"交易对: {get_current_symbol().upper()}\n"
                        f"进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})\n"
                        f"已执行金额: {self.total_executed:,.2f} {quote_currency.upper()}\n"
                        f"成功订单: {self.orders_executed}\n"
                        f"当前时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                    if not success:
                        progress_msg += f"\n⚠️ 订单执行失败: {order.get('error', '未知错误')}{order}"

                    schedule_lark_message(progress_msg, level='info' if success else 'warning')

                # 短暂休息避免API限制
                await asyncio.sleep(1)

            # 完成通知
            base_currency = get_base_currency()
            completion_msg = (
                f"✅ 智能下单算法完成\n"
                f"交易对: {get_current_symbol().upper()}\n"
                f"总订单数: {len(self.order_schedule)}\n"
                f"成功订单: {self.orders_executed}\n"
                f"执行金额: {self.total_executed:,.2f} {quote_currency.upper()}\n"
                f"成功率: {(self.orders_executed/len(self.order_schedule)*100):.1f}%\n"
                f"完成时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(completion_msg, level='info')

            # 保存执行日志
            self.save_execution_log()

            # 交易完成后更新数据库参数状态为0
            try:
                self.update_params_status(param_id, 0)
                logging.info(f"交易完成，参数状态已更新为0: ID={param_id}")
                schedule_lark_message(f"📝 参数状态已更新\n参数ID: {param_id}\n状态: 1 → 0 (已完成)", level='info')
            except Exception as e:
                logging.error(f"更新参数状态失败: {str(e)}")
                schedule_lark_message(f"⚠️ 参数状态更新失败\n参数ID: {param_id}\n错误: {str(e)}", level='warning')

        except Exception as e:
            error_msg = f"下单算法执行异常: {str(e)}"
            logging.error(f"{error_msg}\n{traceback.format_exc()}")
            schedule_lark_message(error_msg, level='error')
        finally:
            self.is_running = False

    def stop_algorithm(self):
        """停止算法执行"""
        self.is_running = False
        logging.info("下单算法停止信号已发送")

    def get_status(self) -> Dict:
        """获取算法执行状态"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'total_orders': len(self.order_schedule),
            'orders_executed': self.orders_executed,
            'total_executed': self.total_executed,
            'progress': (self.orders_executed / len(self.order_schedule) * 100) if self.order_schedule else 0
        }


async def main():
    """主程序入口"""
    try:
        # 创建算法实例
        algorithm = BYBOrderAlgorithm()

        # 运行算法
        await algorithm.run_algorithm()

    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        if 'algorithm' in locals():
            algorithm.stop_algorithm()
    except Exception as e:
        logging.error(f"主程序异常: {str(e)}\n{traceback.format_exc()}")


if __name__ == "__main__":
    print("=" * 60)
    print("BYB智能下单算法".center(60))
    print("=" * 60)
    print("功能: 从数据库获取参数，在指定时间范围内随机分布下单")
    print("下单间隔: 1-60分钟随机")
    print("=" * 60)

    # 运行主程序
    asyncio.run(main())
