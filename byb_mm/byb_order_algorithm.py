import pandas as pd
import pymysql
import numpy as np
import random
import time
import asyncio
import logging
import traceback
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pytz
import sys
sys.path.append(r'/home/<USER>/byb_mm/')
import backoff
import aiohttp

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri

# ==================== 全局交易对配置 ====================
def get_current_symbol():
    """获取当前交易对 - 在这里修改交易对"""
    return "manausdt"  # 修改这里来切换交易对: bybusdt, manausdt, btcusdt 等

def get_base_currency():
    """获取基础货币"""
    symbol = get_current_symbol()
    if symbol == "bybusdt":
        return "byb"
    elif symbol == "manausdt":
        return "mana"
    elif symbol == "btcusdt":
        return "btc"
    elif symbol == "ethusdt":
        return "eth"
    else:
        # 自动解析：去掉usdt后缀
        if symbol.endswith("usdt"):
            return symbol[:-4]
        return "unknown"

def get_quote_currency():
    """获取计价货币"""
    symbol = get_current_symbol()
    if symbol.endswith("usdt"):
        return "usdt"
    elif symbol.endswith("btc"):
        return "btc"
    elif symbol.endswith("eth"):
        return "eth"
    else:
        return "usdt"  # 默认USDT

def get_symbol_config():
    """获取当前交易对的配置"""
    symbol = get_current_symbol()
    base = get_base_currency()
    quote = get_quote_currency()

    # 根据不同交易对返回不同配置
    configs = {
        "bybusdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        },
        "manausdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        }
    }

    return configs.get(symbol, {
        "min_order_size": 1.0,
        "max_order_size": 100.0,
        "default_price": 1.0
    })
# ==================== 全局交易对配置结束 ====================

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_order_algorithm.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接信息
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}

# 交易配置
def get_trading_config():
    """获取当前交易对的交易配置"""
    symbol_config = get_symbol_config()
    return {
        "symbol": get_current_symbol(),
        "side": "BUY",        # 买单
        "type": 2,            # 市价单
        "min_order_size": symbol_config["min_order_size"],
        "max_order_size": symbol_config["max_order_size"],
        "min_interval": 60,     # 最小下单间隔（秒）
        "max_interval": 3600,   # 最大下单间隔（秒）
    }


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB下单算法\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='info'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            task = loop.create_task(send_lark(msg, level))
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class BYBOrderAlgorithm:
    """BYB智能下单算法类"""
    
    def __init__(self):
        """初始化下单算法"""
        base_url = "https://openapi.100exdemo.com"
        self.spot_market = Spot()
        self.spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
        self.spot_market.BASE_URL = base_url
        self.spot_client.BASE_URL = base_url
        self.db_config = DB_CONFIG

        # 算法状态
        self.is_running = False
        self.total_executed = 0
        self.orders_executed = 0
        self.start_time = None
        self.end_time = None

        # 订单计划
        self.order_schedule = []
        self.execution_log = []

        # 获取当前交易对信息
        current_symbol = get_current_symbol()
        base_currency = get_base_currency()
        logging.info(f"智能下单算法初始化完成 - 交易对: {current_symbol}, 基础货币: {base_currency}")
    
    def _get_pending_orders(self, symbol: str, max_retries: int = 3):
        """获取当前未成交的挂单信息，带重试机制"""
        retry_count = 0
        backoff_time = 0.5

        while retry_count <= max_retries:
            try:
                result = self.spot_client.get_open_orders(symbol, pageSize=1000)
                if result is None or 'resultList' not in result:
                    if retry_count < max_retries:
                        retry_count += 1
                        time.sleep(backoff_time)
                        backoff_time *= 2
                        logging.warning(f"获取未成交订单返回无效结果，第 {retry_count} 次重试")
                        continue
                    else:
                        logging.error("获取未成交订单返回无效结果，重试失败")
                        return []
                orders = result['resultList']
                if orders is None:
                    if retry_count < max_retries:
                        retry_count += 1
                        time.sleep(backoff_time)
                        backoff_time *= 2
                        logging.warning(f"获取未成交订单返回None，第 {retry_count} 次重试")
                        continue
                    else:
                        logging.error("获取未成交订单返回None，重试失败")
                        return []
                valid_orders = []
                for order in orders:
                    if not isinstance(order, dict):
                        logging.warning(f"订单对象不是字典: {type(order)}")
                        continue
                    order['order_id'] = order['id']
                    if order['status'] in [0, 1, 3]:
                        valid_orders.append(order)
                logging.info(f"成功获取 {len(valid_orders)} 个有效订单")
                return valid_orders
            except Exception as e:
                error_str = str(e)
                if "API access too frequent" in error_str and retry_count < max_retries:
                    retry_count += 1
                    time.sleep(backoff_time)
                    backoff_time *= 2
                    logging.warning(f"API访问过于频繁，等待{backoff_time:.1f}秒后重试（第{retry_count}次）")
                    continue
                else:
                    logging.error(f"获取未成交订单失败: {error_str}")
                    return []

    def _group_orders_by_price(self, df_orders, price_col='price', volume_col='volume'):
        """按价格聚合挂单数量"""
        grouped = df_orders.groupby(price_col)[volume_col].sum().reset_index()
        grouped[price_col] = grouped[price_col].astype(float)
        grouped[volume_col] = grouped[volume_col].astype(float)
        return grouped

    def _split_mm_and_user_orders(self, orderbook_df, my_grouped, price_col, qty_col):
        """区分做市商和用户订单"""
        if orderbook_df.empty:
            return pd.DataFrame(columns=['price', 'qty']), pd.DataFrame(columns=['price', 'qty'])
        
        merged = pd.merge(
            orderbook_df,
            my_grouped,
            left_on=price_col,
            right_on='price',
            how='left'
        )
        merged['volume'] = merged['volume'].fillna(0)
        merged['user_qty'] = merged[qty_col] - merged['volume']
        
        df_user = merged[merged['user_qty'] > 1e-6][[price_col, 'user_qty']].rename(columns={price_col: 'price', 'user_qty': 'qty'})
        df_mm = merged[merged['volume'] > 1e-6][[price_col, 'volume']].rename(columns={price_col: 'price', 'volume': 'qty'})
        
        return df_mm.reset_index(drop=True), df_user.reset_index(drop=True)

    def get_user_asks(self, symbol: str) -> pd.DataFrame:
        """获取用户卖单"""
        try:
            my_orders = self._get_pending_orders(symbol)
            if not my_orders:
                logging.info("没有发现自己的挂单")
                df_asks, _ = self.spot_market.get_orderbook(symbol)
                return df_asks.rename(columns={'asks_price': 'price', 'asks_qty': 'qty'})

            df_open_orders = pd.DataFrame(my_orders)
            df_my_asks = df_open_orders[df_open_orders['side'] == 'SELL']

            df_asks, _ = self.spot_market.get_orderbook(symbol)

            my_asks_grouped = self._group_orders_by_price(df_my_asks, 'price', 'volume')
            _, df_user_asks = self._split_mm_and_user_orders(df_asks, my_asks_grouped, 'asks_price', 'asks_qty')
            
            logging.info(f"成功分离出 {len(df_user_asks)} 条用户卖单")
            return df_user_asks
        except Exception as e:
            logging.error(f"获取用户卖单失败: {e}")
            df_asks, _ = self.spot_market.get_orderbook(symbol)
            return df_asks.rename(columns={'asks_price': 'price', 'asks_qty': 'qty'})

    def get_buy_back_params(self) -> Dict:
        """从数据库获取回购参数"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM buy_back_params ORDER BY created_at DESC LIMIT 1;")
                result = cursor.fetchone()
                if result:
                    params = {
                        'id': result[0],
                        'days': float(result[1]),
                        'total_amount': float(result[2]),
                        'each_amount': float(result[3]),
                        'created_at': result[4],
                        'status': result[5]
                    }
                    logging.info(f"获取数据库参数: {params}")
                    return params
                else:
                    raise ValueError("数据库中没有找到回购参数")
        except Exception as e:
            logging.error(f"获取数据库参数失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def get_current_price(self) -> float:
        """获取当前交易对价格（计价货币计价）"""
        try:
            current_symbol = get_current_symbol()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()
            symbol_config = get_symbol_config()
            default_price = symbol_config["default_price"]

            # 方法1: 尝试从get_trade_price获取最新成交价
            try:
                trade_prices_df = self.spot_market.get_trade_price()
                symbol_data = trade_prices_df[trade_prices_df['symbol'] == current_symbol]
                if not symbol_data.empty:
                    price = float(symbol_data['price'].iloc[0])
                    logging.info(f"从成交价获取{base_currency.upper()}价格: {price:.6f} {quote_currency.upper()}")
                    return price
            except Exception as e:
                logging.warning(f"从成交价获取{base_currency.upper()}价格失败: {str(e)}")

            # 方法2: 从订单簿获取中间价
            try:
                asks_df, bids_df = self.spot_market.get_orderbook(current_symbol)
                if not asks_df.empty and not bids_df.empty:
                    best_bid = float(bids_df['bids_price'].iloc[0])
                    best_ask = float(asks_df['asks_price'].iloc[0])
                    mid_price = (best_bid + best_ask) / 2
                    logging.info(f"从订单簿获取{base_currency.upper()}中间价: {mid_price:.6f} {quote_currency.upper()} (bid={best_bid:.6f}, ask={best_ask:.6f})")
                    return mid_price
            except Exception as e:
                logging.warning(f"从订单簿获取{base_currency.upper()}价格失败: {str(e)}")

            # 方法3: 使用默认估算价格
            logging.warning(f"无法获取{base_currency.upper()}实时价格，使用默认估算价格: {default_price} {quote_currency.upper()}")
            return default_price

        except Exception as e:
            logging.error(f"获取价格异常: {str(e)}")
            return get_symbol_config()["default_price"]  # 返回默认价格

    def get_mid_price(self, symbol: str) -> Optional[float]:
        """获取订单簿中间价"""
        try:
            asks_df, bids_df = self.spot_market.get_orderbook(symbol)
            if not asks_df.empty and not bids_df.empty:
                best_bid = float(bids_df['bids_price'].iloc[0])
                best_ask = float(asks_df['asks_price'].iloc[0])
                return (best_bid + best_ask) / 2
        except Exception as e:
            logging.warning(f"从订单簿获取中间价失败: {str(e)}")
        return None

    def update_params_status(self, param_id: int, status: int):
        """更新数据库参数状态"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("UPDATE buy_back_params SET status = %s WHERE id = %s;", (status, param_id))
                conn.commit()
                logging.info(f"更新参数状态: ID={param_id}, status={status}")
        except Exception as e:
            logging.error(f"更新参数状态失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def generate_order_schedule(self, days: float, total_amount: float, each_amount: float = None) -> List[Dict]:
        """生成订单时间表

        Args:
            days: 总时间范围（天）
            total_amount: 总下单量（BYB）
            each_amount: 单次最大下单量（USDT金额），如果为None则使用默认配置

        Returns:
            订单时间表列表
        """
        try:
            # 获取当前交易对配置和价格
            trading_config = get_trading_config()
            current_price = self.get_current_price()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()

            # 计算时间范围
            start_time = datetime.now(beijing_tz)
            end_time = start_time + timedelta(days=days)
            total_seconds = int(days * 24 * 3600)

            # 确定单次下单量范围（转换计价货币金额为基础货币数量）
            if each_amount is not None and each_amount > 0:
                max_order_size_base = each_amount
                min_order_size_base = min(trading_config["min_order_size"], max_order_size_base/current_price * 0.5)  # 最小为max的50%
                logging.info(f"单次最大{quote_currency.upper()}金额: {each_amount:.2f} {quote_currency.upper()}, 按当前价格 {current_price:.6f} {quote_currency.upper()} 转换为 {max_order_size_base/current_price:.2f} {base_currency.upper()}")
            else:
                max_order_size_base = trading_config["max_order_size"]
                min_order_size_base = trading_config["min_order_size"]
                logging.info(f"使用默认配置的{base_currency.upper()}数量范围")

            logging.info(f"生成订单计划: 开始时间={start_time}, 结束时间={end_time}, 总金额={total_amount:.2f} {quote_currency.upper()}")
            logging.info(f"单次下单量范围: {min_order_size_base/current_price:.2f} - {max_order_size_base/current_price:.2f} {base_currency.upper()}")
            logging.info(f"当前{base_currency.upper()}价格: {current_price:.6f} {quote_currency.upper()}")

            # 估算订单数量
            estimated_orders = self.estimate_order_count(total_amount, each_amount, current_price)
            logging.info(f"预估订单数量: {estimated_orders}")

            # ====== 新增：短期交易直接用等间隔+微扰动 ======
            if days <= 3:
                # 重新设计：更激进的时间控制策略
                schedule = []
                remaining_amount = total_amount
                
                # 计算实际需要的订单数量（基于金额）
                if each_amount and each_amount > 0:
                    actual_orders_needed = int(total_amount / each_amount)
                    if total_amount % each_amount > 0:
                        actual_orders_needed += 1
                else:
                    actual_orders_needed = int(total_amount / max_order_size_base)
                    if total_amount % max_order_size_base > 0:
                        actual_orders_needed += 1
                
                # 确保订单数量合理
                actual_orders_needed = max(1, min(actual_orders_needed, estimated_orders * 2))
                
                # 根据订单数量调整策略
                if actual_orders_needed < 5:
                    # 订单数量很少时，使用固定间隔，无扰动
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.0  # 无扰动
                elif actual_orders_needed < 10:
                    # 中等订单数量，极小扰动
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.005  # ±0.5%
                else:
                    # 订单数量较多时，适度扰动
                    base_interval = total_seconds / actual_orders_needed
                    perturbation_range = 0.01  # ±1%
                
                current_time = start_time
                
                # 生成订单
                for i in range(actual_orders_needed):
                    if remaining_amount <= 0:
                        break
                    
                    # 计算订单时间
                    if i == actual_orders_needed - 1:
                        # 最后一个订单：精确控制到结束时间，允许间隔违规
                        # 计算前N-1个订单已经使用的时间
                        used_time = 0
                        for j in range(1, len(schedule)):
                            prev_time = schedule[j-1]['scheduled_time']
                            curr_time = schedule[j]['scheduled_time']
                            used_time += (curr_time - prev_time).total_seconds()
                        
                        # 计算剩余可用时间
                        remaining_time = total_seconds - used_time
                        
                        # 确保最后一个订单在合理时间范围内
                        if remaining_time > 0:
                            # 更精确的时间控制：在剩余时间的60%-90%范围内选择
                            # 这样可以确保最后一个订单不会太早完成，也不会太晚
                            target_offset = remaining_time * random.uniform(0.6, 0.9)
                            target_time = current_time + timedelta(seconds=int(target_offset))
                            
                            # 如果剩余时间很少，直接使用结束时间附近
                            if remaining_time < base_interval * 0.5:
                                target_time = end_time - timedelta(seconds=random.randint(60, 300))
                        else:
                            # 如果时间不够，直接使用结束时间
                            target_time = end_time - timedelta(seconds=random.randint(60, 180))
                        
                        # 确保不超过结束时间，但允许更灵活的处理
                        if target_time > end_time:
                            target_time = end_time - timedelta(seconds=random.randint(30, 180))  # 提前30秒到3分钟
                        if target_time <= current_time:
                            target_time = current_time + timedelta(seconds=random.randint(30, 120))  # 至少间隔30秒到2分钟
                        
                        logging.info(f"最后一个订单时间控制: 剩余时间={remaining_time:.1f}秒, 目标时间={target_time.strftime('%H:%M:%S')}, 理论结束时间={end_time.strftime('%H:%M:%S')}")
                    else:
                        # 其他订单：使用精确的时间控制
                        # 计算当前订单应该的时间点
                        progress_ratio = i / (actual_orders_needed - 1)
                        target_progress_time = start_time + timedelta(seconds=int(total_seconds * progress_ratio))
                        
                        # 添加微扰动
                        if perturbation_range > 0:
                            perturbation = random.uniform(-perturbation_range, perturbation_range)
                            perturbation_seconds = int(base_interval * perturbation)
                            target_time = target_progress_time + timedelta(seconds=perturbation_seconds)
                        else:
                            target_time = target_progress_time
                        
                        # 确保时间合理
                        if target_time <= current_time:
                            target_time = current_time + timedelta(seconds=60)  # 至少间隔1分钟
                        if target_time > end_time:
                            target_time = end_time - timedelta(seconds=base_interval)  # 提前一个间隔
                    
                    # 计算订单金额
                    if each_amount and each_amount > 0:
                        order_amount = min(each_amount, remaining_amount)
                    else:
                        order_amount = min(max_order_size_base, remaining_amount)
                    
                    order = {
                        'scheduled_time': target_time,
                        'amount': order_amount,
                        'symbol': trading_config["symbol"],
                        'side': trading_config["side"],
                        'type': trading_config["type"],
                        'status': 'pending'
                    }
                    schedule.append(order)
                    remaining_amount -= order_amount
                    current_time = target_time
                
                # 处理剩余金额
                if remaining_amount > 0 and schedule:
                    schedule[-1]['amount'] += remaining_amount
                
                # 验证订单计划
                if schedule:
                    # 根据实际生成的时间分布调整验证范围
                    actual_intervals = []
                    for j in range(1, len(schedule)):
                        prev_time = schedule[j-1]['scheduled_time']
                        curr_time = schedule[j]['scheduled_time']
                        interval = (curr_time - prev_time).total_seconds()
                        actual_intervals.append(interval)
                    
                    if actual_intervals:
                        avg_actual_interval = sum(actual_intervals) / len(actual_intervals)
                        # 使用实际平均间隔的±5%作为验证范围
                        min_interval = int(avg_actual_interval * 0.95)
                        max_interval = int(avg_actual_interval * 1.05)
                    else:
                        min_interval = int(base_interval * (1 - perturbation_range))
                        max_interval = int(base_interval * (1 + perturbation_range))
                    
                    self._validate_order_schedule_timing(schedule, days, min_interval, max_interval)
                
                logging.info(f"生成订单计划完成: 共 {len(schedule)} 个订单")
                schedule_lark_message(f"生成订单计划完成: 预计共 {len(schedule)} 个订单", level='info')
                return schedule
            # ====== 其他情况走原动态区间算法 ======

            # 计算动态最大和最小间隔
            dynamic_max_interval = self.calculate_dynamic_max_interval(days, estimated_orders, trading_config["min_interval"])
            dynamic_min_interval = self.calculate_dynamic_min_interval(days, estimated_orders, dynamic_max_interval)
            logging.info(f"动态间隔范围: {dynamic_min_interval}秒 - {dynamic_max_interval}秒")
            logging.info(f"原始间隔范围: {trading_config['min_interval']}秒 - {trading_config['max_interval']}秒")

            # 使用动态间隔生成订单计划
            schedule = self._generate_orders_with_interval(
                total_amount, each_amount, current_price, start_time, end_time,
                trading_config, dynamic_max_interval, base_currency, quote_currency, dynamic_min_interval
            )

            # 基于实际生成的订单数量验证和调整
            actual_orders = len(schedule)
            logging.info(f"实际生成订单数量: {actual_orders}")

            # 如果实际订单数量与预估差异较大，重新计算动态间隔
            order_diff_ratio = abs(actual_orders - estimated_orders) / max(estimated_orders, 1)
            if order_diff_ratio > 0.3:  # 如果差异超过30%
                logging.info(f"实际订单数量与预估差异较大({order_diff_ratio:.2%})，重新计算动态间隔")
                adjusted_max_interval = self.calculate_dynamic_max_interval(days, actual_orders, dynamic_min_interval)
                adjusted_min_interval = self.calculate_dynamic_min_interval(days, actual_orders, adjusted_max_interval)
                logging.info(f"调整后动态间隔范围: {adjusted_min_interval}秒 - {adjusted_max_interval}秒")
                max_interval_diff = abs(adjusted_max_interval - dynamic_max_interval) / dynamic_max_interval
                min_interval_diff = abs(adjusted_min_interval - dynamic_min_interval) / dynamic_min_interval
                if max_interval_diff > 0.2 or min_interval_diff > 0.2:
                    logging.info("间隔调整幅度较大，重新生成订单计划")
                    schedule = self._generate_orders_with_interval(
                        total_amount, each_amount, current_price, start_time, end_time,
                        trading_config, adjusted_max_interval, base_currency, quote_currency, adjusted_min_interval
                    )
                    logging.info(f"重新生成后订单数量: {len(schedule)}")

            # 验证订单计划的时间分布
            self._validate_order_schedule_timing(schedule, days, dynamic_min_interval, dynamic_max_interval)

            # 优化：如果时间偏差较大，尝试重新生成
            if len(schedule) > 1:
                total_actual_time = 0
                for i in range(1, len(schedule)):
                    prev_time = schedule[i-1]['scheduled_time']
                    current_time = schedule[i]['scheduled_time']
                    interval = (current_time - prev_time).total_seconds()
                    total_actual_time += interval
                theoretical_total_time = int(days * 24 * 3600)
                time_deviation_ratio = abs(total_actual_time - theoretical_total_time) / theoretical_total_time
                if time_deviation_ratio > 0.2:  # 如果偏差超过20%
                    logging.warning(f"时间偏差较大({time_deviation_ratio:.2%})，尝试优化订单计划")
                    optimized_schedule = self._optimize_order_schedule(
                        schedule, days, dynamic_min_interval, dynamic_max_interval, 
                        total_amount, each_amount, current_price, trading_config, 
                        base_currency, quote_currency
                    )
                    if optimized_schedule:
                        schedule = optimized_schedule
                        logging.info("订单计划优化完成")
                        self._validate_order_schedule_timing(schedule, days, dynamic_min_interval, dynamic_max_interval)

            logging.info(f"生成订单计划完成: 共 {len(schedule)} 个订单")
            schedule_lark_message(f"生成订单计划完成: 预计共 {len(schedule)} 个订单", level='info')
            return schedule
        except Exception as e:
            logging.error(f"生成订单计划失败: {str(e)}")
            schedule_lark_message(f"生成订单计划失败: {str(e)}", level='error')
            raise

    def _generate_orders_with_interval(self, total_amount: float, each_amount: float, current_price: float,
                                     start_time: datetime, end_time: datetime, trading_config: Dict,
                                     max_interval: int, base_currency: str, quote_currency: str, 
                                     min_interval: int = None) -> List[Dict]:
        """使用指定间隔范围生成订单计划
        
        Args:
            total_amount: 总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            start_time: 开始时间
            end_time: 结束时间
            trading_config: 交易配置
            max_interval: 最大时间间隔
            base_currency: 基础货币
            quote_currency: 计价货币
            min_interval: 最小时间间隔，如果为None则使用trading_config中的默认值
            
        Returns:
            订单计划列表
        """
        # 确定单次下单量范围
        if each_amount is not None and each_amount > 0:
            max_order_size_base = each_amount
            min_order_size_base = min(trading_config["min_order_size"], max_order_size_base/current_price * 0.5)
        else:
            max_order_size_base = trading_config["max_order_size"]
            min_order_size_base = trading_config["min_order_size"]

        # 确定使用的最小间隔
        actual_min_interval = min_interval if min_interval is not None else trading_config["min_interval"]
        logging.info(f"使用的最小间隔: {actual_min_interval}秒, 最大间隔: {max_interval}秒")

        # 分解订单
        remaining_amount = total_amount
        schedule = []
        current_time = start_time

        while remaining_amount > 0 and current_time < end_time:
            # 随机订单大小（基础货币数量）
            if each_amount and each_amount > remaining_amount:
                order_amount = int(remaining_amount)
            elif int(min_order_size_base*current_price) < int(min(each_amount or max_order_size_base, remaining_amount)):
                order_amount = random.randint(
                    int(min_order_size_base*current_price),
                    int(min(each_amount or max_order_size_base, remaining_amount))
                )
            else:
                order_amount = int(remaining_amount)
            
            logging.info(f"当次金额：{order_amount:.2f} {quote_currency.upper()}, 剩余金额: {remaining_amount:.2f} {quote_currency.upper()}, ")

            # 随机时间间隔
            interval = random.uniform(
                actual_min_interval,
                max_interval
            )

            # 确保不超过结束时间
            next_time = current_time + timedelta(seconds=interval)
            if next_time > end_time:
                next_time = end_time

            order = {
                'scheduled_time': next_time,
                'amount': order_amount,
                'symbol': trading_config["symbol"],
                'side': trading_config["side"],
                'type': trading_config["type"],
                'status': 'pending'
            }

            schedule.append(order)
            remaining_amount -= order_amount
            current_time = next_time

            # 防止无限循环
            if len(schedule) > 10000:
                logging.warning("订单数量过多，停止生成")
                break
        
        # 处理剩余金额
        if remaining_amount > 0:
            if schedule:
                last_order = schedule[-1]
                max_order_size_check = max_order_size_base if each_amount is not None else max_order_size_base
                if each_amount is not None and (last_order['amount'] + remaining_amount) > max_order_size_check:
                    # 创建新订单处理剩余金额
                    while remaining_amount > 0:
                        new_order_amount = min(remaining_amount, each_amount)
                        new_order = {
                            'scheduled_time': end_time,
                            'amount': round(new_order_amount, 2),
                            'symbol': trading_config["symbol"],
                            'side': trading_config["side"],
                            'type': trading_config["type"],
                            'status': 'pending'
                        }
                        schedule.append(new_order)
                        remaining_amount -= new_order_amount
                        logging.info(f"创建新订单处理剩余金额: {new_order_amount:.2f} {base_currency.upper()}")
                else:
                    # 添加到最后一个订单
                    schedule[-1]['amount'] += remaining_amount
                    logging.info(f"剩余金额 {remaining_amount:.2f} {base_currency.upper()} 添加到最后一个订单")
            else:
                # 没有现有订单，创建新订单
                while remaining_amount > 0:
                    new_order_amount = min(remaining_amount, max_order_size_base)
                    new_order = {
                        'scheduled_time': end_time,
                        'amount': int(new_order_amount),
                        'symbol': trading_config["symbol"],
                        'side': trading_config["side"],
                        'type': trading_config["type"],
                        'status': 'pending'
                    }
                    schedule.append(new_order)
                    remaining_amount -= new_order_amount
                    logging.info(f"创建订单处理剩余金额: {new_order_amount:.2f} {base_currency.upper()}")

        return schedule

    def _validate_order_schedule_timing(self, schedule: List[Dict], days: float, min_interval: int, max_interval: int):
        """验证订单计划的时间分布是否符合要求
        
        Args:
            schedule: 订单计划列表
            days: 总时间范围（天）
            min_interval: 最小时间间隔（秒）
            max_interval: 最大时间间隔（秒）
        """
        try:
            if len(schedule) < 2:
                logging.info("订单数量少于2个，跳过时间分布验证")
                return

            total_seconds = int(days * 24 * 3600)
            intervals = []
            total_actual_time = 0

            # 计算实际间隔
            for i in range(1, len(schedule)):
                prev_time = schedule[i-1]['scheduled_time']
                current_time = schedule[i]['scheduled_time']
                interval = (current_time - prev_time).total_seconds()
                intervals.append(interval)
                total_actual_time += interval

            # 计算统计信息
            avg_interval = sum(intervals) / len(intervals)
            min_actual_interval = min(intervals)
            max_actual_interval = max(intervals)
            
            # 计算理论总时间
            theoretical_total_time = total_seconds
            
            # 计算时间分布偏差
            time_deviation_ratio = abs(total_actual_time - theoretical_total_time) / theoretical_total_time
            
            # 验证间隔范围（排除最后一个订单）
            interval_violations = []
            for i, interval in enumerate(intervals):
                is_last_order = (i == len(intervals) - 1)  # 最后一个订单
                if interval < min_interval:
                    if is_last_order:
                        logging.info(f"最后一个订单间隔{interval:.1f}秒 < 最小间隔{min_interval}秒 (允许违规)")
                    else:
                        interval_violations.append(f"订单{i+2}间隔{interval:.1f}秒 < 最小间隔{min_interval}秒")
                elif interval > max_interval:
                    if is_last_order:
                        logging.info(f"最后一个订单间隔{interval:.1f}秒 > 最大间隔{max_interval}秒 (允许违规)")
                    else:
                        interval_violations.append(f"订单{i+2}间隔{interval:.1f}秒 > 最大间隔{max_interval}秒")

            # 输出验证结果
            logging.info(f"订单计划时间分布验证:")
            logging.info(f"  总订单数: {len(schedule)}")
            logging.info(f"  理论总时间: {theoretical_total_time}秒 ({days}天)")
            logging.info(f"  实际总时间: {total_actual_time:.1f}秒")
            logging.info(f"  时间偏差: {time_deviation_ratio:.2%}")
            logging.info(f"  平均间隔: {avg_interval:.1f}秒")
            logging.info(f"  最小实际间隔: {min_actual_interval:.1f}秒 (要求≥{min_interval}秒)")
            logging.info(f"  最大实际间隔: {max_actual_interval:.1f}秒 (要求≤{max_interval}秒)")
            
            if interval_violations:
                logging.warning(f"发现 {len(interval_violations)} 个间隔违规:")
                for violation in interval_violations:
                    logging.warning(f"    {violation}")
            else:
                logging.info("所有订单间隔都在规定范围内")

            # 时间偏差警告
            if time_deviation_ratio > 0.1:  # 如果偏差超过10%
                logging.warning(f"订单计划时间分布偏差较大({time_deviation_ratio:.2%})，可能影响总时长")
                schedule_lark_message(f"⚠️ 订单计划时间分布偏差较大\n偏差: {time_deviation_ratio:.2%}\n理论时间: {days}天\n实际时间: {total_actual_time/86400:.1f}天", level='warning')
            else:
                logging.info("订单计划时间分布合理")

        except Exception as e:
            logging.error(f"验证订单计划时间分布失败: {str(e)}")

    def _optimize_order_schedule(self, original_schedule: List[Dict], days: float, 
                                min_interval: int, max_interval: int, total_amount: float,
                                each_amount: float, current_price: float, trading_config: Dict,
                                base_currency: str, quote_currency: str) -> List[Dict]:
        """优化订单计划，减少时间偏差
        
        Args:
            original_schedule: 原始订单计划
            days: 总时间范围
            min_interval: 最小间隔
            max_interval: 最大间隔
            total_amount: 总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            trading_config: 交易配置
            base_currency: 基础货币
            quote_currency: 计价货币
            
        Returns:
            优化后的订单计划，如果优化失败返回None
        """
        try:
            logging.info("开始优化订单计划...")
            
            # 计算理论总时间
            theoretical_total_time = int(days * 24 * 3600)
            
            # 计算当前实际总时间
            current_total_time = 0
            if len(original_schedule) > 1:
                for i in range(1, len(original_schedule)):
                    prev_time = original_schedule[i-1]['scheduled_time']
                    current_time = original_schedule[i]['scheduled_time']
                    interval = (current_time - prev_time).total_seconds()
                    current_total_time += interval
            
            # 计算时间偏差
            time_deviation = theoretical_total_time - current_total_time
            time_deviation_ratio = abs(time_deviation) / theoretical_total_time
            
            logging.info(f"时间偏差: {time_deviation_ratio:.2%} ({time_deviation}秒)")
            
            # 如果偏差较小，不需要优化
            if time_deviation_ratio <= 0.1:
                logging.info("时间偏差较小，无需优化")
                return None
            
            # 优化策略：调整间隔范围
            if time_deviation > 0:
                # 实际时间短于理论时间，需要增加间隔
                adjustment_factor = 1 + time_deviation_ratio * 0.5
                new_min_interval = int(min_interval * adjustment_factor)
                new_max_interval = int(max_interval * adjustment_factor)
                logging.info(f"增加间隔范围: {new_min_interval}-{new_max_interval}秒")
            else:
                # 实际时间长于理论时间，需要减少间隔
                adjustment_factor = 1 - time_deviation_ratio * 0.5
                new_min_interval = int(min_interval * adjustment_factor)
                new_max_interval = int(max_interval * adjustment_factor)
                logging.info(f"减少间隔范围: {new_min_interval}-{new_max_interval}秒")
            
            # 确保新的间隔范围合理
            new_min_interval = max(30, min(new_min_interval, new_max_interval * 0.8))
            new_max_interval = max(new_min_interval * 1.5, new_max_interval)
            
            # 重新生成订单计划
            start_time = original_schedule[0]['scheduled_time']
            end_time = start_time + timedelta(days=days)
            
            optimized_schedule = self._generate_orders_with_interval(
                total_amount, each_amount, current_price, start_time, end_time,
                trading_config, new_max_interval, base_currency, quote_currency, new_min_interval
            )
            
            # 验证优化效果
            if len(optimized_schedule) > 1:
                optimized_total_time = 0
                for i in range(1, len(optimized_schedule)):
                    prev_time = optimized_schedule[i-1]['scheduled_time']
                    current_time = optimized_schedule[i]['scheduled_time']
                    interval = (current_time - prev_time).total_seconds()
                    optimized_total_time += interval
                
                optimized_deviation_ratio = abs(optimized_total_time - theoretical_total_time) / theoretical_total_time
                
                if optimized_deviation_ratio < time_deviation_ratio:
                    logging.info(f"优化成功: 偏差从{time_deviation_ratio:.2%}减少到{optimized_deviation_ratio:.2%}")
                    return optimized_schedule
                else:
                    logging.warning(f"优化失败: 偏差从{time_deviation_ratio:.2%}增加到{optimized_deviation_ratio:.2%}")
                    return None
            
            return None
            
        except Exception as e:
            logging.error(f"优化订单计划失败: {str(e)}")
            return None
    
    async def execute_order(self, order: Dict) -> bool:
        """执行单个订单
        
        Args:
            order: 订单信息
            
        Returns:
            是否执行成功
        """
        try:
            symbol = order['symbol']
            side = order['side']
            order_type = order['type']
            amount_to_buy = order['amount']

            logging.info(f"准备执行订单: {amount_to_buy} {symbol} {side}")

            df_user_asks = self.get_user_asks(symbol)

            if df_user_asks.empty:
                logging.warning("订单簿上没有用户卖单，本次不执行下单。")
                order['status'] = 'skipped'
                order['error'] = 'No user asks on orderbook'
                return False

            mid_price = self.get_mid_price(symbol)
            if not mid_price:
                logging.warning("无法获取中间价，跳过价格检查逻辑，按原逻辑执行。")
                # Fallback to original logic if mid_price is not available
                user_asks_total_qty = df_user_asks['qty'].sum()
                final_buy_amount = min(amount_to_buy, user_asks_total_qty)
            else:
                # 筛选出价格在2%以内的用户订单
                df_user_asks['deviation'] = abs(df_user_asks['price'] - mid_price) / mid_price
                valid_asks = df_user_asks[df_user_asks['deviation'] <= 0.02].copy()

                if valid_asks.empty:
                    logging.warning("没有价格在2%以内的用户卖单，本次不执行下单。")
                    order['status'] = 'skipped'
                    order['error'] = 'No user asks within 2% price deviation'
                    return False
                
                logging.info(f"发现 {len(valid_asks)} 个价格在2%以内的用户卖单。")
                
                # 从最便宜的开始吃
                valid_asks = valid_asks.sort_values(by='price', ascending=True)
                
                amount_left_to_buy = amount_to_buy
                final_buy_amount = 0
                
                for index, user_ask in valid_asks.iterrows():
                    if amount_left_to_buy <= 1e-6:
                        break
                    
                    target_price = user_ask['price']
                    target_qty = user_ask['qty']
                    
                    # 拉盘用的数量，先用一个较小值，例如最小下单量的1.1倍
                    pull_qty = get_symbol_config().get("min_order_size", 1.0) * 1.1

                    logging.info(f"准备处理用户卖单: 价格={target_price}, 数量={target_qty}")
                    logging.info(f"开始拉盘... 价格: {target_price}, 数量: {pull_qty}")
                    
                    try:
                        # 1. 自成交拉盘，下一个买单把价格拉上去
                        pull_order_result = await self.spot_client.async_self_trade(
                            symbol=symbol,
                            side='BUY',
                            volume=int(pull_qty),
                            type=1, # 限价单
                            price=str(target_price)
                        )
                        if pull_order_result:
                            logging.info(f"拉盘订单成功: {pull_order_result}")
                        else:
                            logging.warning("拉盘订单失败，跳过此用户订单。")
                            continue

                        # 短暂等待盘口更新
                        await asyncio.sleep(0.5)

                        # 2. 下限价单吃掉用户的单
                        qty_to_buy = min(amount_left_to_buy, target_qty)
                        
                        logging.info(f"准备下限价单成交: 价格={target_price}, 数量={qty_to_buy}")
                        
                        trade_result = await self.spot_client.async_new_order(
                            symbol=symbol,
                            side='BUY',
                            type=1, # 限价单
                            volume=qty_to_buy,
                            price=str(target_price),
                            clientOrderId=f'byb_buy_back_trade_{order["scheduled_time"].strftime("%Y%m%d_%H%M%S_%f")}_{index}'
                        )
                        
                        if trade_result:
                            logging.info(f"与用户订单成交成功: {trade_result}")
                            final_buy_amount += qty_to_buy
                            amount_left_to_buy -= qty_to_buy
                        else:
                            logging.warning(f"与用户订单 {target_price} 成交失败")

                    except Exception as e:
                        logging.error(f"处理用户订单 {target_price} 时发生异常: {e}")
                        continue


            if final_buy_amount <= 1e-6:
                logging.warning("最终购买量为0，本次不执行下单。")
                order['status'] = 'skipped'
                order['error'] = 'Final buy amount is zero after filtering and trading'
                return False

            order['status'] = 'completed'
            order['result'] = 'Aggregated trade' # 结果是多次交易的聚合
            order['executed_time'] = datetime.now(beijing_tz)
            order['executed_amount'] = final_buy_amount
            self.total_executed += final_buy_amount
            self.orders_executed += 1
            
            logging.info(f"订单聚合执行成功: 总金额={final_buy_amount}")
            return True
                
        except Exception as e:
            order['status'] = 'failed'
            order['error'] = str(e)
            logging.error(f"订单执行异常: {str(e)}\n{traceback.format_exc()}")
            return False
    
    def save_execution_log(self):
        """保存执行日志到文件"""
        try:
            log_file = f"byb_order_execution_{datetime.now(beijing_tz).strftime('%Y%m%d_%H%M%S')}.json"
            log_data = {
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': datetime.now(beijing_tz).isoformat(),
                'total_executed': self.total_executed,
                'orders_executed': self.orders_executed,
                'total_orders': len(self.order_schedule),
                'execution_log': []
            }
            
            for order in self.order_schedule:
                log_entry = order.copy()
                # 转换时间为字符串
                if 'scheduled_time' in log_entry:
                    log_entry['scheduled_time'] = log_entry['scheduled_time'].isoformat()
                if 'executed_time' in log_entry:
                    log_entry['executed_time'] = log_entry['executed_time'].isoformat()
                log_data['execution_log'].append(log_entry)
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"执行日志已保存到: {log_file}")
            
        except Exception as e:
            logging.error(f"保存执行日志失败: {str(e)}")

    def calculate_dynamic_max_interval(self, days: float, estimated_orders: int, min_interval: int = 60) -> int:
        """计算动态最大时间间隔
        
        Args:
            days: 总时间范围（天）
            estimated_orders: 预估订单数量
            min_interval: 最小时间间隔（秒）
            
        Returns:
            动态计算的最大时间间隔（秒）
        """
        try:
            # 计算总可用时间（秒）
            total_seconds = int(days * 24 * 3600)
            
            # 基础计算：总时间除以订单数，确保订单能均匀分布
            base_interval = total_seconds / max(estimated_orders, 1)
            
            # 激进优化：针对短期交易使用更直接的时间控制策略
            if days <= 1:
                # 1天内：使用基于理论间隔的直接计算，减少随机性
                # 目标：确保实际间隔接近理论间隔
                target_interval = base_interval
                
                # 允许的偏差范围：±15%
                max_deviation = 0.15
                dynamic_max_interval = int(target_interval * (1 + max_deviation))
                
                # 确保不超过合理上限
                max_max_interval = min(total_seconds // 6, 3600)  # 最大1小时
                dynamic_max_interval = min(dynamic_max_interval, max_max_interval)
                
                # 确保最小间隔的2倍
                dynamic_max_interval = max(min_interval * 2, dynamic_max_interval)
                
                logging.info(f"短期交易(≤1天)最大间隔计算:")
                logging.info(f"  理论间隔: {target_interval:.1f}秒")
                logging.info(f"  允许偏差: ±{max_deviation:.1%}")
                logging.info(f"  最终最大间隔: {dynamic_max_interval}秒")
                
                return dynamic_max_interval
                
            elif days <= 3:
                # 1-3天：适度随机性，但更接近理论值
                random_factor = random.uniform(0.9, 1.1)  # ±10%
                distribution_factor = random.uniform(1.0, 1.3)  # 减少分布不均匀性
                market_activity_factor = random.uniform(0.95, 1.05)  # 减少市场活跃度影响
                volatility_factor = random.uniform(0.95, 1.05)  # 减少波动性
            elif days <= 7:
                # 3-7天：正常随机性
                random_factor = random.uniform(0.8, 1.2)  # ±20%
                distribution_factor = random.uniform(1.05, 1.6)  # 正常分布不均匀性
                market_activity_factor = random.uniform(0.9, 1.1)  # 正常市场活跃度影响
                volatility_factor = random.uniform(0.9, 1.1)  # 正常波动性
            else:
                # 7天以上：保持原有随机性
                random_factor = random.uniform(0.7, 1.1)
                distribution_factor = random.uniform(1.1, 2.2)
                market_activity_factor = random.uniform(0.9, 1.4)
                volatility_factor = random.uniform(0.8, 1.3)
            
            # 基于当前时间的微调因子（增加时间相关性）
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                # 交易活跃时段，间隔稍短
                time_factor = random.uniform(0.9, 1.1)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                # 午休或晚餐时段，间隔稍长
                time_factor = random.uniform(1.1, 1.3)
            else:
                # 其他时段，正常间隔
                time_factor = random.uniform(0.95, 1.15)
            
            # 综合计算动态最大间隔
            dynamic_max_interval = int(base_interval * random_factor * distribution_factor * 
                                     market_activity_factor * volatility_factor * time_factor)
            
            # 优化：根据时间范围调整上下限策略
            min_max_interval = min_interval * 2  # 最小最大间隔为最小间隔的2倍
            
            # 根据总时间动态调整最大间隔上限，优化短期交易
            if days <= 1:
                # 1天内：更严格的上限控制
                max_max_interval = min(total_seconds // 8, 2700)  # 最大45分钟
            elif days <= 3:
                # 1-3天：适度上限控制
                max_max_interval = min(total_seconds // 6, 5400)  # 最大1.5小时
            elif days <= 7:
                # 3-7天：正常上限控制
                max_max_interval = min(total_seconds // 4, 14400)
            else:
                # 7天以上：保持原有上限
                max_max_interval = min(total_seconds // 3, 21600)
            
            # 确保在合理范围内
            dynamic_max_interval = max(min_max_interval, min(dynamic_max_interval, max_max_interval))
            
            # 优化：根据时间范围调整最终微调范围
            if days <= 1:
                # 1天内：减少微调范围，提高精度
                final_adjustment = random.uniform(0.98, 1.02)
            elif days <= 3:
                # 1-3天：适度微调
                final_adjustment = random.uniform(0.96, 1.04)
            else:
                # 其他情况：正常微调
                final_adjustment = random.uniform(0.95, 1.05)
            
            dynamic_max_interval = int(dynamic_max_interval * final_adjustment)
            
            logging.info(f"动态最大间隔计算详情:")
            logging.info(f"  总时间: {total_seconds}秒 ({days}天)")
            logging.info(f"  预估订单: {estimated_orders}")
            logging.info(f"  基础间隔: {base_interval:.1f}秒")
            logging.info(f"  随机因子: {random_factor:.3f}")
            logging.info(f"  分布因子: {distribution_factor:.3f}")
            logging.info(f"  市场活跃度因子: {market_activity_factor:.3f}")
            logging.info(f"  波动性因子: {volatility_factor:.3f}")
            logging.info(f"  时间因子: {time_factor:.3f} (当前小时: {current_hour})")
            logging.info(f"  最终调整: {final_adjustment:.3f}")
            logging.info(f"  最终最大间隔: {dynamic_max_interval}秒")
            
            return dynamic_max_interval
            
        except Exception as e:
            logging.error(f"计算动态最大间隔失败: {str(e)}")
            # 返回默认值
            return 3600

    def calculate_dynamic_min_interval(self, days: float, estimated_orders: int, max_interval: int = 3600) -> int:
        """计算动态最小时间间隔，确保交易总时长不会偏离days太多
        
        Args:
            days: 总时间范围（天）
            estimated_orders: 预估订单数量
            max_interval: 最大时间间隔（秒）
            
        Returns:
            动态计算的最小时间间隔（秒）
        """
        try:
            # 计算总可用时间（秒）
            total_seconds = int(days * 24 * 3600)
            
            # 基础计算：总时间除以订单数，确保订单能均匀分布
            base_interval = total_seconds / max(estimated_orders, 1)
            
            # 优化：根据时间范围调整基础最小间隔比例，减少短期交易偏差
            if days <= 1:
                # 1天内：大幅增加基础最小间隔比例，确保时间控制精度
                base_min_ratio = random.uniform(0.6, 0.8)  # 60%-80%
            elif days <= 3:
                # 1-3天：适度增加基础最小间隔比例
                base_min_ratio = random.uniform(0.5, 0.7)  # 50%-70%
            elif days <= 7:
                # 3-7天：正常基础最小间隔比例
                base_min_ratio = random.uniform(0.4, 0.6)  # 40%-60%
            else:
                # 7天以上：保持原有比例
                base_min_ratio = random.uniform(0.3, 0.5)  # 30%-50%
            
            base_min_interval = base_interval * base_min_ratio
            
            # 优化：根据时间范围调整随机因子范围
            if days <= 1:
                # 1天内：大幅减少随机性，提高精度
                distribution_factor = random.uniform(0.98, 1.02)  # ±2%
                time_factor = random.uniform(1.0, 1.1)  # 减少时间因子影响
                hour_factor = random.uniform(0.99, 1.01)  # 几乎不调整
                order_factor = random.uniform(0.99, 1.01)  # 几乎不调整
            elif days <= 3:
                # 1-3天：适度减少随机性
                distribution_factor = random.uniform(0.95, 1.05)  # ±5%
                time_factor = random.uniform(0.98, 1.15)  # 适度时间因子影响
                hour_factor = random.uniform(0.95, 1.05)  # 适度调整
                order_factor = random.uniform(0.95, 1.05)  # 适度调整
            elif days <= 7:
                # 3-7天：正常随机性
                distribution_factor = random.uniform(0.9, 1.1)  # ±10%
                time_factor = random.uniform(0.95, 1.15)  # 正常时间因子影响
                hour_factor = random.uniform(0.9, 1.1)  # 正常调整
                order_factor = random.uniform(0.9, 1.1)  # 正常调整
            else:
                # 7天以上：保持原有随机性
                distribution_factor = random.uniform(0.8, 1.2)
                time_factor = random.uniform(0.9, 1.2)
                hour_factor = random.uniform(0.95, 1.15)
                order_factor = random.uniform(0.9, 1.1)
            
            # 基于当前时间的微调因子
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                # 交易活跃时段，最小间隔可以稍短
                hour_factor *= random.uniform(0.9, 1.1)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                # 休息时段，最小间隔稍长
                hour_factor *= random.uniform(1.1, 1.3)
            else:
                # 其他时段，正常范围
                hour_factor *= random.uniform(0.95, 1.15)
            
            # 订单数量调整因子：订单越多，最小间隔越短
            if estimated_orders > 50:
                order_factor *= random.uniform(0.7, 0.9)
            elif estimated_orders > 20:
                order_factor *= random.uniform(0.8, 1.0)
            elif estimated_orders > 10:
                order_factor *= random.uniform(0.9, 1.1)
            else:
                order_factor *= random.uniform(1.0, 1.2)
            
            # 综合计算动态最小间隔
            dynamic_min_interval = int(base_min_interval * distribution_factor * time_factor * 
                                     hour_factor * order_factor)
            
            # 优化：根据时间范围调整上下限策略
            absolute_min_interval = 30  # 绝对最小间隔30秒
            
            # 根据总时间动态调整最小间隔上限，优化短期交易
            if days <= 1:
                # 1天内：更严格的上限控制
                max_min_interval = min(total_seconds // 15, 1200)  # 最大20分钟
            elif days <= 3:
                # 1-3天：适度上限控制
                max_min_interval = min(total_seconds // 12, 2400)  # 最大40分钟
            elif days <= 7:
                # 3-7天：正常上限控制
                max_min_interval = min(total_seconds // 8, 7200)
            else:
                # 7天以上：保持原有上限
                max_min_interval = min(total_seconds // 6, 10800)
            
            # 确保最小间隔不超过最大间隔的50%
            max_min_interval = min(max_min_interval, max_interval * 0.5)
            
            # 确保在合理范围内
            dynamic_min_interval = max(absolute_min_interval, min(dynamic_min_interval, max_min_interval))
            
            # 优化：根据时间范围调整最终微调范围
            if days <= 1:
                # 1天内：减少微调范围，提高精度
                final_adjustment = random.uniform(0.98, 1.02)
            elif days <= 3:
                # 1-3天：适度微调
                final_adjustment = random.uniform(0.97, 1.03)
            else:
                # 其他情况：正常微调
                final_adjustment = random.uniform(0.97, 1.03)
            
            dynamic_min_interval = int(dynamic_min_interval * final_adjustment)
            
            logging.info(f"动态最小间隔计算详情:")
            logging.info(f"  总时间: {total_seconds}秒 ({days}天)")
            logging.info(f"  预估订单: {estimated_orders}")
            logging.info(f"  基础间隔: {base_interval:.1f}秒")
            logging.info(f"  基础最小间隔比例: {base_min_ratio:.3f}")
            logging.info(f"  基础最小间隔: {base_min_interval:.1f}秒")
            logging.info(f"  分布因子: {distribution_factor:.3f}")
            logging.info(f"  时间因子: {time_factor:.3f}")
            logging.info(f"  小时因子: {hour_factor:.3f} (当前小时: {current_hour})")
            logging.info(f"  订单因子: {order_factor:.3f}")
            logging.info(f"  最终调整: {final_adjustment:.3f}")
            logging.info(f"  最终最小间隔: {dynamic_min_interval}秒")
            
            return dynamic_min_interval
            
        except Exception as e:
            logging.error(f"计算动态最小间隔失败: {str(e)}")
            # 返回默认值
            return 60

    def estimate_order_count(self, total_amount: float, each_amount: float, current_price: float) -> int:
        """估算订单数量
        
        Args:
            total_amount: 总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            
        Returns:
            预估订单数量
        """
        try:
            # 基础订单数量计算
            if each_amount and each_amount > 0:
                # 如果指定了each_amount，按此计算
                base_orders = total_amount / each_amount
            else:
                # 否则使用默认配置估算
                symbol_config = get_symbol_config()
                avg_order_size = (symbol_config["min_order_size"] + symbol_config["max_order_size"]) / 2
                base_orders = total_amount / (avg_order_size * current_price)
            
            # 优化：根据总金额调整随机因子范围，提高短期交易精度
            if total_amount <= 2000:
                # 小额订单：减少随机性，提高精度
                base_variation = random.uniform(0.9, 1.1)  # ±10%
                amount_factor = random.uniform(1.0, 1.1)   # 小额订单稍微分散
            elif total_amount <= 5000:
                # 中小额订单：适度随机性
                base_variation = random.uniform(0.85, 1.15)  # ±15%
                amount_factor = random.uniform(0.95, 1.15)
            elif total_amount <= 15000:
                # 中等金额：正常随机性
                base_variation = random.uniform(0.8, 1.2)    # ±20%
                amount_factor = random.uniform(0.9, 1.1)
            else:
                # 大额订单：保持原有随机性
                base_variation = random.uniform(0.8, 1.2)
                amount_factor = random.uniform(0.9, 1.1)
            
            # 基于当前时间的调整因子
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                # 交易活跃时段，订单数量可能稍多
                time_factor = random.uniform(1.05, 1.15)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                # 休息时段，订单数量可能稍少
                time_factor = random.uniform(0.9, 1.05)
            else:
                # 其他时段，正常范围
                time_factor = random.uniform(0.95, 1.1)
            
            # 市场波动性因子
            volatility_factor = random.uniform(0.85, 1.15)
            
            # 基于星期几的微调（模拟周内交易模式）
            current_weekday = datetime.now(beijing_tz).weekday()
            if current_weekday in [0, 4]:  # 周一、周五
                weekday_factor = random.uniform(1.05, 1.15)  # 周初和周末可能更活跃
            elif current_weekday in [2, 3]:  # 周三、周四
                weekday_factor = random.uniform(0.95, 1.05)  # 周中相对稳定
            else:
                weekday_factor = random.uniform(0.9, 1.1)  # 其他时间正常
            
            # 综合计算最终订单数量
            final_orders = base_orders * base_variation * amount_factor * time_factor * volatility_factor * weekday_factor
            
            # 确保订单数量在合理范围内
            estimated_orders = max(1, int(final_orders))
            
            # 优化：根据总金额调整最终微调范围
            if total_amount <= 2000:
                # 小额订单：减少微调范围，提高精度
                final_adjustment = random.uniform(0.98, 1.02)  # ±2%
            elif total_amount <= 5000:
                # 中小额订单：适度微调
                final_adjustment = random.uniform(0.97, 1.03)  # ±3%
            else:
                # 其他情况：正常微调
                final_adjustment = random.uniform(0.97, 1.03)
            
            estimated_orders = max(1, int(estimated_orders * final_adjustment))
            
            logging.info(f"订单数量估算详情:")
            logging.info(f"  总金额: {total_amount:,.2f}")
            logging.info(f"  单次最大: {each_amount if each_amount else '默认配置'}")
            logging.info(f"  当前价格: {current_price:.6f}")
            logging.info(f"  基础订单数: {base_orders:.1f}")
            logging.info(f"  基础变化: {base_variation:.3f}")
            logging.info(f"  金额因子: {amount_factor:.3f}")
            logging.info(f"  时间因子: {time_factor:.3f} (当前小时: {current_hour})")
            logging.info(f"  波动因子: {volatility_factor:.3f}")
            logging.info(f"  星期因子: {weekday_factor:.3f} (星期{current_weekday + 1})")
            logging.info(f"  最终调整: {final_adjustment:.3f}")
            logging.info(f"  最终预估订单数: {estimated_orders}")
            
            return estimated_orders
            
        except Exception as e:
            logging.error(f"估算订单数量失败: {str(e)}")
            return 10  # 默认返回10个订单

    async def run_algorithm(self):
        """运行下单算法主程序"""
        try:
            # 获取数据库参数
            params = self.get_buy_back_params()

            # 检查状态是否允许执行
            if params.get('status', 0) != 1:
                logging.info(f"参数状态不允许执行: status={params.get('status', 0)}, 需要status=1")
                schedule_lark_message(f"⚠️ 下单算法跳过执行\n参数ID: {params['id']}\n状态: {params.get('status', 0)} (需要为1)", level='warning')
                return

            days = params['days']
            total_amount = params['total_amount']
            each_amount = params.get('each_amount', None)
            param_id = params['id']

            # 获取当前交易对配置和价格
            trading_config = get_trading_config()
            current_price = self.get_current_price()
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()

            # 发送启动通知
            start_msg = (
                f"🚀 智能下单算法启动\n"
                f"交易对: {trading_config['symbol'].upper()}\n"
                f"总时间范围: {days} 天\n"
                f"总下单量: {total_amount:,.2f} {quote_currency.upper()}\n"
                f"当前{base_currency.upper()}价格: {current_price:.6f} {quote_currency.upper()}\n"
            )
            if each_amount:
                each_amount_base = each_amount / current_price
                start_msg += f"单次最大{quote_currency.upper()}: {each_amount:,.2f} {quote_currency.upper()} (约 {each_amount_base:.2f} {base_currency.upper()})\n"
            else:
                start_msg += f"单次量范围: {trading_config['min_order_size']}-{trading_config['max_order_size']} {base_currency.upper()}\n"

            start_msg += (
                f"启动时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(start_msg, level='info')

            # 生成订单计划
            self.order_schedule = self.generate_order_schedule(days, total_amount, each_amount)
            self.start_time = datetime.now(beijing_tz)
            self.is_running = True

            logging.info(f"开始执行订单计划，共 {len(self.order_schedule)} 个订单")

            # 执行订单
            failed_orders = []  # 记录失败的订单
            for i, order in enumerate(self.order_schedule):
                if not self.is_running:
                    logging.info("算法被停止")
                    break

                # 等待到预定时间
                now = datetime.now(beijing_tz)
                if order['scheduled_time'] > now:
                    if i == 0:
                        logging.info(f"第一个订单将在 60 秒后执行")
                        await asyncio.sleep(60)
                    else:
                        wait_seconds = (order['scheduled_time'] - now).total_seconds()
                        logging.info(f"等待 {wait_seconds:.1f} 秒执行第 {i+1} 个订单")
                        await asyncio.sleep(wait_seconds)

                # 执行订单
                success = await self.execute_order(order)
                
                # 记录失败的订单
                if not success:
                    failed_orders.append(order)
                    logging.warning(f"订单执行失败: {order.get('error', '未知错误')}")

                # 记录进度
                progress = (i + 1) / len(self.order_schedule) * 100
                logging.info(f"订单执行进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})")

                # 每10个订单发送一次进度通知
                if (i + 1) % 10 == 0 or not success:
                    base_currency = get_base_currency()
                    progress_msg = (
                        f"📊 下单进度更新\n"
                        f"交易对: {get_current_symbol().upper()}\n"
                        f"进度: {progress:.1f}% ({i+1}/{len(self.order_schedule)})\n"
                        f"已执行金额: {self.total_executed:,.2f} {quote_currency.upper()}\n"
                        f"成功订单: {self.orders_executed}\n"
                        f"失败订单: {len(failed_orders)}\n"
                        f"当前时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                    if not success:
                        progress_msg += f"\n⚠️ 订单执行失败: {order.get('error', '未知错误')}"

                    schedule_lark_message(progress_msg, level='info' if success else 'warning')

                # 短暂休息避免API限制
                await asyncio.sleep(1)

            # 处理失败的订单 - 重新执行或补偿
            if failed_orders:
                logging.info(f"发现 {len(failed_orders)} 个失败订单，开始处理...")
                await self._handle_failed_orders(failed_orders, total_amount, each_amount, current_price, trading_config, base_currency, quote_currency)

            # 最终金额校验
            await self._validate_total_amount(total_amount, param_id, base_currency, quote_currency)

            # 完成通知
            base_currency = get_base_currency()
            completion_msg = (
                f"✅ 智能下单算法完成\n"
                f"交易对: {get_current_symbol().upper()}\n"
                f"总订单数: {len(self.order_schedule)}\n"
                f"成功订单: {self.orders_executed}\n"
                f"失败订单: {len(failed_orders)}\n"
                f"执行金额: {self.total_executed:,.2f} {quote_currency.upper()}\n"
                f"目标金额: {total_amount:,.2f} {quote_currency.upper()}\n"
                f"完成率: {(self.total_executed/total_amount*100) if total_amount > 0 else 0:.1f}%\n"
                f"成功率: {(self.orders_executed/len(self.order_schedule)*100) if self.order_schedule else 0:.1f}%\n"
                f"完成时间: {datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')}"
            )
            schedule_lark_message(completion_msg, level='info')

            # 保存执行日志
            self.save_execution_log()

            # 交易完成后更新数据库参数状态为0
            try:
                self.update_params_status(param_id, 0)
                logging.info(f"交易完成，参数状态已更新为0: ID={param_id}")
                schedule_lark_message(f"📝 参数状态已更新\n参数ID: {param_id}\n状态: 1 → 0 (已完成)", level='info')
            except Exception as e:
                logging.error(f"更新参数状态失败: {str(e)}")
                schedule_lark_message(f"⚠️ 参数状态更新失败\n参数ID: {param_id}\n错误: {str(e)}", level='warning')

        except Exception as e:
            error_msg = f"下单算法执行异常: {str(e)}"
            logging.error(f"{error_msg}\n{traceback.format_exc()}")
            schedule_lark_message(error_msg, level='error')
        finally:
            self.is_running = False

    def stop_algorithm(self):
        """停止算法执行"""
        self.is_running = False
        logging.info("下单算法停止信号已发送")

    def get_status(self) -> Dict:
        """获取算法执行状态"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'total_orders': len(self.order_schedule),
            'orders_executed': self.orders_executed,
            'total_executed': self.total_executed,
            'progress': (self.orders_executed / len(self.order_schedule) * 100) if self.order_schedule and len(self.order_schedule) > 0 else 0
        }

    async def _handle_failed_orders(self, failed_orders: List[Dict], total_amount: float, 
                                   each_amount: float, current_price: float, trading_config: Dict,
                                   base_currency: str, quote_currency: str):
        """处理失败的订单
        
        Args:
            failed_orders: 失败的订单列表
            total_amount: 目标总金额
            each_amount: 单次最大金额
            current_price: 当前价格
            trading_config: 交易配置
            base_currency: 基础货币
            quote_currency: 计价货币
        """
        try:
            logging.info(f"开始处理 {len(failed_orders)} 个失败订单")
            
            # 计算还需要执行的金额
            remaining_amount = total_amount - self.total_executed
            logging.info(f"还需要执行金额: {remaining_amount:,.2f} {quote_currency.upper()}")
            
            if remaining_amount <= 1e-6:
                logging.info("已执行金额已达到目标，无需处理失败订单")
                return
            
            # 策略1：尝试重新执行失败的订单
            retry_success_count = 0
            for order in failed_orders:
                if not self.is_running:
                    break
                    
                logging.info(f"重试执行失败订单: {order['amount']} {quote_currency.upper()}")
                
                # 等待一段时间后重试
                await asyncio.sleep(5)
                
                # 重新执行订单
                success = await self.execute_order(order)
                if success:
                    retry_success_count += 1
                    logging.info(f"重试成功: {order['amount']} {quote_currency.upper()}")
                else:
                    logging.warning(f"重试失败: {order.get('error', '未知错误')}")
            
            logging.info(f"重试结果: {retry_success_count}/{len(failed_orders)} 个订单重试成功")
            
            # 策略2：如果还有未完成的金额，创建补偿订单
            remaining_amount = total_amount - self.total_executed
            if remaining_amount > 1e-6:
                logging.info(f"重试后仍有未完成金额: {remaining_amount:,.2f} {quote_currency.upper()}，创建补偿订单")
                await self._create_compensation_orders(remaining_amount, each_amount, current_price, 
                                                      trading_config, base_currency, quote_currency)
            
        except Exception as e:
            logging.error(f"处理失败订单异常: {str(e)}")
            schedule_lark_message(f"⚠️ 处理失败订单异常\n错误: {str(e)}", level='error')

    async def _create_compensation_orders(self, remaining_amount: float, each_amount: float, 
                                        current_price: float, trading_config: Dict,
                                        base_currency: str, quote_currency: str):
        """创建补偿订单
        
        Args:
            remaining_amount: 剩余需要执行的金额
            each_amount: 单次最大金额
            current_price: 当前价格
            trading_config: 交易配置
            base_currency: 基础货币
            quote_currency: 计价货币
        """
        try:
            logging.info(f"创建补偿订单，剩余金额: {remaining_amount:,.2f} {quote_currency.upper()}")
            
            # 计算补偿订单数量
            if each_amount and each_amount > 0:
                compensation_orders = int(remaining_amount / each_amount)
                if remaining_amount % each_amount > 0:
                    compensation_orders += 1
            else:
                max_order_size = trading_config["max_order_size"]
                compensation_orders = int(remaining_amount / max_order_size)
                if remaining_amount % max_order_size > 0:
                    compensation_orders += 1
            
            logging.info(f"需要创建 {compensation_orders} 个补偿订单")
            
            # 创建补偿订单
            current_time = datetime.now(beijing_tz)
            compensation_success_count = 0
            
            for i in range(compensation_orders):
                if not self.is_running:
                    break
                
                # 计算订单金额
                if each_amount and each_amount > 0:
                    order_amount = min(each_amount, remaining_amount)
                else:
                    order_amount = min(trading_config["max_order_size"], remaining_amount)
                
                # 创建补偿订单
                compensation_order = {
                    'scheduled_time': current_time + timedelta(seconds=i * 60),  # 每分钟一个
                    'amount': order_amount,
                    'symbol': trading_config["symbol"],
                    'side': trading_config["side"],
                    'type': trading_config["type"],
                    'status': 'pending',
                    'is_compensation': True  # 标记为补偿订单
                }
                
                logging.info(f"执行补偿订单 {i+1}: {order_amount:,.2f} {quote_currency.upper()}")
                
                # 执行补偿订单
                success = await self.execute_order(compensation_order)
                if success:
                    compensation_success_count += 1
                    remaining_amount -= order_amount
                    logging.info(f"补偿订单执行成功: {order_amount:,.2f} {quote_currency.upper()}")
                else:
                    logging.warning(f"补偿订单执行失败: {compensation_order.get('error', '未知错误')}")
                
                # 等待一段时间避免API限制
                await asyncio.sleep(2)
            
            logging.info(f"补偿订单执行完成: {compensation_success_count}/{compensation_orders} 个成功")
            
            # 如果补偿订单后仍有未完成金额，发送警告
            remaining_amount = total_amount - self.total_executed
            if remaining_amount > 1e-6:
                # 计算总目标金额（需要从外部传入或通过其他方式获取）
                total_target_amount = self.total_executed + remaining_amount
                warning_msg = (
                    f"⚠️ 补偿订单后仍有未完成金额\n"
                    f"剩余金额: {remaining_amount:,.2f} {quote_currency.upper()}\n"
                    f"完成率: {((total_target_amount - remaining_amount)/total_target_amount*100) if total_target_amount > 0 else 0:.1f}%\n"
                    f"建议手动处理剩余金额"
                )
                schedule_lark_message(warning_msg, level='warning')
                
        except Exception as e:
            logging.error(f"创建补偿订单异常: {str(e)}")
            schedule_lark_message(f"⚠️ 创建补偿订单异常\n错误: {str(e)}", level='error')

    async def _validate_total_amount(self, target_amount: float, param_id: int, 
                                   base_currency: str, quote_currency: str):
        """验证总金额是否达到目标
        
        Args:
            target_amount: 目标金额
            param_id: 参数ID
            base_currency: 基础货币
            quote_currency: 计价货币
        """
        try:
            if target_amount <= 1e-6:
                logging.info("目标金额为0，跳过金额校验")
                return

            completion_rate = (self.total_executed / target_amount) * 100
            deviation = abs(self.total_executed - target_amount)
            deviation_rate = (deviation / target_amount) * 100
            
            logging.info(f"金额校验结果:")
            logging.info(f"  目标金额: {target_amount:,.2f} {quote_currency.upper()}")
            logging.info(f"  实际执行: {self.total_executed:,.2f} {quote_currency.upper()}")
            logging.info(f"  完成率: {completion_rate:.1f}%")
            logging.info(f"  偏差金额: {deviation:,.2f} {quote_currency.upper()}")
            logging.info(f"  偏差率: {deviation_rate:.1f}%")
            
            # 根据完成率决定是否更新数据库状态
            if completion_rate >= 95:  # 完成率≥95%认为基本完成
                try:
                    self.update_params_status(param_id, 0)
                    logging.info(f"交易基本完成，参数状态已更新为0: ID={param_id}")
                    schedule_lark_message(f"📝 参数状态已更新\n参数ID: {param_id}\n状态: 1 → 0 (基本完成)\n完成率: {completion_rate:.1f}%", level='info')
                except Exception as e:
                    logging.error(f"更新参数状态失败: {str(e)}")
                    schedule_lark_message(f"⚠️ 参数状态更新失败\n参数ID: {param_id}\n错误: {str(e)}", level='warning')
            else:
                logging.warning(f"交易完成率不足95%({completion_rate:.1f}%)，参数状态保持为1")
                schedule_lark_message(f"⚠️ 交易完成率不足\n参数ID: {param_id}\n完成率: {completion_rate:.1f}%\n建议检查失败原因", level='warning')
            
            # 发送最终校验结果
            validation_msg = (
                f"📊 交易金额校验结果\n"
                f"交易对: {get_current_symbol().upper()}\n"
                f"目标金额: {target_amount:,.2f} {quote_currency.upper()}\n"
                f"实际执行: {self.total_executed:,.2f} {quote_currency.upper()}\n"
                f"完成率: {completion_rate:.1f}%\n"
                f"偏差率: {deviation_rate:.1f}%\n"
                f"参数ID: {param_id}"
            )
            
            if completion_rate >= 95:
                validation_msg += "\n✅ 交易基本完成"
            elif completion_rate >= 80:
                validation_msg += "\n⚠️ 交易部分完成，建议检查"
            else:
                validation_msg += "\n❌ 交易完成率过低，需要手动处理"
            
            schedule_lark_message(validation_msg, level='info' if completion_rate >= 95 else 'warning')
            
        except Exception as e:
            logging.error(f"金额校验异常: {str(e)}")
            schedule_lark_message(f"⚠️ 金额校验异常\n错误: {str(e)}", level='error')


async def main():
    """主程序入口"""
    try:
        # 创建算法实例
        algorithm = BYBOrderAlgorithm()

        # 运行算法
        await algorithm.run_algorithm()

    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        if 'algorithm' in locals():
            algorithm.stop_algorithm()
    except Exception as e:
        logging.error(f"主程序异常: {str(e)}\n{traceback.format_exc()}")


if __name__ == "__main__":
    print("=" * 60)
    print("BYB智能下单算法".center(60))
    print("=" * 60)
    print("功能: 从数据库获取参数，在指定时间范围内随机分布下单")
    print("下单间隔: 1-60分钟随机")
    print("=" * 60)

    # 运行主程序
    asyncio.run(main())
