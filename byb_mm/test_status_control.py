#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
状态控制功能测试脚本
功能：测试数据库参数status字段的控制逻辑
作者：AI Assistant
创建时间：2025-07-07
"""

import asyncio
import logging
import sys
import traceback
from datetime import datetime
import pytz

# 添加项目路径
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


class MockBYBOrderAlgorithm(BYBOrderAlgorithm):
    """模拟版本的下单算法，用于测试状态控制"""
    
    def __init__(self):
        """初始化模拟算法"""
        # 不调用父类初始化，避免数据库连接
        self.mock_params = {
            'id': 1,
            'days': 7.0,
            'total_amount': 50000.0,
            'each_amount': 500.0,
            'created_at': datetime.now(beijing_tz),
            'status': 1  # 初始状态为可执行
        }
        
        # 算法状态
        self.is_running = False
        self.total_executed = 0
        self.orders_executed = 0
        self.start_time = None
        self.end_time = None
        
        # 订单计划
        self.order_schedule = []
        self.execution_log = []
        
        logging.info("模拟BYB下单算法初始化完成")
    
    def get_buy_back_params(self):
        """模拟获取数据库参数"""
        logging.info(f"获取模拟参数: {self.mock_params}")
        return self.mock_params.copy()
    
    def update_params_status(self, param_id: int, status: int):
        """模拟更新参数状态"""
        if self.mock_params['id'] == param_id:
            old_status = self.mock_params['status']
            self.mock_params['status'] = status
            logging.info(f"模拟更新参数状态: ID={param_id}, {old_status}→{status}")
        else:
            logging.warning(f"参数ID不匹配: 当前={self.mock_params['id']}, 请求={param_id}")
    
    def set_mock_status(self, status: int):
        """设置模拟状态"""
        self.mock_params['status'] = status
        logging.info(f"设置模拟状态: status={status}")
    
    async def simulate_execution(self):
        """模拟执行过程"""
        logging.info("开始模拟执行...")
        await asyncio.sleep(2)  # 模拟执行时间
        
        # 模拟执行结果
        self.orders_executed = 10
        self.total_executed = 5000.0
        self.order_schedule = [{'amount': 500} for _ in range(10)]
        
        logging.info("模拟执行完成")


async def test_status_control():
    """测试状态控制功能"""
    print("\n" + "=" * 60)
    print("测试状态控制功能")
    print("=" * 60)
    
    try:
        algorithm = MockBYBOrderAlgorithm()
        
        # 测试1：status=1，应该允许执行
        print("\n📋 测试1：status=1（允许执行）")
        algorithm.set_mock_status(1)
        
        # 模拟run_algorithm的状态检查部分
        params = algorithm.get_buy_back_params()
        if params.get('status', 0) != 1:
            print(f"❌ 状态检查失败: status={params.get('status', 0)}")
        else:
            print(f"✅ 状态检查通过: status={params.get('status', 0)}")
            await algorithm.simulate_execution()
            
            # 模拟执行完成后更新状态
            algorithm.update_params_status(params['id'], 0)
            print(f"✅ 状态更新完成: 1→0")
        
        # 测试2：status=0，应该跳过执行
        print("\n📋 测试2：status=0（跳过执行）")
        algorithm.set_mock_status(0)
        
        params = algorithm.get_buy_back_params()
        if params.get('status', 0) != 1:
            print(f"✅ 正确跳过执行: status={params.get('status', 0)}")
        else:
            print(f"❌ 应该跳过但没有跳过: status={params.get('status', 0)}")
        
        # 测试3：status=None，应该跳过执行
        print("\n📋 测试3：status=None（跳过执行）")
        algorithm.mock_params.pop('status', None)  # 移除status字段
        
        params = algorithm.get_buy_back_params()
        if params.get('status', 0) != 1:
            print(f"✅ 正确跳过执行: status={params.get('status', 0)}")
        else:
            print(f"❌ 应该跳过但没有跳过: status={params.get('status', 0)}")
        
        print("\n✅ 状态控制功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 状态控制功能测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_status_update():
    """测试状态更新功能"""
    print("\n" + "=" * 60)
    print("测试状态更新功能")
    print("=" * 60)
    
    try:
        algorithm = MockBYBOrderAlgorithm()
        
        # 测试状态更新
        test_cases = [
            {'param_id': 1, 'status': 0, 'expected': True},   # 正确的ID
            {'param_id': 999, 'status': 0, 'expected': False}, # 错误的ID
            {'param_id': 1, 'status': 1, 'expected': True},   # 重新设置为1
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\n📋 测试案例 {i+1}: ID={case['param_id']}, status={case['status']}")
            
            old_status = algorithm.mock_params.get('status', 0)
            
            try:
                algorithm.update_params_status(case['param_id'], case['status'])
                
                if case['expected']:
                    new_status = algorithm.mock_params.get('status', 0)
                    if new_status == case['status']:
                        print(f"✅ 状态更新成功: {old_status}→{new_status}")
                    else:
                        print(f"❌ 状态更新失败: 期望{case['status']}, 实际{new_status}")
                else:
                    print(f"✅ 正确处理错误ID")
                    
            except Exception as e:
                if case['expected']:
                    print(f"❌ 意外异常: {str(e)}")
                else:
                    print(f"✅ 正确抛出异常: {str(e)}")
        
        print("\n✅ 状态更新功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 状态更新功能测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_complete_workflow():
    """测试完整工作流程"""
    print("\n" + "=" * 60)
    print("测试完整工作流程")
    print("=" * 60)
    
    try:
        algorithm = MockBYBOrderAlgorithm()
        
        print("📋 模拟完整的执行流程:")
        
        # 1. 检查初始状态
        params = algorithm.get_buy_back_params()
        print(f"1. 初始状态: status={params.get('status', 0)}")
        
        # 2. 状态检查
        if params.get('status', 0) != 1:
            print(f"2. ❌ 状态不允许执行，跳过")
            return False
        else:
            print(f"2. ✅ 状态允许执行，继续")
        
        # 3. 模拟执行
        print(f"3. 🚀 开始执行...")
        await algorithm.simulate_execution()
        print(f"4. ✅ 执行完成: {algorithm.orders_executed} 订单, {algorithm.total_executed:.2f} BYB")
        
        # 4. 更新状态
        print(f"5. 📝 更新状态...")
        algorithm.update_params_status(params['id'], 0)
        
        # 5. 验证状态
        final_params = algorithm.get_buy_back_params()
        final_status = final_params.get('status', 0)
        print(f"6. 📊 最终状态: status={final_status}")
        
        if final_status == 0:
            print(f"✅ 完整工作流程测试成功")
            return True
        else:
            print(f"❌ 状态未正确更新: 期望0, 实际{final_status}")
            return False
        
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🧪 状态控制功能测试套件")
    print("=" * 80)
    
    test_results = {}
    
    # 运行各项测试
    status_control_success = await test_status_control()
    test_results['status_control'] = status_control_success
    
    status_update_success = await test_status_update()
    test_results['status_update'] = status_update_success
    
    workflow_success = await test_complete_workflow()
    test_results['complete_workflow'] = workflow_success
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    for test_name, success in test_results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name.ljust(20)}: {status}")
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    
    print(f"\n总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！状态控制功能正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查相关实现。")
    
    return test_results


if __name__ == "__main__":
    print("启动状态控制功能测试...")
    asyncio.run(run_all_tests())
