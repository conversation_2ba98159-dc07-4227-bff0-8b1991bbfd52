#!/usr/bin/env python3
"""
测试修改后的时间设置
"""

import sys
sys.path.append("/home/<USER>/usd_mm/")
import random
from wash_trading import generate_random_sleep

def test_time_settings():
    """测试时间设置"""
    print("=== 测试修改后的时间设置 ===")
    
    # 测试睡眠时间
    print("\n1. 测试检查间隔时间（应该在30-60秒之间）:")
    for i in range(5):
        sleep_time = generate_random_sleep()
        print(f"  检查间隔 {i+1}: {sleep_time:.1f} 秒")
        if 30 <= sleep_time <= 60:
            print("    ✅ 时间范围正确")
        else:
            print("    ❌ 时间范围错误")
    
    # 测试无交易阈值
    print("\n2. 测试无交易阈值（应该在60-240秒，即1-4分钟之间）:")
    for i in range(5):
        threshold = random.uniform(60, 240)
        minutes = threshold / 60
        print(f"  阈值 {i+1}: {threshold:.1f} 秒 ({minutes:.1f} 分钟)")
        if 60 <= threshold <= 240:
            print("    ✅ 阈值范围正确")
        else:
            print("    ❌ 阈值范围错误")

if __name__ == "__main__":
    test_time_settings()
