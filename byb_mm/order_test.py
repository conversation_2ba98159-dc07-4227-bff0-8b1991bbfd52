import numpy as np
import pandas as pd
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import con_pri
import time


base_url = "https://openapi.100exdemo.com"
spot_market = Spot()
spot_market.BASE_URL = base_url
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_client.BASE_URL = base_url

# Ask 参数
ask_start = 0.0031
ask_end = 0.13
ask_levels = 200

# Bid 参数（反向对称，向下减小）
bid_start = 0.0030
bid_levels = 100

# 生成线性 ask 价格
ask_prices = np.linspace(ask_start, ask_end, ask_levels)
ask_quantities = np.random.randint(10, 300, size=ask_levels)  # 随机数量

# # 生成线性 bid 价格（递减）
# bid_prices = np.linspace(bid_start - 1e-6, bid_start - 0.001, bid_levels)  # 从略低于0.0031开始向下
# bid_quantities = np.random.randint(10, 300, size=bid_levels)


# spot_client.cancel_all_orders_by_symbol('usdtusd')
# time.sleep(10)
#
# for i in range(len(ask_prices)):
#     client_order_id = f'order_asks_test_{time.time()}'
#     # 直接使用格式化好的参数，减少中间处理
#     params = {
#         "symbol": 'usdtusd',
#         "side": 'sell',
#         "type": 1,
#         "volume": str(round(ask_quantities[i], 3)),
#         "price": str(round(ask_prices[i], 4)),
#         "clientOrderId": client_order_id
#     }
#
#     # 调用API下单
#     order_result = spot_client.new_order(**params)
#     time.sleep(0.1)
#
# spot_client.cancel_all_orders_by_symbol('usdtusd')