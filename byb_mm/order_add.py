import math
import time
import random
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import FormatStrFormatter


class DynamicResistanceSystem:
    def __init__(self, resistance_fund=500000):
        """
        动态压力系统初始化
        :param resistance_fund: 压力资金总额
        """
        self.resistance_fund = resistance_fund
        self.current_peak = None  # 初始峰值未设置
        self.start_price = None  # 起始价格未设置
        self.end_price = None  # 结束价格未设置
        self.breakout_level = None  # 突破阈值
        self.volatility = 0.02  # 初始波动率
        self.orders = []  # 当前订单
        self.history = []  # 历史状态记录

    def initialize(self, market_price):
        """
        根据市价初始化系统
        :param market_price: 当前市场价格
        """
        # 设置初始压力峰值（比市价高20%）
        self.current_peak = round(market_price * 1.20, 2)

        # 向上取整到0.01倍数作为起始点
        self.start_price = math.ceil(market_price * 100) / 100

        # 计算结束价格（峰值+0.1）
        self.end_price = round(self.current_peak + 0.1, 2)

        # 突破阈值（峰值+0.05）
        self.breakout_level = self.current_peak + 0.05

        # 生成初始订单
        self.generate_orders()

        # 记录历史状态
        self.record_state(market_price, "初始化")

    def record_state(self, market_price, action):
        """记录系统状态"""
        self.history.append({
            "timestamp": time.time(),
            "market_price": market_price,
            "resistance_peak": self.current_peak,
            "start_price": self.start_price,
            "end_price": self.end_price,
            "breakout_level": self.breakout_level,
            "action": action
        })

    def update_peak(self, market_price):
        """
        检查并更新压力峰值
        :param market_price: 当前市场价格
        :return: 如果更新了峰值返回True，否则False
        """
        # 如果价格突破阈值
        if market_price > self.breakout_level:
            # 计算新的压力峰值（比当前市价高20%）
            new_peak = round(market_price * 1.20, 2)

            # 更新系统参数
            self.current_peak = new_peak
            self.start_price = math.ceil(market_price * 100) / 100
            self.end_price = round(new_peak + 0.1, 2)
            self.breakout_level = new_peak + 0.05

            # 生成新订单
            self.generate_orders()

            # 记录状态
            self.record_state(market_price, f"突破更新至{new_peak}")
            return True

        # 如果价格接近当前峰值（90%位置）
        elif market_price > self.current_peak * 0.90:
            # 调整压力分布但不改变峰值
            self.adjust_distribution(market_price)
            return False

        return False

    def adjust_distribution(self, market_price):
        """调整压力分布（不改变峰值）"""
        # 计算市价到峰值的距离
        distance_to_peak = self.current_peak - market_price

        # 根据距离调整标准差（距离越小，分布越集中）
        std_dev = max(0.03, min(0.08, distance_to_peak * 0.5))

        # 重新生成订单
        self.generate_orders(std_dev)

        # 记录状态
        self.record_state(market_price, f"分布调整(std_dev={std_dev:.3f})")

    def generate_orders(self, std_dev=0.05):
        """
        生成动态压力订单
        :param std_dev: 高斯分布的标准差
        """
        # 创建价格档位（0.01步长）
        num_levels = int((self.end_price - self.start_price) * 100) + 1
        prices = [round(self.start_price + i * 0.01, 2) for i in range(num_levels)]

        # 高斯权重分布（峰值在current_peak）
        orders = []
        total_weight = 0

        for price in prices:
            # 高斯分布权重
            exponent = -((price - self.current_peak) ** 2) / (2 * (std_dev ** 2))
            weight = math.exp(exponent)
            orders.append({"price": price, "weight": weight})
            total_weight += weight

        # 分配资金
        self.orders = []
        for order in orders:
            amount = (order["weight"] / total_weight) * self.resistance_fund
            self.orders.append({
                "price": order["price"],
                "amount": round(amount, 2),
                "type": "sell",
                "weight": order["weight"],
                "std_dev": std_dev
            })

    def get_orders(self):
        """获取当前订单"""
        return self.orders

    def visualize_orders(self, market_price=None):
        """可视化当前订单分布"""
        if not self.orders:
            print("没有可用的订单数据")
            return

        df = pd.DataFrame(self.orders)
        df = df.sort_values('price')

        plt.figure(figsize=(14, 7))

        # 绘制订单金额
        plt.subplot(1, 2, 1)
        plt.bar(df['price'], df['amount'], width=0.005, color='salmon')
        plt.axvline(x=self.current_peak, color='r', linestyle='--', alpha=0.7)

        if market_price:
            plt.axvline(x=market_price, color='g', linestyle='-', linewidth=2)

        plt.title(f'压力订单分布 (峰值: {self.current_peak})')
        plt.xlabel('价格')
        plt.ylabel('金额 (USDT)')
        plt.grid(True, alpha=0.3)

        # 绘制订单权重
        plt.subplot(1, 2, 2)
        plt.plot(df['price'], df['weight'], 'o-', color='darkred')
        plt.axvline(x=self.current_peak, color='r', linestyle='--', alpha=0.7)

        if market_price:
            plt.axvline(x=market_price, color='g', linestyle='-', linewidth=2)

        plt.title(f'订单权重分布 (标准差: {self.orders[0]["std_dev"]:.3f})')
        plt.xlabel('价格')
        plt.ylabel('权重')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def history_report(self):
        """生成历史报告"""
        if not self.history:
            print("没有历史记录")
            return

        df = pd.DataFrame(self.history)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

        plt.figure(figsize=(14, 10))

        # 价格历史
        plt.subplot(2, 1, 1)
        plt.plot(df['timestamp'], df['market_price'], 'o-', label='市场价')
        plt.plot(df['timestamp'], df['resistance_peak'], 's-', label='压力峰值')
        plt.plot(df['timestamp'], df['breakout_level'], '^--', label='突破阈值')

        plt.title('市场价格与压力峰值变化')
        plt.xlabel('时间')
        plt.ylabel('价格')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.gca().xaxis.set_major_formatter(FormatStrFormatter('%.4f'))

        # 系统参数
        plt.subplot(2, 1, 2)
        plt.plot(df['timestamp'], df['start_price'], 'o-', label='起始价格')
        plt.plot(df['timestamp'], df['end_price'], 's-', label='结束价格')

        plt.title('压力范围变化')
        plt.xlabel('时间')
        plt.ylabel('价格')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.gca().xaxis.set_major_formatter(FormatStrFormatter('%.4f'))

        plt.tight_layout()
        plt.show()

        # 打印历史记录
        print("\n系统历史记录:")
        print(df[['timestamp', 'action', 'market_price', 'resistance_peak']])


class SupportSystem:
    def __init__(self, support_fund=1500000):
        """
        支撑系统初始化
        :param support_fund: 支撑资金总额
        """
        self.support_fund = support_fund
        self.orders = []

    def generate_orders(self):
        """
        生成支撑买单
        :return: 支撑订单列表
        """
        # 价格档位：0.15 → 0.01 → 0.001
        prices = [round(x * 0.01, 2) for x in range(15, 0, -1)] + [0.001]

        # 三区段权重函数
        def support_weight(p):
            if p >= 0.1:  # 高价区
                return math.exp(-3.0 * (p - 0.1))
            elif p >= 0.01:  # 中价区
                return math.exp(-0.35 * (0.1 - p))
            else:  # 终极支撑
                return 1.5

        # 计算权重
        weights = [support_weight(p) for p in prices]
        total_weight = sum(weights)

        # 生成订单
        self.orders = []
        for i, price in enumerate(prices):
            self.orders.append({
                "price": price,
                "amount": round((weights[i] / total_weight) * self.support_fund, 2),
                "type": "buy",
                "weight": round(weights[i], 4)
            })

        return self.orders

    def get_orders(self):
        """获取当前订单"""
        return self.orders

    def visualize_orders(self):
        """可视化支撑订单"""
        if not self.orders:
            print("没有可用的订单数据")
            return

        df = pd.DataFrame(self.orders)
        df = df.sort_values('price', ascending=False)

        plt.figure(figsize=(14, 7))
        plt.bar(df['price'], df['amount'], width=0.005, color='lightgreen')
        plt.axvline(x=0.1, color='g', linestyle='--', alpha=0.7)

        plt.title('支撑订单分布')
        plt.xlabel('价格')
        plt.ylabel('金额 (USDT)')
        plt.grid(True, alpha=0.3)

        # 添加金额标签
        for i, row in df.iterrows():
            if row['amount'] > 100000:  # 只显示大额订单
                plt.text(row['price'], row['amount'] + 5000,
                         f"{row['amount'] / 1000:.1f}K",
                         ha='center', fontsize=9)

        plt.tight_layout()
        plt.show()


class MarketSimulator:
    def __init__(self, initial_price=0.1577):
        self.price = initial_price
        self.volatility = 0.02  # 初始波动率
        self.trend_strength = 0  # 趋势强度 (-1 到 1)
        self.price_history = []
        self.volatility_history = []

    def next_price(self):
        """生成下一个价格"""
        # 随机波动
        random_change = random.uniform(-self.volatility, self.volatility)

        # 趋势影响
        trend_effect = self.trend_strength * self.volatility / 2

        # 计算新价格
        new_price = self.price * (1 + random_change + trend_effect)

        # 更新波动率（价格变化大则波动率增加）
        price_change = abs(new_price - self.price) / self.price
        self.volatility = min(0.15, max(0.01, self.volatility * 0.9 + price_change * 2))

        # 更新趋势强度
        if price_change > 0.03:
            self.trend_strength = min(1.0, self.trend_strength + 0.1)
        else:
            self.trend_strength = max(-1.0, self.trend_strength * 0.95)

        self.price = new_price
        self.price_history.append(self.price)
        self.volatility_history.append(self.volatility)

        return round(self.price, 4)

    def plot_history(self):
        """绘制价格历史"""
        plt.figure(figsize=(14, 7))

        # 价格历史
        plt.subplot(2, 1, 1)
        plt.plot(self.price_history, 'b-')
        plt.title('模拟价格走势')
        plt.xlabel('时间')
        plt.ylabel('价格')
        plt.grid(True, alpha=0.3)

        # 波动率历史
        plt.subplot(2, 1, 2)
        plt.plot(self.volatility_history, 'r-')
        plt.title('市场波动率')
        plt.xlabel('时间')
        plt.ylabel('波动率')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()


def main_simulation():
    """主模拟函数"""
    print("=" * 60)
    print("动态压力峰值市场深度系统")
    print("=" * 60)

    # 初始化系统
    support_system = SupportSystem(support_fund=1500000)
    resistance_system = DynamicResistanceSystem(resistance_fund=500000)
    market_simulator = MarketSimulator(initial_price=0.1577)

    # 初始市价
    current_price = market_simulator.next_price()
    print(f"初始市场价格: {current_price}")

    # 初始化系统
    support_system.generate_orders()
    resistance_system.initialize(current_price)

    # 可视化初始状态
    print("\n初始支撑订单:")
    support_system.visualize_orders()

    print("\n初始压力订单:")
    resistance_system.visualize_orders(current_price)

    # 模拟市场变化
    print("\n开始市场模拟...")
    for i in range(100):  # 模拟100个周期
        current_price = market_simulator.next_price()
        print(f"周期 {i + 1}: 价格 = {current_price:.4f}, 波动率 = {market_simulator.volatility:.4f}")

        # 更新压力系统
        resistance_system.update_peak(current_price)

        # 每20个周期可视化一次
        if (i + 1) % 20 == 0:
            resistance_system.visualize_orders(current_price)

    # 结束模拟
    print("\n模拟结束")
    market_simulator.plot_history()
    resistance_system.history_report()

    # 最终订单可视化
    print("\n最终压力订单分布:")
    resistance_system.visualize_orders(current_price)

    print("\n最终支撑订单分布:")
    support_system.visualize_orders()


if __name__ == "__main__":
    main_simulation()