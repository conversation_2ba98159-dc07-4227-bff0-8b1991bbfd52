#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试each_amount修复的脚本
验证USDT金额正确转换为BYB数量
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from byb_order_algorithm import BYBOrderAlgorithm
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_price_conversion():
    """测试价格转换功能"""
    print("=" * 60)
    print("测试价格转换功能")
    print("=" * 60)
    
    algorithm = BYBOrderAlgorithm()
    
    try:
        # 获取当前BYB价格
        current_price = algorithm.get_current_byb_price()
        print(f"当前BYB价格: {current_price:.6f} USDT")
        
        # 测试不同USDT金额的转换
        test_amounts = [50, 100, 200, 500, 1000]
        
        print("\nUSDT金额 -> BYB数量转换:")
        print("-" * 30)
        for usdt_amount in test_amounts:
            byb_amount = usdt_amount / current_price
            print(f"{usdt_amount:>4} USDT -> {byb_amount:>8.2f} BYB")
        
        return current_price
        
    except Exception as e:
        print(f"价格转换测试失败: {str(e)}")
        return None

def test_order_generation():
    """测试订单生成功能"""
    print("\n" + "=" * 60)
    print("测试订单生成功能")
    print("=" * 60)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    test_cases = [
        {"days": 1.0, "total_amount": 1000.0, "each_amount": 100.0, "desc": "1天，1000 BYB，单次最大100 USDT"},
        {"days": 0.5, "total_amount": 500.0, "each_amount": 50.0, "desc": "0.5天，500 BYB，单次最大50 USDT"},
        {"days": 2.0, "total_amount": 2000.0, "each_amount": 200.0, "desc": "2天，2000 BYB，单次最大200 USDT"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {case['desc']}")
        print("-" * 50)
        
        try:
            schedule = algorithm.generate_order_schedule(
                case["days"], 
                case["total_amount"], 
                case["each_amount"]
            )
            
            if schedule:
                # 统计信息
                total_scheduled = sum(order['amount'] for order in schedule)
                min_order = min(order['amount'] for order in schedule)
                max_order = max(order['amount'] for order in schedule)
                avg_order = total_scheduled / len(schedule)
                
                print(f"生成订单数: {len(schedule)}")
                print(f"计划总金额: {total_scheduled:.2f} BYB")
                print(f"目标总金额: {case['total_amount']:.2f} BYB")
                print(f"差异: {abs(total_scheduled - case['total_amount']):.2f} BYB")
                print(f"订单大小范围: {min_order:.2f} - {max_order:.2f} BYB")
                print(f"平均订单大小: {avg_order:.2f} BYB")
                
                # 验证单次最大限制
                current_price = algorithm.get_current_byb_price()
                max_allowed_byb = case["each_amount"] / current_price
                print(f"单次最大限制: {case['each_amount']} USDT = {max_allowed_byb:.2f} BYB")
                
                # 检查是否有订单超过限制
                over_limit = [order for order in schedule if order['amount'] > max_allowed_byb * 1.01]  # 允许1%误差
                if over_limit:
                    print(f"⚠️ 发现 {len(over_limit)} 个订单超过单次限制")
                    for order in over_limit[:3]:  # 只显示前3个
                        print(f"   超限订单: {order['amount']:.2f} BYB")
                else:
                    print("✅ 所有订单都在单次限制内")
                    
            else:
                print("❌ 未生成任何订单")
                
        except Exception as e:
            print(f"❌ 测试案例失败: {str(e)}")
            import traceback
            traceback.print_exc()

def test_parameter_display():
    """测试参数显示功能"""
    print("\n" + "=" * 60)
    print("测试参数显示功能")
    print("=" * 60)
    
    try:
        algorithm = BYBOrderAlgorithm()
        
        # 模拟数据库参数
        mock_params = {
            'id': 1,
            'days': 1.0,
            'total_amount': 1000.0,
            'each_amount': 100.0,  # USDT金额
            'created_at': '2025-07-08 10:00:00',
            'status': 1
        }
        
        current_price = algorithm.get_current_byb_price()
        each_amount_byb = mock_params['each_amount'] / current_price
        
        print("数据库参数显示:")
        print("-" * 30)
        print(f"参数ID: {mock_params['id']}")
        print(f"执行天数: {mock_params['days']} 天")
        print(f"总下单量: {mock_params['total_amount']:,.2f} BYB")
        print(f"单次最大USDT: {mock_params['each_amount']:,.2f} USDT")
        print(f"当前BYB价格: {current_price:.6f} USDT")
        print(f"单次最大BYB: {each_amount_byb:.2f} BYB")
        print(f"状态: {mock_params['status']}")
        
        print("\n✅ 参数显示格式正确")
        
    except Exception as e:
        print(f"❌ 参数显示测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("BYB回购系统 - each_amount修复测试")
    print("测试USDT金额正确转换为BYB数量")
    print("=" * 60)
    
    try:
        # 运行所有测试
        test_price_conversion()
        test_order_generation()
        test_parameter_display()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        print("\n修改总结:")
        print("1. each_amount现在表示USDT金额，不是BYB数量")
        print("2. 系统会自动获取当前BYB价格进行转换")
        print("3. 订单生成时会将USDT金额转换为对应的BYB数量")
        print("4. 所有日志和通知消息已更新以明确区分USDT和BYB")
        print("5. 如果无法获取实时价格，会使用默认价格0.1 USDT")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
