import requests
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import json
import os
import httpx
import asyncio
import pymysql
import pytz


# 获取平台币（可用coingecko的exchange_token标签或内置名单）
def get_top_exchange_tokens():
    # 这里用常见平台币名单
    return ["BNB", "KCS", "GT", "CRO", "BGB"]

# 获取币种K线数据（以Binance为例，symbol如BTCUSDT，interval=1m）
async def get_binance_klines(symbol, start_time, end_time, client):
    url = "https://api.binance.com/api/v3/klines"
    params = {
        "symbol": symbol + "USDT",
        "interval": "1m",
        "startTime": int(start_time.timestamp() * 1000),
        "endTime": int(end_time.timestamp() * 1000),
        "limit": 1000
    }
    r = await client.get(url, params=params)
    data = r.json()
    df = pd.DataFrame(data, columns=[
        "open_time", "open", "high", "low", "close", "volume",
        "close_time", "quote_asset_volume", "number_of_trades",
        "taker_buy_base_asset_volume", "taker_buy_quote_asset_volume", "ignore"
    ])
    df["timestamp"] = df["open_time"] // 1000
    df["close"] = pd.to_numeric(df["close"], errors='coerce')
    df["quote_asset_volume"] = pd.to_numeric(df["quote_asset_volume"], errors='coerce')
    df = df.dropna(subset=["timestamp", "close", "quote_asset_volume"])
    return df[["timestamp", "close", "quote_asset_volume"]]

# 获取币安支持的现货币种symbol列表

def get_binance_spot_symbols():
    url = "https://api.binance.com/api/v3/exchangeInfo"
    r = requests.get(url)
    data = r.json()
    symbols = set()
    for s in data["symbols"]:
        if s["quoteAsset"] == "USDT" and s["status"] == "TRADING":
            symbols.add(s["baseAsset"].upper())
    return symbols

# 平台币与交易所映射
platform_token_exchange = {
    "KCS": "kucoin",
    "GT": "gate",
    "BGB": "bitget",
    "BNB": "binance",
    "CRO": "crypto",
}


async def get_kucoin_klines(symbol, start_time, end_time, client):
    url = "https://api.kucoin.com/api/v1/market/candles"
    params = {
        "symbol": f"{symbol}-USDT",
        "type": "1min",
        "startAt": int(start_time.timestamp()),
        "endAt": int(end_time.timestamp()),
    }
    r = await client.get(url, params=params)
    data = r.json()
    if data.get("code") != "200000":
        raise Exception(f"KuCoin返回错误: {data.get('msg')}")
    df = pd.DataFrame(data["data"], columns=["time", "open", "close", "high", "low", "volume", "turnover"])
    df["timestamp"] = pd.to_numeric(df["time"], errors='coerce')
    df["close"] = pd.to_numeric(df["close"], errors='coerce')
    df["turnover"] = pd.to_numeric(df["turnover"], errors='coerce')
    df = df.dropna(subset=["timestamp", "close", "turnover"])
    return df[["timestamp", "close", "turnover"]]

async def get_gate_klines(symbol, start_time, end_time, client):
    url = f"https://api.gateio.ws/api/v4/spot/candlesticks"
    params = {
        "currency_pair": f"{symbol}_usdt".lower(),
        "interval": "1m",
        "from": int(start_time.timestamp()),
        "to": int(end_time.timestamp()),
        "limit": 1000
    }
    r = await client.get(url, params=params)
    data = r.json()
    if not isinstance(data, list):
        raise Exception("Gate返回错误")
    df = pd.DataFrame(data, columns=["timestamp", "volume", "close", "high", "low", "open", "turnover", "amount"])
    df["timestamp"] = pd.to_numeric(df["timestamp"], errors='coerce')
    df["close"] = pd.to_numeric(df["close"], errors='coerce')
    df["turnover"] = pd.to_numeric(df["turnover"], errors='coerce')
    df = df.dropna(subset=["timestamp", "close", "turnover"])
    return df[["timestamp", "close", "turnover"]]

async def get_bitget_klines(symbol, start_time, end_time, client):
    url = "https://api.bitget.com/api/spot/v1/market/candles"
    params = {
        "symbol": f"{symbol}USDT_SPBL",
        "period": "1min",
        "after": int(start_time.timestamp() * 1000),
        "before": int(end_time.timestamp() * 1000),
        "limit": 1000
    }
    r = await client.get(url, params=params)
    data = r.json()
    if data.get("code") != "00000":
        raise Exception(f"Bitget返回错误: {data.get('msg')}")
    df = pd.DataFrame(data["data"])
    df["ts"] = pd.to_numeric(df["ts"], errors='coerce') // 1000
    df.rename(columns={"ts": "timestamp"}, inplace=True)
    df["close"] = pd.to_numeric(df["close"], errors='coerce')
    df["usdtVol"] = pd.to_numeric(df["usdtVol"], errors='coerce')
    df = df.dropna(subset=["timestamp", "close", "usdtVol"])
    return df[["timestamp", "close", "usdtVol"]]

async def get_crypto_klines(symbol, start_time, end_time, client):
    url = "https://api.crypto.com/v2/public/get-candlestick"
    params = {
        "instrument_name": f"{symbol}_USDT",
        "timeframe": "1m",
        "start_ts": int(start_time.timestamp() * 1000),
        "end_ts": int(end_time.timestamp() * 1000)
    }
    r = await client.get(url, params=params)
    data = r.json()
    if data.get("code") != 0:
        raise Exception(f"Crypto.com返回错误: {data.get('message')}")
    df = pd.DataFrame(data["result"]["data"])
    df["timestamp"] = pd.to_numeric(df["t"], errors='coerce') // 1000
    df["close"] = pd.to_numeric(df["c"], errors='coerce')
    df["v"] = pd.to_numeric(df["v"], errors='coerce')
    df["c"] = pd.to_numeric(df["c"], errors='coerce')
    # 成交额估算为close*v
    df["quote_usdt"] = df["c"] * df["v"]
    df = df.dropna(subset=["timestamp", "close", "quote_usdt"])
    return df[["timestamp", "close", "quote_usdt"]]

# 读取成交额缓存
def load_turnover_cache(cache_file, minutes):
    cache_valid = False
    turnovers = {}
    cache_data = {}
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            cache_time = cache_data.get('update_time', 0)
            now_ts = int(time.time())
            if now_ts - cache_time < minutes * 60:
                turnovers = cache_data.get('turnovers', {})
                print(f"成交额已从本地缓存读取（{cache_file}，{now_ts - cache_time}秒前）")
                cache_valid = True
        except Exception as e:
            print(f"读取缓存失败: {e}")
    return cache_valid, turnovers

# 保存成交额缓存

def save_turnover_cache(cache_file, turnovers):
    try:
        with open(cache_file, 'w') as f:
            json.dump({
                'update_time': int(time.time()),
                'turnovers': turnovers
            }, f)
        print(f"成交额已写入本地缓存（{cache_file}）")
    except Exception as e:
        print(f"写入缓存失败: {e}")

# 剔除无效币种，区分平台币和主流币

def filter_valid_symbols(all_symbols, turnovers, returns, binance_list, non_binance_platforms):
    valid_symbols = [s for s in all_symbols if float(turnovers.get(s, 0)) > 0 and s in returns]
    platform_valid = [s for s in valid_symbols if s in non_binance_platforms or s == 'BNB']
    mainstream_valid = [s for s in valid_symbols if s in binance_list and s != 'BNB']
    return valid_symbols, platform_valid, mainstream_valid

# 计算组内权重

def calculate_group_weights(platform_valid, mainstream_valid, turnovers, platform_weight, mainstream_weight):
    platform_total = sum(float(turnovers[s]) for s in platform_valid)
    mainstream_total = sum(float(turnovers[s]) for s in mainstream_valid)
    platform_weights = {s: (float(turnovers[s])/platform_total if platform_total > 0 else 1/len(platform_valid)) for s in platform_valid}
    mainstream_weights = {s: (float(turnovers[s])/mainstream_total if mainstream_total > 0 else 1/len(mainstream_valid)) for s in mainstream_valid}
    platform_weights = {s: w*platform_weight for s, w in platform_weights.items()}
    mainstream_weights = {s: w*mainstream_weight for s, w in mainstream_weights.items()}
    return platform_weights, mainstream_weights

# 单币种权重裁剪并归一化

def clip_and_normalize_weights(platform_weights, mainstream_weights, max_single_weight, min_single_weight):
    weight_dict = {}
    weight_dict.update(platform_weights)
    weight_dict.update(mainstream_weights)
    for s in weight_dict:
        if weight_dict[s] > max_single_weight:
            weight_dict[s] = max_single_weight
        elif weight_dict[s] < min_single_weight:
            weight_dict[s] = min_single_weight
    total_weight = sum(weight_dict.values())
    if total_weight > 0:
        weight_dict = {s: w/total_weight for s, w in weight_dict.items()}
    return weight_dict

# 合并加权收益率

def merge_weighted_returns(returns, weight_dict):
    all_returns = pd.DataFrame({s: returns[s] for s in weight_dict.keys()})
    for symbol in weight_dict.keys():
        if symbol not in all_returns.columns:
            all_returns[symbol] = 0
    all_returns = all_returns.fillna(0)
    all_returns["weighted_return"] = sum(all_returns[symbol] * weight_dict.get(symbol, 0) for symbol in weight_dict.keys())
    all_returns = all_returns.reset_index()
    return all_returns

# 生成BNB噪声因子

def generate_bnb_noise(bnb_kline):
    indicators = ['MA_CROSS', 'Z_score', 'MOMENTUM']
    selected_indicator = np.random.choice(indicators)
    if selected_indicator == 'MA_CROSS':
        fast = np.random.choice([5, 10, 15])
        slow = np.random.choice([20, 30, 60])
        ma_fast = bnb_kline['close'].rolling(window=fast).mean()
        ma_slow = bnb_kline['close'].rolling(window=slow).mean()
        indicator_value = (ma_fast - ma_slow) / ma_slow
    elif selected_indicator == 'Z_score':
        period = np.random.choice([10, 20, 30])
        sma = bnb_kline['close'].rolling(window=period).mean()
        std = bnb_kline['close'].rolling(window=period).std()
        zscore = (bnb_kline['close'] - sma) / std
        indicator_value = zscore * (std / bnb_kline['close'])
    else:
        period = np.random.choice([5, 10, 20])
        momentum = bnb_kline['close'].pct_change(period)
        momentum_std = momentum.rolling(period).std().fillna(0)
        indicator_value = momentum + np.random.normal(loc=0, scale=momentum_std, size=len(momentum))
    indicator_series = pd.Series(indicator_value, index=bnb_kline.index).fillna(0)
    return indicator_series.values

# 给加权收益率加噪声

def add_noise_to_returns(all_returns, bnb_noise, noise_weight):
    random_noise = np.random.normal(0, np.std(all_returns['weighted_return']) * 0.2, size=len(bnb_noise))
    total_noise = bnb_noise + random_noise
    all_returns['weighted_return_with_noise'] = all_returns['weighted_return'] + noise_weight * total_noise
    return all_returns

# 输出最新一条结果到csv

def output_latest_row(all_returns):
    latest_row = all_returns.tail(1).copy()
    now = datetime.utcnow()
    one_minute_ago = now - timedelta(minutes=1)
    one_minute_ago = one_minute_ago.replace(second=0, microsecond=0)
    latest_row["timestamp"] = int(one_minute_ago.timestamp())
    # latest_row[["timestamp", "weighted_return_with_noise"]].to_csv("weighted_return_with_noise.csv", index=False)
    # print("已输出最新一条到 weighted_return_with_noise.csv（含BNB CTA噪声）")
    # latest_row[["timestamp", "weighted_return"]].to_csv("weighted_return.csv", index=False)
    # print("已输出最新一条到 weighted_return.csv（无噪声）")


# 主流程
def main(minutes=60, platform_weight=0.4, mainstream_weight=0.6, max_single_weight=0.2, min_single_weight=0.05, noise_weight=0.1):
    # 固定币种名单
    binance_list = ['BNB', 'TRX', 'XRP', 'ETH', 'ADA', 'BTC', 'SOL', 'DOGE']
    non_binance_platforms = ['GT', 'KCS', 'CRO', 'BGB']
    all_symbols = binance_list + non_binance_platforms
    print("币安支持的币种：", binance_list)
    print("非币安平台币：", non_binance_platforms)

    end_time = datetime.utcnow()
    start_time = end_time - timedelta(minutes=minutes+1)

    returns = {}
    turnovers = {}
    cache_file = 'turnover_cache.json'
    cache_valid, turnovers = load_turnover_cache(cache_file, minutes)

    async def fetch_returns():
        returns = {}
        turnovers = {}
        async with httpx.AsyncClient(timeout=15) as client:
            binance_tasks = [get_binance_klines(symbol, start_time, end_time, client) for symbol in binance_list]
            binance_results = await asyncio.gather(*binance_tasks, return_exceptions=True)
            for symbol, df in zip(binance_list, binance_results):
                if isinstance(df, Exception):
                    print(f"{symbol} 获取失败: {df}")
                    continue
                df = df.sort_values("timestamp")
                df["return"] = df["close"].pct_change()
                returns[symbol] = df.set_index("timestamp")["return"]
                turnovers[symbol] = df["quote_asset_volume"].astype(float).sum()
            platform_token_exchange = {
                "KCS": "kucoin",
                "GT": "gate",
                "BGB": "bitget",
                "BNB": "binance",
                "CRO": "crypto",
            }
            platform_tasks = []
            platform_symbols = []
            for symbol in non_binance_platforms:
                exch = platform_token_exchange[symbol]
                if exch == "kucoin":
                    platform_tasks.append(get_kucoin_klines(symbol, start_time, end_time, client))
                elif exch == "gate":
                    platform_tasks.append(get_gate_klines(symbol, start_time, end_time, client))
                elif exch == "bitget":
                    platform_tasks.append(get_bitget_klines(symbol, start_time, end_time, client))
                elif exch == "crypto":
                    platform_tasks.append(get_crypto_klines(symbol, start_time, end_time, client))
                else:
                    continue
                platform_symbols.append(symbol)
            platform_results = await asyncio.gather(*platform_tasks, return_exceptions=True)
            for symbol, df in zip(platform_symbols, platform_results):
                if isinstance(df, Exception):
                    print(f"{symbol} 获取失败: {df}")
                    continue
                df = df.sort_values("timestamp")
                df["return"] = df["close"].pct_change()
                returns[symbol] = df.set_index("timestamp")["return"]
                # turnover列名因平台不同
                if symbol == "KCS":
                    turnovers[symbol] = df["turnover"].sum()
                elif symbol == "GT":
                    turnovers[symbol] = df["turnover"].sum()
                elif symbol == "BGB":
                    turnovers[symbol] = df["usdtVol"].sum()
                elif symbol == "CRO":
                    turnovers[symbol] = df["quote_usdt"].sum()
        return returns, turnovers

    if not cache_valid:
        returns, turnovers = asyncio.run(fetch_returns())
        # 写入缓存
        save_turnover_cache(cache_file, turnovers)
    else:
        async def fetch_returns_only():
            returns = {}
            async with httpx.AsyncClient(timeout=15) as client:
                binance_tasks = [get_binance_klines(symbol, start_time, end_time, client) for symbol in binance_list]
                binance_results = await asyncio.gather(*binance_tasks, return_exceptions=True)
                for symbol, df in zip(binance_list, binance_results):
                    if isinstance(df, Exception):
                        print(f"{symbol} return获取失败: {df}")
                        continue
                    df = df.sort_values("timestamp")
                    df["return"] = df["close"].pct_change()
                    returns[symbol] = df.set_index("timestamp")["return"]
                platform_token_exchange = {
                    "KCS": "kucoin",
                    "GT": "gate",
                    "BGB": "bitget",
                    "BNB": "binance",
                    "CRO": "crypto",
                }
                platform_tasks = []
                platform_symbols = []
                for symbol in non_binance_platforms:
                    exch = platform_token_exchange[symbol]
                    if exch == "kucoin":
                        platform_tasks.append(get_kucoin_klines(symbol, start_time, end_time, client))
                    elif exch == "gate":
                        platform_tasks.append(get_gate_klines(symbol, start_time, end_time, client))
                    elif exch == "bitget":
                        platform_tasks.append(get_bitget_klines(symbol, start_time, end_time, client))
                    elif exch == "crypto":
                        platform_tasks.append(get_crypto_klines(symbol, start_time, end_time, client))
                    else:
                        continue
                    platform_symbols.append(symbol)
                platform_results = await asyncio.gather(*platform_tasks, return_exceptions=True)
                for symbol, df in zip(platform_symbols, platform_results):
                    if isinstance(df, Exception):
                        print(f"{symbol} return获取失败: {df}")
                        continue
                    df = df.sort_values("timestamp")
                    df["return"] = df["close"].pct_change()
                    returns[symbol] = df.set_index("timestamp")["return"]
            return returns
        returns = asyncio.run(fetch_returns_only())

    # 剔除成交额为0或无效的币种
    valid_symbols, platform_valid, mainstream_valid = filter_valid_symbols(all_symbols, turnovers, returns, binance_list, non_binance_platforms)

    # 组内成交额合计
    platform_total = sum(float(turnovers[s]) for s in platform_valid)
    mainstream_total = sum(float(turnovers[s]) for s in mainstream_valid)

    if platform_total == 0 and mainstream_total == 0:
        print("所有币种成交额为0，无法分配权重！")
        return

    # 组内权重
    platform_weights, mainstream_weights = calculate_group_weights(platform_valid, mainstream_valid, turnovers, platform_weight, mainstream_weight)

    # 单币种权重裁剪
    weight_dict = clip_and_normalize_weights(platform_weights, mainstream_weights, max_single_weight, min_single_weight)

    print(f"平台币权重({platform_weight}):", platform_weights)
    print(f"主流币权重({mainstream_weight}):", mainstream_weights)
    print(f"单币种权重区间: {min_single_weight*100:.1f}% ~ {max_single_weight*100:.1f}%")
    print("最终权重:", weight_dict)

    # 合并所有return
    all_returns = merge_weighted_returns(returns, weight_dict)

    # ====== 加权收益率整体加噪声（噪声因子包含BNB的CTA噪声） ======
    def get_bnb_kline_sync():
        url = "https://api.binance.com/api/v3/klines"
        params = {
            "symbol": "BNBUSDT",
            "interval": "1m",
            "startTime": int(start_time.timestamp() * 1000),
            "endTime": int(end_time.timestamp() * 1000),
            "limit": 1000
        }
        r = requests.get(url, params=params, timeout=15)
        data = r.json()
        bnb_df = pd.DataFrame(data, columns=[
            "open_time", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base_asset_volume", "taker_buy_quote_asset_volume", "ignore"
        ])
        bnb_df["timestamp"] = bnb_df["open_time"] // 1000
        bnb_df["open"] = pd.to_numeric(bnb_df["open"], errors='coerce')
        bnb_df["close"] = pd.to_numeric(bnb_df["close"], errors='coerce')
        bnb_df = bnb_df.dropna(subset=["timestamp", "open", "close"])
        bnb_df = bnb_df.sort_values("timestamp")
        return bnb_df

    bnb_kline = get_bnb_kline_sync()

    # 先生成 bnb_noise
    bnb_noise = generate_bnb_noise(bnb_kline)
    # 只对 all_returns 的最后 len(bnb_kline) 行加噪声
    all_returns = all_returns.tail(len(bnb_kline)).reset_index(drop=True)
    all_returns = add_noise_to_returns(all_returns, bnb_noise, noise_weight) # * 2

    # 只保留最新一条
    output_latest_row(all_returns)
    latest_row = all_returns.tail(1)
    return latest_row

def insert_target_return_to_db(target_return, timestamp):
    host = "127.0.0.1"
    port = 3306
    user = "hao"
    password = "12345678"
    database = "pricing_model"
    conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conn.cursor() as cursor:
            sql = "INSERT INTO platform_coin (target_return, timestamp) VALUES (%s, %s)"
            cursor.execute(sql, (float(target_return), str(timestamp)))
            conn.commit()
            print(f"已插入数据库: target_return={target_return}, timestamp={timestamp}")
    except Exception as e:
        print(f"插入数据库失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    while True:
        try:
            latest_row = main()
            if not latest_row.empty:
                target_return = latest_row["weighted_return_with_noise"].values[0]
                timestamp = str(datetime.now(tz=pytz.timezone("Asia/Shanghai")))[:19]
                insert_target_return_to_db(target_return, timestamp)
        except Exception as e:
            print(f"主循环异常: {e}")
        time.sleep(60)