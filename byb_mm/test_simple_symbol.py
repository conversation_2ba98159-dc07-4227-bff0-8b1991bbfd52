#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版全局交易对配置测试脚本
验证简单的symbol函数功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_byb_buy_back_config():
    """测试byb_buy_back.py中的配置函数"""
    print("=" * 60)
    print("测试 byb_buy_back.py 配置函数")
    print("=" * 60)
    
    try:
        from byb_buy_back import (
            get_current_symbol, get_base_currency, get_quote_currency, 
            get_symbol_config, get_monitor_config
        )
        
        # 测试基本配置
        symbol = get_current_symbol()
        base = get_base_currency()
        quote = get_quote_currency()
        config = get_symbol_config()
        monitor_config = get_monitor_config()
        
        print(f"✅ 当前交易对: {symbol}")
        print(f"✅ 基础货币: {base}")
        print(f"✅ 计价货币: {quote}")
        print(f"✅ 最小订单量: {config['min_order_size']}")
        print(f"✅ 最大订单量: {config['max_order_size']}")
        print(f"✅ 默认价格: {config['default_price']}")
        print(f"✅ 监控配置: {monitor_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ byb_buy_back配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_config():
    """测试byb_order_algorithm.py中的配置函数"""
    print("\n" + "=" * 60)
    print("测试 byb_order_algorithm.py 配置函数")
    print("=" * 60)
    
    try:
        from byb_order_algorithm import (
            get_current_symbol, get_base_currency, get_quote_currency, 
            get_symbol_config, get_trading_config
        )
        
        # 测试基本配置
        symbol = get_current_symbol()
        base = get_base_currency()
        quote = get_quote_currency()
        config = get_symbol_config()
        trading_config = get_trading_config()
        
        print(f"✅ 当前交易对: {symbol}")
        print(f"✅ 基础货币: {base}")
        print(f"✅ 计价货币: {quote}")
        print(f"✅ 交易配置: {trading_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ algorithm配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_algorithm_integration():
    """测试算法集成"""
    print("\n" + "=" * 60)
    print("测试算法集成")
    print("=" * 60)
    
    try:
        from byb_order_algorithm import BYBOrderAlgorithm
        
        # 创建算法实例
        algorithm = BYBOrderAlgorithm()
        print("✅ 算法实例创建成功")
        
        # 测试价格获取
        try:
            price = algorithm.get_current_price()
            print(f"✅ 价格获取成功: {price}")
        except Exception as e:
            print(f"⚠️ 价格获取失败（正常，可能是网络问题）: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_different_symbols():
    """测试不同交易对的配置"""
    print("\n" + "=" * 60)
    print("测试不同交易对配置")
    print("=" * 60)
    
    # 测试不同交易对的配置解析
    test_symbols = ["bybusdt", "manausdt", "btcusdt", "ethusdt", "dogeusdt"]
    
    try:
        # 临时修改函数来测试不同交易对
        import byb_buy_back
        
        original_get_symbol = byb_buy_back.get_current_symbol
        
        for symbol in test_symbols:
            # 临时修改返回值
            byb_buy_back.get_current_symbol = lambda: symbol
            
            base = byb_buy_back.get_base_currency()
            quote = byb_buy_back.get_quote_currency()
            config = byb_buy_back.get_symbol_config()
            
            print(f"交易对: {symbol:<10} -> {base.upper()}/{quote.upper():<5} "
                  f"订单量: {config['min_order_size']}-{config['max_order_size']:<8} "
                  f"默认价格: {config['default_price']}")
        
        # 恢复原函数
        byb_buy_back.get_current_symbol = original_get_symbol
        
        print("✅ 不同交易对配置测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 不同交易对测试失败: {str(e)}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("使用说明")
    print("=" * 60)
    print("要切换交易对，请修改以下文件中的 get_current_symbol() 函数:")
    print("")
    print("1. byb_mm/byb_buy_back.py")
    print("2. byb_mm/byb_order_algorithm.py")
    print("")
    print("将函数中的返回值修改为目标交易对，例如:")
    print("  return \"bybusdt\"   # BYB/USDT")
    print("  return \"manausdt\"  # MANA/USDT") 
    print("  return \"btcusdt\"   # BTC/USDT")
    print("  return \"ethusdt\"   # ETH/USDT")
    print("")
    print("支持的交易对格式:")
    print("- xxxusdt (任何币种对USDT)")
    print("- xxxbtc  (任何币种对BTC)")
    print("- xxxeth  (任何币种对ETH)")
    print("")
    print("系统会自动解析基础货币和计价货币")
    print("如果交易对不在预设配置中，会使用默认配置")

def main():
    """主测试函数"""
    print("简化版全局交易对配置系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("byb_buy_back配置", test_byb_buy_back_config()))
    test_results.append(("algorithm配置", test_algorithm_config()))
    test_results.append(("算法集成", test_algorithm_integration()))
    test_results.append(("不同交易对", test_different_symbols()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！简化版配置系统工作正常")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
    
    # 显示使用说明
    show_usage_instructions()

if __name__ == "__main__":
    main()
