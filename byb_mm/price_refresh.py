import time
import random
import logging
from datetime import datetime
from typing import Dict, Optional, <PERSON><PERSON>
import numpy as np
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import con_pri
import pytz
import inspect

api_key = con_pri.api_key
api_secret = con_pri.api_secret

base_url = "https://openapi.100exdemo.com"
spot_market = Spot()
spot_client = SpotTrade(api_key, api_secret)
spot_client.BASE_URL = base_url
spot_market.BASE_URL = base_url

beijing_tz = pytz.timezone("Asia/Shanghai")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        tz = beijing_tz
        dt = datetime.fromtimestamp(timestamp, tz)
        return dt.timetuple()

    def format(self, record):
        # 获取调用者的帧信息
        frame = inspect.currentframe()
        if frame is not None:
            frame = frame.f_back
            while frame:
                # 使用文件名而非__file__变量，以兼容Jupyter环境
                if hasattr(frame, 'f_code') and frame.f_code.co_filename.endswith('byb_mm_new.py'):
                    record.lineno = frame.f_lineno
                    break
                frame = frame.f_back

        return super().format(record)


logging.basicConfig(
    filename='/Users/<USER>/PycharmProjects/mm/byb_mm/price_refresh.log',  # 日志输出到此文件
    level=logging.INFO,  # 将日志级别调整为INFO，减少DEBUG日志
    format='%(asctime)s - %(levelname)s - %(message)s',  # 简化格式，移除文件名和行号
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))

# 全局变量
last_price = None


class PriceNudgingAgent:
    # 类变量
    daily_nudge_count = 0
    reset_day = datetime.now().date()

    def __init__(
            self,
            symbol: str,
            target_price: float,
            order_size: float = 1.0,
            nudge_threshold: float = 0.01,  # 触发价格推动的百分比差异
            cooldown_seconds: int = 1,
            max_daily_nudges: int = 100,
            trading_hours: Tuple[int, int] = (0, 24),  # 24小时格式
            price_precision: float = 4,
            amount_precision: float = 3
    ):
        """
        初始化价格推动代理。

        参数:
            symbol: 交易对符号
            target_price: 目标锚定价格
            order_size: 用于推动价格的市场订单大小（极小）
            nudge_threshold: 触发推动的百分比差异
            cooldown_seconds: 推动之间的最小时间间隔
            max_daily_nudges: 每天最大推动次数
            trading_hours: 24小时格式的(开始小时,结束小时)元组
        """
        self.symbol = symbol
        self.target_price = round(target_price, 4)  # 目标价格精度为0.0001
        self.order_size = order_size
        self.nudge_threshold = nudge_threshold / 100.0
        self.cooldown_seconds = cooldown_seconds
        self.max_daily_nudges = max_daily_nudges
        self.trading_hours = trading_hours
        self.price_precision = price_precision
        self.amount_precision = amount_precision
        self.last_nudge_time = None

        logger.info(f"价格推动代理已初始化，交易对：{symbol}，目标价格：{target_price}")

    @classmethod
    def reset_daily_counter(cls):
        """如果是新的一天，重置每日推动计数器。"""
        current_date = datetime.now().date()
        if current_date > cls.reset_day:
            cls.daily_nudge_count = 0
            cls.reset_day = current_date
            logger.info(f"每日推动计数器已重置，新日期：{current_date}")

    def is_within_trading_hours(self) -> bool:
        """检查当前时间是否在允许的交易时间内。"""
        current_hour = datetime.now().hour
        start_hour, end_hour = self.trading_hours

        # 处理跨夜交易时段
        if start_hour <= end_hour:
            return start_hour <= current_hour < end_hour
        else:  # 例如 trading_hours = (22, 4) 表示晚上10点到凌晨4点
            return current_hour >= start_hour or current_hour < end_hour

    # 1
    def get_market_trades(self) -> list:
        """
        从交易所获取当前市场成交情况。

        返回:
            包含成交数据列表，包括:
                - c_time: 时间戳
                - price: 成交价格
                - amount: 成交数量
        """

        ## 用以前的 API 封装包接股权币价格
        trades_records = []
        trades_df = spot_market.get_trades("usdtusd")  ## usdtusd
        for _, row in trades_df.iterrows():
            # 确保必要的字段存在
            if 'ctime' in row and 'price' in row and 'amount' in row:
                # 将ctime从毫秒转换为秒
                timestamp = row['ctime'] / 1000
                price = float(row['price'])
                amount = float(row['amount'])

                # 创建元组并添加到结果列表
                trades_records.append((timestamp, price, amount))
        return trades_records

    # 2
    def should_nudge(self, market_trades: list) -> Optional[str]:
        """
        确定是否需要推动价格以及推动的方向。

        参数:
            market_trades: 当前市场成交数据列表

        返回:
            Optional[str]: "buy", "sell", 或 None (如果不需要推动)
        """
        # 如果需要，重置每日计数器
        self.__class__.reset_daily_counter()

        # 检查我们是否在活跃时段
        if not self.is_within_trading_hours():
            logger.debug(f"非交易时段: {datetime.now().hour}:00")
            return None

        # 检查是否已达到每日限制
        if self.__class__.daily_nudge_count >= self.max_daily_nudges:
            logger.info(f"已达到每日推动限制: {self.__class__.daily_nudge_count}/{self.max_daily_nudges}")
            return None

        # 检查冷却期
        if self.last_nudge_time is not None:
            elapsed = (datetime.now() - self.last_nudge_time).total_seconds()
            cooldown_seconds_new = random.uniform(self.cooldown_seconds * 0.8, self.cooldown_seconds * 2)
            if elapsed < cooldown_seconds_new:
                logger.debug(f"处于冷却期: {elapsed:.1f}秒/{cooldown_seconds_new}秒")
                return None

        # 获取当前市场价格
        current_price = spot_market.get_ticker(self.symbol)['last']

        # 计算价格偏差
        price_deviation = (current_price - self.target_price) / self.target_price

        # 根据目标价格确定方向
        if abs(price_deviation) < self.nudge_threshold:
            # 当价格变动小于阈值时，随机选择方向进行小额交易
            direction = random.choice(["buy", "sell"])
            logger.info(f"价格变动小于阈值，随机选择{direction}方向进行小额交易: 当前={current_price:.6f}, 目标={self.target_price:.6f}")
            return direction
        elif price_deviation < 0:
            # 当前价格低于目标 - 通过买入推高价格
            logger.info(f"价格低于目标，需要买入推高: 当前={current_price:.6f}, 目标={self.target_price:.6f}")
            return "buy"
        else:
            # 当前价格高于目标 - 通过卖出压低价格
            logger.info(f"价格高于目标，需要卖出压低: 当前={current_price:.6f}, 目标={self.target_price:.6f}")
            return "sell"

    def calculate_required_volume(self, direction: str) -> float:
        """
        计算击穿目标价格所需的订单数量

        参数:
            direction: "buy" 或 "sell"

        返回:
            float: 所需的订单数量
        """
        try:
            asks_df, bids_df = spot_market.get_orderbook(self.symbol)
            asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
            bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))

            # 获取当前市场价格
            current_price = spot_market.get_ticker(self.symbol)['last']
            price_deviation = (current_price - self.target_price) / self.target_price

            # 如果价格变动小于阈值，使用较小的随机交易量
            if abs(price_deviation) < self.nudge_threshold:
                small_volume = round(random.uniform(1, 300), self.amount_precision)
                logger.info(f"价格变动小于阈值，使用小额交易量: {small_volume}")
                return small_volume

            if direction == "buy":
                # 计算买入所需数量：累加卖单直到价格超过目标价格
                required_volume = 0
                for price, volume in asks:
                    if price <= self.target_price:
                        required_volume += volume
                    else:
                        # 如果计算出的数量为0，取小于当前档位的随机数量
                        if required_volume == 0:
                            required_volume = round(random.uniform(1, 300), self.amount_precision)
                        break
            else:
                # 计算卖出所需数量：累加买单直到价格低于目标价格
                required_volume = 0
                for price, volume in bids:
                    if price >= self.target_price:
                        required_volume += volume
                    else:
                        # 如果计算出的数量为0，取小于当前档位的随机数量
                        if required_volume == 0:
                            required_volume = round(random.uniform(1, 300), self.amount_precision)
                        break
            required_volume = min(1000 + random.randint(-500, 500), required_volume)

            # 如果最终计算出的数量为0，使用默认数量
            if required_volume == 0:
                required_volume = round(random.uniform(1, 300), self.amount_precision)

            return round(required_volume, self.amount_precision)
        except Exception as e:
            logger.error(f"计算所需数量时出错: {str(e)}")
            return round(random.uniform(1, 300), self.amount_precision)  # 出错时返回随机默认数量

    def update_target_price(self, new_target: float):
        """更新目标价格，保持精度为0.0001"""
        self.target_price = round(new_target, 4)
        logger.info(f"更新目标价格: {self.target_price}")

    def submit_nudge_order(self, direction: str) -> bool:
        try:
            market_data = spot_market.get_ticker(self.symbol)
            best_bid = market_data["buy"]
            best_ask = market_data["sell"]

            if direction == "buy":
                price = best_ask
                side = "BUY"
                logger.info(f"通过在卖一价下买单推高价格: {price}")
            else:
                price = best_bid
                side = "SELL"
                logger.info(f"通过在买一价下卖单压低价格: {price}")

            price = round(price, self.price_precision)

            # 计算所需数量并添加随机增量
            base_volume = self.calculate_required_volume(direction)
            random_increment = random.uniform(0.1, 0.5)  # 随机增加10%-50%
            volume = round(base_volume * (1 + random_increment), self.amount_precision)

            logger.info(f"提交{direction}推动限价单，交易对：{self.symbol}，数量={volume}，价格={price}")

            try:
                client_order_id = f"price_refresh_{time.time()}"
                if client_order_id:
                    # 预先格式化参数，减少运行时转换
                    volume_str = str(round(float(volume), self.amount_precision))
                    price_str = str(round(float(price), self.price_precision))
                    params = {
                        "symbol": 'usdtusd',
                        "side": side.lower(),
                        "type": 1,
                        "volume": volume_str,
                        "price": price_str,
                        "clientOrderId": client_order_id
                    }
                    order = spot_client.new_order(**params)
                    logger.info(f"推动订单成功。自定义订单ID: {client_order_id}， 返回订单ID：{order}")

                    # 等待0.5秒后撤单
                    time.sleep(0.5)
                    try:
                        cancel_result = spot_client.cancel_order(symbol='usdtusd', order_id=order)
                        logger.info(f"撤单成功。订单ID: {order}, 撤单结果: {cancel_result}")
                    except Exception as e:
                        logger.error(f"撤单失败: {str(e)}")

                    self.__class__.daily_nudge_count += 1
                    self.last_nudge_time = datetime.now()
                    logger.info(f"每日推动计数: {self.__class__.daily_nudge_count}/{self.max_daily_nudges}")
                    return True
                else:
                    logger.warning(f"推动订单下单失败，未返回订单ID")
                    return False

            except Exception as e:
                logger.error(f"下推动订单时出错: {str(e)}")
                return False

        except Exception as e:
            logger.error(f"推动订单准备过程中出错: {str(e)}")
            return False

    def run_once(self) -> Optional[str]:
        """
        运行价格推动逻辑的单次迭代。
        返回:
            Optional[str]: 采取的行动 ("buy", "sell", 或 None)
        """
        try:
            # 获取当前市场数据
            market_trades = self.get_market_trades()

            # 确定是否应该推动价格
            nudge_direction = self.should_nudge(market_trades)

            # 如果需要，执行推动
            if nudge_direction:
                success = self.submit_nudge_order(nudge_direction)
                if success:
                    return nudge_direction
            return None

        except Exception as e:
            logger.error(f"价格推动器出错: {str(e)}", exc_info=True)
            return None


def get_price_return(symbols: list = None, num: float = 1.1):
    """
    使用get_orderbook获取价格变化来计算收益率

    参数:
        symbols: 交易对列表（可选）
        num: 收益率乘数（可选）

    返回:
        float: 价格变化率
    """
    global last_price
    if symbols:
        returns = []
        for symbol in symbols:
            asks_df, bids_df = spot_market.get_orderbook(symbol.lower())
            # 转换为我们需要的格式: [(price, qty), ...]
            asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
            bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))
            current_price = (asks[0][0] + bids[0][0]) / 2
            if last_price is None:
                last_price = current_price
                return 0
            r = (current_price - last_price) / last_price
            last_price = current_price
            returns.append(r)
            result = np.mean(returns) * num
            if result == 0:
                return 0
            else:
                return result
    else:
        asks_df, bids_df = spot_market.get_orderbook('btcusdt')
        # 转换为我们需要的格式: [(price, qty), ...]
        asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
        bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))
        current_price = (asks[0][0] + bids[0][0]) / 2
        if last_price is None:
            last_price = current_price
            return 0
        r = (current_price - last_price) / last_price
        last_price = current_price
        result = r * num
        if result == 0:
            return 0
        else:
            return result


def main():
    # 初始化价格推动代理
    price = spot_market.get_ticker('usdtusd')['last']
    agent = PriceNudgingAgent(
        symbol="usdtusd",
        target_price=price,  # 初始目标价格会在第一次循环中更新
        order_size=10,  # 微小订单，币的数量
        nudge_threshold=0.005,  # 触发推动的0.005%偏差
        cooldown_seconds=50,
        max_daily_nudges=86400,  # 检查是否需要每日最大推动次数 每秒一次最大86400
        trading_hours=(0, 24),  # 24小时交易，根据需要调整
        price_precision=4,
        amount_precision=3
    )

    while True:
        try:
            rate = get_price_return(num=1.5)
            if rate != 0:  # 只在收益率不为0时更新和打印日志
                price = spot_market.get_ticker('usdtusd')['last']
                target_price = price * (1 + rate)
                logger.info(f'当前价格: {price}, 收益率: {rate}, 目标价格: {target_price}')
                agent.update_target_price(target_price)

            agent.run_once()
            time.sleep(1)
        except Exception as e:
            logger.error(f"运行出错: {str(e)}", exc_info=True)
            time.sleep(1)


if __name__ == "__main__":
    main()


