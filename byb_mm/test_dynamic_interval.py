#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态时间间隔计算功能
"""

import sys
import os
import random
import time
from datetime import datetime, timedelta
import pytz

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from byb_order_algorithm import BYBOrderAlgorithm, get_trading_config, get_current_symbol

def test_dynamic_interval_calculation():
    """测试动态时间间隔计算"""
    print("=" * 80)
    print("动态时间间隔计算测试".center(80))
    print("=" * 80)
    
    # 创建算法实例
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数组合
    test_cases = [
        {"days": 1, "total_amount": 5000, "each_amount": 500},
        {"days": 3, "total_amount": 15000, "each_amount": 1000},
        {"days": 7, "total_amount": 30000, "each_amount": 2000},
        {"days": 14, "total_amount": 50000, "each_amount": 3000},
        {"days": 1, "total_amount": 1000, "each_amount": None},
        {"days": 5, "total_amount": 8000, "each_amount": None},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"  时间范围: {case['days']} 天")
        print(f"  总金额: {case['total_amount']:,.2f}")
        print(f"  单次最大: {case['each_amount'] if case['each_amount'] else '默认配置'}")
        
        try:
            # 模拟获取当前价格
            current_price = 0.1  # 模拟BYB价格
            
            # 估算订单数量
            estimated_orders = algorithm.estimate_order_count(
                case['total_amount'], 
                case['each_amount'], 
                current_price
            )
            
            # 计算动态最大间隔
            trading_config = get_trading_config()
            dynamic_max_interval = algorithm.calculate_dynamic_max_interval(
                case['days'], 
                estimated_orders, 
                trading_config["min_interval"]
            )
            
            # 计算理论平均间隔
            total_seconds = int(case['days'] * 24 * 3600)
            theoretical_avg_interval = total_seconds / estimated_orders
            
            print(f"  预估订单数: {estimated_orders}")
            print(f"  理论平均间隔: {theoretical_avg_interval:.1f} 秒 ({theoretical_avg_interval/60:.1f} 分钟)")
            print(f"  动态最大间隔: {dynamic_max_interval} 秒 ({dynamic_max_interval/60:.1f} 分钟)")
            print(f"  间隔比例: {dynamic_max_interval/theoretical_avg_interval:.2f}")
            
            # 验证间隔是否合理
            if dynamic_max_interval < trading_config["min_interval"] * 2:
                print(f"  ⚠️  警告: 最大间隔过小")
            elif dynamic_max_interval > total_seconds / 3:
                print(f"  ⚠️  警告: 最大间隔过大")
            else:
                print(f"  ✅ 间隔设置合理")
                
        except Exception as e:
            print(f"  ❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 80)

def test_interval_distribution():
    """测试时间间隔分布"""
    print("\n时间间隔分布测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 3
    total_amount = 15000
    each_amount = 1000
    current_price = 0.1
    
    print(f"测试参数:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    print(f"  当前价格: {current_price}")
    
    # 多次运行，观察间隔分布
    intervals = []
    for run in range(10):
        print(f"\n运行 {run + 1}:")
        
        # 估算订单数量
        estimated_orders = algorithm.estimate_order_count(total_amount, each_amount, current_price)
        
        # 计算动态最大间隔
        trading_config = get_trading_config()
        dynamic_max_interval = algorithm.calculate_dynamic_max_interval(
            days, estimated_orders, trading_config["min_interval"]
        )
        
        intervals.append(dynamic_max_interval)
        
        print(f"  预估订单: {estimated_orders}")
        print(f"  最大间隔: {dynamic_max_interval} 秒 ({dynamic_max_interval/60:.1f} 分钟)")
        
        # 模拟生成几个随机间隔
        min_interval = trading_config["min_interval"]
        sample_intervals = []
        for _ in range(5):
            interval = random.uniform(min_interval, dynamic_max_interval)
            sample_intervals.append(interval)
        
        print(f"  样本间隔: {[f'{i/60:.1f}分钟' for i in sample_intervals]}")
    
    # 统计分析
    print(f"\n统计分析:")
    print(f"  最大间隔范围: {min(intervals)} - {max(intervals)} 秒")
    print(f"  平均最大间隔: {sum(intervals)/len(intervals):.1f} 秒")
    print(f"  标准差: {((sum((x - sum(intervals)/len(intervals))**2 for x in intervals) / len(intervals))**0.5):.1f} 秒")
    
    print("\n" + "=" * 80)

def test_realistic_schedule():
    """测试生成真实的订单计划"""
    print("\n真实订单计划生成测试".center(80))
    print("=" * 80)
    
    algorithm = BYBOrderAlgorithm()
    
    # 测试参数
    days = 2
    total_amount = 8000
    each_amount = 800
    
    print(f"生成订单计划:")
    print(f"  时间范围: {days} 天")
    print(f"  总金额: {total_amount:,.2f}")
    print(f"  单次最大: {each_amount}")
    
    try:
        # 生成订单计划
        schedule = algorithm.generate_order_schedule(days, total_amount, each_amount)
        
        print(f"\n生成的订单计划:")
        print(f"  总订单数: {len(schedule)}")
        
        # 分析时间间隔
        intervals = []
        for i in range(1, min(10, len(schedule))):  # 只分析前10个订单
            prev_time = schedule[i-1]['scheduled_time']
            curr_time = schedule[i]['scheduled_time']
            interval = (curr_time - prev_time).total_seconds()
            intervals.append(interval)
            
            print(f"  订单 {i}: {curr_time.strftime('%Y-%m-%d %H:%M:%S')} "
                  f"(间隔: {interval/60:.1f}分钟, 金额: {schedule[i]['amount']:.2f})")
        
        if intervals:
            print(f"\n间隔分析:")
            print(f"  平均间隔: {sum(intervals)/len(intervals)/60:.1f} 分钟")
            print(f"  最小间隔: {min(intervals)/60:.1f} 分钟")
            print(f"  最大间隔: {max(intervals)/60:.1f} 分钟")
        
    except Exception as e:
        print(f"❌ 生成订单计划失败: {str(e)}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    print("开始测试动态时间间隔计算功能...")
    print(f"当前时间: {datetime.now(pytz.timezone('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前交易对: {get_current_symbol()}")
    
    # 运行测试
    test_dynamic_interval_calculation()
    test_interval_distribution()
    test_realistic_schedule()
    
    print("\n测试完成！") 