#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的动态间隔计算功能
"""

import sys
import os
import random
import logging
from datetime import datetime
import pytz

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class OptimizedIntervalCalculator:
    """优化后的间隔计算器，用于测试"""
    
    def calculate_dynamic_max_interval(self, days: float, estimated_orders: int, min_interval: int = 60) -> int:
        """计算动态最大时间间隔"""
        try:
            # 计算总可用时间（秒）
            total_seconds = int(days * 24 * 3600)
            
            # 基础计算：总时间除以订单数，确保订单能均匀分布
            base_interval = total_seconds / max(estimated_orders, 1)
            
            # 激进优化：针对短期交易使用更直接的时间控制策略
            if days <= 1:
                # 1天内：使用基于理论间隔的直接计算，减少随机性
                # 目标：确保实际间隔接近理论间隔
                target_interval = base_interval
                
                # 允许的偏差范围：±15%
                max_deviation = 0.15
                dynamic_max_interval = int(target_interval * (1 + max_deviation))
                
                # 确保不超过合理上限
                max_max_interval = min(total_seconds // 6, 3600)  # 最大1小时
                dynamic_max_interval = min(dynamic_max_interval, max_max_interval)
                
                # 确保最小间隔的2倍
                dynamic_max_interval = max(min_interval * 2, dynamic_max_interval)
                
                return dynamic_max_interval
                
            elif days <= 3:
                # 1-3天：适度随机性，但更接近理论值
                random_factor = random.uniform(0.9, 1.1)  # ±10%
                distribution_factor = random.uniform(1.0, 1.3)  # 减少分布不均匀性
                market_activity_factor = random.uniform(0.95, 1.05)  # 减少市场活跃度影响
                volatility_factor = random.uniform(0.95, 1.05)  # 减少波动性
            elif days <= 7:
                # 3-7天：正常随机性
                random_factor = random.uniform(0.8, 1.2)  # ±20%
                distribution_factor = random.uniform(1.05, 1.6)  # 正常分布不均匀性
                market_activity_factor = random.uniform(0.9, 1.1)  # 正常市场活跃度影响
                volatility_factor = random.uniform(0.9, 1.1)  # 正常波动性
            else:
                # 7天以上：保持原有随机性
                random_factor = random.uniform(0.7, 1.1)
                distribution_factor = random.uniform(1.1, 2.2)
                market_activity_factor = random.uniform(0.9, 1.4)
                volatility_factor = random.uniform(0.8, 1.3)
            
            # 基于当前时间的微调因子
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                time_factor = random.uniform(0.9, 1.1)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                time_factor = random.uniform(1.1, 1.3)
            else:
                time_factor = random.uniform(0.95, 1.15)
            
            # 综合计算动态最大间隔
            dynamic_max_interval = int(base_interval * random_factor * distribution_factor * 
                                     market_activity_factor * volatility_factor * time_factor)
            
            # 优化：根据时间范围调整上下限策略
            min_max_interval = min_interval * 2
            
            # 根据总时间动态调整最大间隔上限，优化短期交易
            if days <= 1:
                max_max_interval = min(total_seconds // 8, 2700)  # 最大45分钟
            elif days <= 3:
                max_max_interval = min(total_seconds // 6, 5400)  # 最大1.5小时
            elif days <= 7:
                max_max_interval = min(total_seconds // 4, 14400)
            else:
                max_max_interval = min(total_seconds // 3, 21600)
            
            # 确保在合理范围内
            dynamic_max_interval = max(min_max_interval, min(dynamic_max_interval, max_max_interval))
            
            # 优化：根据时间范围调整最终微调范围
            if days <= 1:
                final_adjustment = random.uniform(0.98, 1.02)
            elif days <= 3:
                final_adjustment = random.uniform(0.96, 1.04)
            else:
                final_adjustment = random.uniform(0.95, 1.05)
            
            dynamic_max_interval = int(dynamic_max_interval * final_adjustment)
            
            return dynamic_max_interval
            
        except Exception as e:
            logging.error(f"计算动态最大间隔失败: {str(e)}")
            return 3600

    def calculate_dynamic_min_interval(self, days: float, estimated_orders: int, max_interval: int = 3600) -> int:
        """计算动态最小时间间隔"""
        try:
            # 计算总可用时间（秒）
            total_seconds = int(days * 24 * 3600)
            
            # 基础计算：总时间除以订单数，确保订单能均匀分布
            base_interval = total_seconds / max(estimated_orders, 1)
            
            # 优化：根据时间范围调整基础最小间隔比例，减少短期交易偏差
            if days <= 1:
                base_min_ratio = random.uniform(0.6, 0.8)  # 60%-80%
            elif days <= 3:
                base_min_ratio = random.uniform(0.5, 0.7)  # 50%-70%
            elif days <= 7:
                base_min_ratio = random.uniform(0.4, 0.6)  # 40%-60%
            else:
                base_min_ratio = random.uniform(0.3, 0.5)  # 30%-50%
            
            base_min_interval = base_interval * base_min_ratio
            
            # 优化：根据时间范围调整随机因子范围
            if days <= 1:
                distribution_factor = random.uniform(0.9, 1.1)
                time_factor = random.uniform(1.1, 1.3)
                hour_factor = random.uniform(0.95, 1.05)
                order_factor = random.uniform(0.95, 1.05)
            elif days <= 3:
                distribution_factor = random.uniform(0.85, 1.15)
                time_factor = random.uniform(1.0, 1.25)
                hour_factor = random.uniform(0.9, 1.1)
                order_factor = random.uniform(0.9, 1.1)
            elif days <= 7:
                distribution_factor = random.uniform(0.8, 1.2)
                time_factor = random.uniform(0.95, 1.2)
                hour_factor = random.uniform(0.9, 1.1)
                order_factor = random.uniform(0.85, 1.15)
            else:
                distribution_factor = random.uniform(0.8, 1.2)
                time_factor = random.uniform(0.9, 1.2)
                hour_factor = random.uniform(0.95, 1.15)
                order_factor = random.uniform(0.9, 1.1)
            
            # 基于当前时间的微调因子
            current_hour = datetime.now(beijing_tz).hour
            if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
                hour_factor *= random.uniform(0.9, 1.1)
            elif 12 <= current_hour <= 13 or 18 <= current_hour <= 20:
                hour_factor *= random.uniform(1.1, 1.3)
            else:
                hour_factor *= random.uniform(0.95, 1.15)
            
            # 订单数量调整因子
            if estimated_orders > 50:
                order_factor *= random.uniform(0.7, 0.9)
            elif estimated_orders > 20:
                order_factor *= random.uniform(0.8, 1.0)
            elif estimated_orders > 10:
                order_factor *= random.uniform(0.9, 1.1)
            else:
                order_factor *= random.uniform(1.0, 1.2)
            
            # 综合计算动态最小间隔
            dynamic_min_interval = int(base_min_interval * distribution_factor * time_factor * 
                                     hour_factor * order_factor)
            
            # 优化：根据时间范围调整上下限策略
            absolute_min_interval = 30
            
            # 根据总时间动态调整最小间隔上限，优化短期交易
            if days <= 1:
                max_min_interval = min(total_seconds // 15, 1200)  # 最大20分钟
            elif days <= 3:
                max_min_interval = min(total_seconds // 12, 2400)  # 最大40分钟
            elif days <= 7:
                max_min_interval = min(total_seconds // 8, 7200)
            else:
                max_min_interval = min(total_seconds // 6, 10800)
            
            # 确保最小间隔不超过最大间隔的50%
            max_min_interval = min(max_min_interval, max_interval * 0.5)
            
            # 确保在合理范围内
            dynamic_min_interval = max(absolute_min_interval, min(dynamic_min_interval, max_min_interval))
            
            # 优化：根据时间范围调整最终微调范围
            if days <= 1:
                final_adjustment = random.uniform(0.98, 1.02)
            elif days <= 3:
                final_adjustment = random.uniform(0.97, 1.03)
            else:
                final_adjustment = random.uniform(0.97, 1.03)
            
            dynamic_min_interval = int(dynamic_min_interval * final_adjustment)
            
            return dynamic_min_interval
            
        except Exception as e:
            logging.error(f"计算动态最小间隔失败: {str(e)}")
            return 60

def test_optimized_intervals():
    """测试优化后的动态间隔计算"""
    print("=" * 60)
    print("测试优化后的动态间隔计算功能".center(60))
    print("=" * 60)
    
    calculator = OptimizedIntervalCalculator()
    
    # 测试不同场景
    test_cases = [
        {"days": 1, "estimated_orders": 10, "description": "1天10个订单"},
        {"days": 3, "estimated_orders": 25, "description": "3天25个订单"},
        {"days": 7, "estimated_orders": 50, "description": "7天50个订单"},
        {"days": 14, "estimated_orders": 100, "description": "14天100个订单"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {case['description']}")
        print("-" * 40)
        
        days = case["days"]
        estimated_orders = case["estimated_orders"]
        
        # 计算动态最大间隔
        dynamic_max_interval = calculator.calculate_dynamic_max_interval(days, estimated_orders, 60)
        print(f"动态最大间隔: {dynamic_max_interval}秒 ({dynamic_max_interval/60:.1f}分钟)")
        
        # 计算动态最小间隔
        dynamic_min_interval = calculator.calculate_dynamic_min_interval(days, estimated_orders, dynamic_max_interval)
        print(f"动态最小间隔: {dynamic_min_interval}秒 ({dynamic_min_interval/60:.1f}分钟)")
        
        # 计算间隔范围
        interval_range = dynamic_max_interval - dynamic_min_interval
        print(f"间隔范围: {interval_range}秒 ({interval_range/60:.1f}分钟)")
        
        # 验证合理性
        if dynamic_min_interval >= dynamic_max_interval:
            print("❌ 错误: 最小间隔大于等于最大间隔")
        elif dynamic_min_interval < 30:
            print("⚠️  警告: 最小间隔过短")
        elif dynamic_max_interval > days * 24 * 3600 * 0.5:
            print("⚠️  警告: 最大间隔过长")
        else:
            print("✅ 间隔设置合理")
        
        # 计算理论平均间隔
        theoretical_avg_interval = (days * 24 * 3600) / estimated_orders
        print(f"理论平均间隔: {theoretical_avg_interval:.1f}秒 ({theoretical_avg_interval/60:.1f}分钟)")
        
        # 检查动态间隔是否在合理范围内
        if dynamic_min_interval <= theoretical_avg_interval <= dynamic_max_interval:
            print("✅ 动态间隔范围覆盖理论平均间隔")
        else:
            print("⚠️  警告: 动态间隔范围可能不合理")
        
        # 计算时间分布偏差
        total_theoretical_time = theoretical_avg_interval * (estimated_orders - 1)
        total_actual_time_range = (dynamic_min_interval + dynamic_max_interval) / 2 * (estimated_orders - 1)
        time_deviation = abs(total_actual_time_range - total_theoretical_time) / total_theoretical_time
        print(f"预估时间偏差: {time_deviation:.2%}")
        
        # 评估优化效果
        if time_deviation < 0.3:
            print("✅ 优化效果良好")
        elif time_deviation < 0.5:
            print("⚠️  优化效果一般")
        else:
            print("❌ 优化效果不佳")

def test_multiple_runs_optimized():
    """测试多次运行的一致性"""
    print("\n" + "=" * 60)
    print("测试多次运行的一致性（优化后）".center(60))
    print("=" * 60)
    
    calculator = OptimizedIntervalCalculator()
    
    # 测试短期交易场景
    test_scenarios = [
        {"days": 1, "estimated_orders": 8, "description": "1天8个订单"},
        {"days": 3, "estimated_orders": 20, "description": "3天20个订单"},
    ]
    
    for scenario in test_scenarios:
        print(f"\n{scenario['description']}:")
        days = scenario["days"]
        estimated_orders = scenario["estimated_orders"]
        
        max_intervals = []
        min_intervals = []
        deviations = []
        
        for i in range(5):
            max_interval = calculator.calculate_dynamic_max_interval(days, estimated_orders, 60)
            min_interval = calculator.calculate_dynamic_min_interval(days, estimated_orders, max_interval)
            
            max_intervals.append(max_interval)
            min_intervals.append(min_interval)
            
            # 计算预估偏差
            theoretical_avg = (days * 24 * 3600) / estimated_orders
            actual_avg = (min_interval + max_interval) / 2
            deviation = abs(actual_avg - theoretical_avg) / theoretical_avg
            deviations.append(deviation)
            
            print(f"  第{i+1}次: {min_interval}-{max_interval}秒, 偏差{deviation:.2%}")
        
        # 计算统计信息
        avg_deviation = sum(deviations) / len(deviations)
        max_variation = (max(max_intervals) - min(max_intervals)) / min(max_intervals)
        min_variation = (max(min_intervals) - min(min_intervals)) / min(min_intervals)
        
        print(f"  平均偏差: {avg_deviation:.2%}")
        print(f"  最大间隔变化: {max_variation:.2%}")
        print(f"  最小间隔变化: {min_variation:.2%}")
        
        if avg_deviation < 0.3 and max_variation < 0.2 and min_variation < 0.2:
            print("  ✅ 优化效果优秀")
        elif avg_deviation < 0.5 and max_variation < 0.3 and min_variation < 0.3:
            print("  ⚠️  优化效果良好")
        else:
            print("  ❌ 优化效果需要改进")

if __name__ == "__main__":
    test_optimized_intervals()
    test_multiple_runs_optimized()
    print("\n测试完成!") 