import pandas as pd
import pymysql
import asyncio
import logging
import traceback
import json
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Optional
import pytz
import time
import backoff
import aiohttp

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)
sys.path.append(current_dir)

from byex.spot.trade import SpotTrade
from byex.spot.market import Spot
import con_pri
from byb_order_algorithm import BYBOrderAlgorithm

# ==================== 全局交易对配置 ====================
def get_current_symbol():
    """获取当前交易对 - 在这里修改交易对"""
    return "bybusdt"  # 修改这里来切换交易对: bybusdt, manausdt, btcusdt 等

def get_base_currency():
    """获取基础货币"""
    symbol = get_current_symbol()
    if symbol == "bybusdt":
        return "byb"
    elif symbol == "ongusdt":
        return "ong"
    elif symbol == "btcusdt":
        return "btc"
    elif symbol == "ethusdt":
        return "eth"
    else:
        # 自动解析：去掉usdt后缀
        if symbol.endswith("usdt"):
            return symbol[:-4]
        return "unknown"

def get_quote_currency():
    """获取计价货币"""
    symbol = get_current_symbol()
    if symbol.endswith("usdt"):
        return "usdt"
    elif symbol.endswith("btc"):
        return "btc"
    elif symbol.endswith("eth"):
        return "eth"
    else:
        return "usdt"  # 默认USDT

def get_symbol_config():
    """获取当前交易对的配置"""
    symbol = get_current_symbol()
    base = get_base_currency()
    quote = get_quote_currency()

    # 根据不同交易对返回不同配置
    configs = {
        "bybusdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        },
        "ongusdt": {
            "min_order_size": 100.0,
            "max_order_size": 10000.0,
            "default_price": 0.1
        }
    }

    return configs.get(symbol, {
        "min_order_size": 1.0,
        "max_order_size": 100.0,
        "default_price": 1.0
    })
# ==================== 全局交易对配置结束 ====================

def convert_datetime(obj):
    if isinstance(obj, dict):
        return {k: convert_datetime(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime(i) for i in obj]
    elif 'datetime' in str(type(obj)).lower() and hasattr(obj, 'isoformat'):
        # 兜底：只要有isoformat方法且类型名里有datetime
        return obj.isoformat()
    else:
        return obj

# 时区设置
beijing_tz = pytz.timezone("Asia/Shanghai")

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('byb_buy_back_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接信息
DB_CONFIG = {
    "host": "127.0.0.1",
    "port": 3306,
    "user": "weili",
    "password": "12345678",
    "database": "buy_back"
}

# 监控配置
def get_monitor_config():
    """获取当前交易对的监控配置"""
    return {
        "check_interval": 10,  # 参数检查间隔（秒）
        "symbol": get_current_symbol(),   # 交易对
        "base_currency": get_base_currency(), # 基础货币
        "quote_currency": get_quote_currency() # 计价货币
    }


@backoff.on_exception(backoff.expo, Exception, max_tries=3)
async def send_lark(msg, level='info'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level.upper()}】BYB回购系统\n{str(msg)}"
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/230d3321-92cb-44b4-8dcc-a77b2e2c4c2a'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='info'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            task = loop.create_task(send_lark(msg, level))
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class BYBBuyBackSystem:
    """BYB智能回购系统（并发参数检测与下单）"""

    def __init__(self):
        self.db_config = DB_CONFIG
        # base_url = "https://openapi.100exdemo.com"
        self.spot_market = Spot()
        # self.spot_market.BASE_URL = base_url
        self.spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
        # self.spot_client.BASE_URL = base_url
        self.is_running = True
        self.current_algorithm = None
        self.current_params = None
        self.last_params = None
        self.execution_history = []
        self.total_spent_quote = 0.0
        self.state_file = "buy_back_state.json"
        self.order_task = None
        self.load_state()
        current_symbol = get_current_symbol()
        base_currency = get_base_currency()
        quote_currency = get_quote_currency()
        logging.info(f"智能回购系统初始化完成 - 交易对: {current_symbol}, 基础货币: {base_currency}, 计价货币: {quote_currency}")

    def get_buy_back_params(self) -> Dict:
        """从数据库获取回购参数"""
        try:
            conn = pymysql.connect(**self.db_config)
            with conn.cursor() as cursor:
                cursor.execute("SELECT * FROM buy_back_params ORDER BY created_at DESC LIMIT 1;")
                result = cursor.fetchone()
                if result:
                    params = {
                        'id': result[0],
                        'days': float(result[1]),
                        'total_amount': float(result[2]),
                        'each_amount': float(result[3]),
                        'created_at': result[4],
                        'status': result[5]
                    }
                    return params
                else:
                    raise ValueError("数据库中没有找到回购参数")
        except Exception as e:
            logging.error(f"获取数据库参数失败: {str(e)}")
            raise
        finally:
            if 'conn' in locals():
                conn.close()

    def get_account_balance(self) -> Dict:
        """获取账户余额"""
        try:
            account_info = self.spot_client.account()
            if not account_info or 'coin_list' not in account_info:
                raise ValueError("获取账户信息失败")

            coin_list = account_info['coin_list']
            balances = {}

            for coin_info in coin_list:
                coin = coin_info['coin'].lower()
                normal = float(coin_info.get('normal', 0))
                locked = float(coin_info.get('locked', 0))
                total = normal + locked
                balances[coin] = {
                    'normal': normal,
                    'locked': locked,
                    'total': total
                }

            # 获取当前交易对的货币信息
            base_currency = get_base_currency()
            quote_currency = get_quote_currency()
            base_balance = balances.get(base_currency, {}).get('total', 0)
            quote_balance = balances.get(quote_currency, {}).get('total', 0)

            logging.info(f"获取账户余额成功: {base_currency.upper()}={base_balance:.2f}, {quote_currency.upper()}={quote_balance:.2f}")
            return balances

        except Exception as e:
            logging.error(f"获取账户余额失败: {str(e)}")
            raise

    def load_state(self):
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.total_spent_quote = state.get('total_spent_quote', state.get('total_spent_usdt', 0.0))
                    self.execution_history = state.get('execution_history', [])
                    self.last_params = state.get('last_params', None)
                    self.current_params = self.last_params
                    quote_currency = get_quote_currency()
                    logging.info(f"加载历史状态: 已花费{quote_currency.upper()}={self.total_spent_quote:.2f}, 执行记录={len(self.execution_history)}条, last_params={self.last_params}")
            else:
                logging.info("未找到历史状态文件，从零开始")
        except Exception as e:
            logging.error(f"加载历史状态失败: {str(e)}")
            self.total_spent_quote = 0.0
            self.execution_history = []
            self.last_params = None
            self.current_params = None

    def save_state(self):
        try:
            state = {
                'total_spent_quote': self.total_spent_quote,
                'total_spent_usdt': self.total_spent_quote,
                'execution_history': convert_datetime(self.execution_history),
                'last_update': datetime.now(beijing_tz).isoformat(),
                'last_params': convert_datetime(self.last_params)
            }
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            quote_currency = get_quote_currency()
            logging.debug(f"状态已保存: 已花费{quote_currency.upper()}={self.total_spent_quote:.2f}, last_params={self.last_params}")
        except Exception as e:
            logging.error(f"保存状态失败: {str(e)}")

    def params_changed(self, new_params: Dict) -> bool:
        if not self.last_params:
            return True
        key_fields = ['days', 'total_amount', 'each_amount', 'status']
        for field in key_fields:
            if self.last_params.get(field) != new_params.get(field):
                logging.info(f"参数变化检测: {field} 从 {self.last_params.get(field)} 变为 {new_params.get(field)}")
                return True
        return False

    async def stop_current_algorithm(self):
        """停止当前运行的算法"""
        if self.current_algorithm and self.current_algorithm.is_running:
            logging.info("停止当前运行的下单算法")
            self.current_algorithm.stop_algorithm()

            # 等待算法停止
            max_wait = 30  # 最多等待30秒
            wait_count = 0
            while self.current_algorithm.is_running and wait_count < max_wait:
                await asyncio.sleep(1)
                wait_count += 1

            if self.current_algorithm.is_running:
                logging.warning("算法未能在预期时间内停止")
            else:
                logging.info("算法已成功停止")

    async def monitor_params_loop(self):
        """参数监控循环，发现status变化时控制下单"""
        monitor_config = get_monitor_config()
        while self.is_running:
            try:
                current_params = self.get_buy_back_params()
                if self.current_params is None or self.params_changed(current_params):
                    logging.info(f"参数变化: {self.current_params} -> {current_params}")
                    self.last_params = current_params
                    self.save_state()
                    self.current_params = current_params
                    if current_params.get('status', 0) != 1:
                        logging.info("监测到status!=1，准备停止下单")
                        if self.current_algorithm and self.current_algorithm.is_running:
                            self.current_algorithm.stop_algorithm()
                        if self.order_task and not self.order_task.done():
                            self.order_task.cancel()
                    else:
                        # status=1，自动重启下单
                        if (not self.order_task) or self.order_task.done():
                            logging.info("status=1，启动下单任务")
                            self.order_task = asyncio.create_task(self.order_algorithm_loop(current_params))
                await asyncio.sleep(monitor_config['check_interval'])
            except Exception as e:
                logging.error(f"参数监控异常: {str(e)}\n{traceback.format_exc()}")
                await asyncio.sleep(10)

    async def order_algorithm_loop(self, params):
        """下单主循环，直到被stop"""
        try:
            self.current_algorithm = BYBOrderAlgorithm()
            # 动态参数注入
            def modified_get_params():
                return params.copy()
            self.current_algorithm.get_buy_back_params = modified_get_params
            await self.current_algorithm.run_algorithm()
            # 记录执行结果
            execution_record = {
                'start_time': datetime.now(beijing_tz).isoformat(),
                'params': params,
                'status': 'completed' if not self.current_algorithm.is_running else 'interrupted',
                'orders_executed': getattr(self.current_algorithm, 'orders_executed', None),
                'total_executed': getattr(self.current_algorithm, 'total_executed', None)
            }
            self.execution_history.append(execution_record)
            self.save_state()
            logging.info(f"下单任务完成: {execution_record}")
        except asyncio.CancelledError:
            logging.info("下单任务被取消")
        except Exception as e:
            logging.error(f"下单任务异常: {str(e)}\n{traceback.format_exc()}")

    def stop_system(self):
        self.is_running = False
        if self.current_algorithm:
            self.current_algorithm.stop_algorithm()
        if self.order_task and not self.order_task.done():
            self.order_task.cancel()
        logging.info("回购系统停止信号已发送")

    def get_system_status(self) -> Dict:
        algorithm_status = None
        if self.current_algorithm:
            algorithm_status = self.current_algorithm.get_status()
        quote_currency = get_quote_currency()
        return {
            'system_running': self.is_running,
            'total_spent_quote': self.total_spent_quote,
            f'total_spent_{quote_currency}': self.total_spent_quote,
            'execution_history_count': len(self.execution_history),
            'last_params': self.last_params,
            'current_algorithm': algorithm_status
        }


# 原始的简单函数保持兼容性
def get_buy_back_params():
    """获取回购参数（兼容性函数）"""
    system = BYBBuyBackSystem()
    return system.get_buy_back_params()


async def main():
    try:
        buy_back_system = BYBBuyBackSystem()
        await asyncio.gather(
            buy_back_system.monitor_params_loop(),
        )
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        if 'buy_back_system' in locals():
            buy_back_system.stop_system()
    except Exception as e:
        logging.error(f"主程序异常: {str(e)}\n{traceback.format_exc()}")


if __name__ == '__main__':
    print("=" * 80)
    print("BYB智能回购系统".center(80))
    print("=" * 80)
    print("功能：监控数据库参数变化，自动执行下单算法")
    print("特点：支持参数更新、余额计算、动态调整")
    print("监控：实时参数监控、Lark通知、完整日志")
    print("=" * 80)
    asyncio.run(main())

