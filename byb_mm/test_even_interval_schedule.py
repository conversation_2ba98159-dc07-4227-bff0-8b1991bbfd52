#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试等间隔+微扰动订单计划效果
"""
import sys
import os
from datetime import datetime, timedelta
import pytz
import random

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from byb_order_algorithm import BYBOrderAlgorithm

beijing_tz = pytz.timezone("Asia/Shanghai")

def test_even_interval_schedule(days, total_amount, each_amount=None):
    print(f"\n{'='*60}")
    print(f"测试: {days}天, 总金额: {total_amount}, 单次最大: {each_amount if each_amount else '默认'}")
    algo = BYBOrderAlgorithm()
    # Mock价格方法
    def mock_get_current_price():
        return 0.1  # 固定价格0.1
    algo.get_current_price = mock_get_current_price
    schedule = algo.generate_order_schedule(days, total_amount, each_amount)
    print(f"生成订单数: {len(schedule)}")
    if len(schedule) < 2:
        print("订单数过少，跳过分析")
        return
    # 统计间隔
    intervals = []
    for i in range(1, len(schedule)):
        prev_time = schedule[i-1]['scheduled_time']
        curr_time = schedule[i]['scheduled_time']
        intervals.append((curr_time - prev_time).total_seconds())
    avg_interval = sum(intervals) / len(intervals)
    min_interval = min(intervals)
    max_interval = max(intervals)
    print(f"平均间隔: {avg_interval:.1f}秒 ({avg_interval/60:.1f}分钟)")
    print(f"最小间隔: {min_interval:.1f}秒, 最大间隔: {max_interval:.1f}秒")
    # 总时长
    total_actual_time = (schedule[-1]['scheduled_time'] - schedule[0]['scheduled_time']).total_seconds()
    total_theoretical_time = days * 24 * 3600
    deviation = abs(total_actual_time - total_theoretical_time) / total_theoretical_time
    print(f"理论总时长: {total_theoretical_time/3600:.2f}小时, 实际总时长: {total_actual_time/3600:.2f}小时")
    print(f"总时长偏差: {deviation:.2%}")
    if deviation < 0.05:
        print("✅ 总时长控制优秀")
    elif deviation < 0.1:
        print("⚠️  总时长控制良好")
    else:
        print("❌ 总时长偏差较大")
    print(f"订单金额分布: {[o['amount'] for o in schedule]}")

if __name__ == "__main__":
    # 典型短期场景
    test_even_interval_schedule(1, 1000, 100)
    test_even_interval_schedule(2, 2000, 200)
    test_even_interval_schedule(3, 3000, 300)
    # 边界情况
    test_even_interval_schedule(0.5, 500, 100)
    test_even_interval_schedule(3, 1000, 50)
    print("\n测试完成！") 