import numpy as np
import pandas as pd
import random
import time
from math import tanh
import sys
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import logging
import pytz
import signal
from datetime import datetime
import con_pri
import asyncio
import traceback
import inspect
import config
import json
import os

beijing_tz = pytz.timezone("Asia/Shanghai")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        tz = beijing_tz
        dt = datetime.fromtimestamp(timestamp, tz)
        return dt.timetuple()


    def format(self, record):
        # 获取调用者的帧信息
        frame = inspect.currentframe()
        if frame is not None:
            frame = frame.f_back
            while frame:
                # 使用文件名而非__file__变量，以兼容Jupyter环境
                if hasattr(frame, 'f_code') and frame.f_code.co_filename.endswith('byb_mm_new.py'):
                    record.lineno = frame.f_lineno
                    break
                frame = frame.f_back

        return super().format(record)


logging.basicConfig(
    filename='/Users/<USER>/PycharmProjects/mm/byb_mm/byb_mm.log',  # Log to this file
    level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',  # 添加文件名和行号
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'))


def handle_exit(signum, frame):
    logging.info(f"收到信号 {signum}, 进行清理...")
    # 取消所有订单
    try:
        spot_client.cancel_all_orders_by_symbol('usdtusd')
    except Exception as e:
        logging.error(f"撤单失败: {str(e)}\n{traceback.format_exc()}")
    # 关闭事件循环
    loop = asyncio.get_event_loop()
    loop.stop()
    logging.info("程序即将退出...")
    sys.exit(0)


# 监听 Ctrl+C (SIGINT) 和 kill (SIGTERM)
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

base_url = "https://openapi.100exdemo.com"
spot_market = Spot()
spot_market.BASE_URL = base_url
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_client.BASE_URL = base_url

# 添加最大并发控制
MAX_CONCURRENT_ORDERS = 10  # 最大并发订单数
order_semaphore = asyncio.Semaphore(MAX_CONCURRENT_ORDERS)  # 订单并发信号量
active_orders = set()  # 活跃订单集合

# 按价格分组的订单ID字典
current_order_ids_grouped = {
    "buy": {},  # 按价格分组的买单订单ID列表
    "sell": {}  # 按价格分组的卖单订单ID列表
}

# 全局变量标记是否是首次运行
first_run = True

# 初始库存量常量
INITIAL_USDT_INVENTORY = 999885.********  # 初始 USDT 库存量
INITIAL_USD_INVENTORY = 999829.44  # 初始 USD 库存量


def get_inventory(coin: str = 'usdt'):
    """获取交易所API返回的原始库存数据"""
    account = spot_client.account()
    df_account = pd.DataFrame(account['coin_list'])
    usdt_assets = df_account[df_account['coin'] == coin]
    usdt_num = float(usdt_assets['normal'].iloc[0]) + float(usdt_assets['locked'].iloc[0])
    return usdt_num


def get_pending_orders(symbol: str = 'usdtusd'):
    """获取当前未成交的挂单信息

    返回一个列表，每个元素是一个字典，包含挂单信息
    """
    try:
        # 获取所有未成交订单
        orders = spot_client.get_open_orders(symbol)

        # 检查返回结果的类型
        if not isinstance(orders, list):
            logging.warning(f"获取未成交订单返回的不是列表: {type(orders)}, 值: {orders}")
            # 尝试转换为列表
            if isinstance(orders, dict):
                # 检查常见的键名
                if 'resultList' in orders:
                    orders = orders.get('resultList', [])
                    logging.info(f"从 resultList 键中提取到 {len(orders)} 个订单")
                elif 'orders' in orders:
                    orders = orders.get('orders', [])
                    logging.info(f"从 orders 键中提取到 {len(orders)} 个订单")
                elif 'data' in orders:
                    orders = orders.get('data', [])
                    logging.info(f"从 data 键中提取到 {len(orders)} 个订单")
                else:
                    # 如果没有常见键，尝试查找包含列表的任何键
                    for key, value in orders.items():
                        if isinstance(value, list) and len(value) > 0:
                            orders = value
                            logging.info(f"从 {key} 键中提取到 {len(orders)} 个订单")
                            break
                    else:
                        # 如果没有找到列表，返回空列表
                        logging.error(f"无法从返回的字典中提取订单列表，键: {list(orders.keys())}")
                        return []
            else:
                # 如果无法转换，返回空列表
                logging.error(f"返回的数据既不是列表也不是字典: {type(orders)}")
                return []

        # 验证每个订单对象
        valid_orders = []
        for order in orders:
            if isinstance(order, dict) and 'side' in order:
                valid_orders.append(order)
            else:
                logging.warning(f"订单对象格式不正确: {type(order)}, 值: {order}")

        return valid_orders
    except Exception as e:
        logging.error(f"获取未成交订单失败: {str(e)}\n{traceback.format_exc()}")
        return []


def get_market_trades(symbol: str):
    """
    获取市场成交记录并转换为适合execution_history的格式

    参数:
        symbol: 交易对符号，如"usdtusd"

    返回:
        列表，每个元素为元组(timestamp, price, inventory)
    """
    try:
        # 使用spot_market.get_trades获取成交记录
        trades_df = spot_market.get_trades(symbol)

        # 如果返回的DataFrame为空，返回空列表
        if trades_df.empty:
            logging.warning(f"获取{symbol}成交记录失败或无成交记录")
            return []

        # 获取当前库存
        current_inventory = get_inventory()

        # 转换为execution_history需要的格式
        execution_records = []

        for _, row in trades_df.iterrows():
            # 确保必要的字段存在
            if 'ctime' in row and 'price' in row:
                # 将ctime从毫秒转换为秒
                timestamp = row['ctime'] / 1000
                price = float(row['price'])

                # 创建元组并添加到结果列表
                execution_records.append((timestamp, price, current_inventory))

        # 按时间戳排序
        execution_records.sort(key=lambda x: x[0])

        logging.info(f"已获取{len(execution_records)}条市场成交记录")
        return execution_records
    except Exception as e:
        logging.error(f"获取市场成交记录时发生错误: {str(e)}\n{traceback.format_exc()}")
        return []


class BybMarketMaker:
    def __init__(self,
                 base_order_size=1.0,  # 基础订单量
                 num_levels=50,  # 订单簿层数
                 inventory_limit=200000.0,  # 库存限制
                 max_inventory=500000.0,  # 最大库存量
                 price_precision=4,  # 价格精度
                 size_precision=3,  # 数量精度
                 total_side_qty=2500000,  # 单边做市总量
                 risk_aversion=0.7):  # 风险厌恶系数
        """初始化做市商参数"""

        # 库存历史文件路径
        self.inventory_history_file = 'inventory_history.json'

        # 基础参数
        api_inventory = get_inventory()  # 获取API原始库存
        # 计算实际库存（使用更严格的浮点数比较）
        original_value = api_inventory - INITIAL_USDT_INVENTORY

        # 检查是否非常接近初始库存
        if abs(original_value) < 0.0001:
            self.inventory = 0.0  # 如果差值非常小，说明实际库存应为0
            logging.info(f"API库存与初始库存差值很小: {original_value}，初始化库存为0")
        elif abs(original_value - api_inventory) < 0.0001:
            # 如果原始差值接近API库存值，说明可能没有正确减去初始库存
            self.inventory = 0.0
            logging.warning(f"警告: 原始差值 {original_value} 接近API库存 {api_inventory}，可能没有正确减去初始库存，强制设置为0")
        else:
            # 正常情况，使用原始差值的绝对值
            self.inventory = abs(original_value)  # 当前库存（减去初始库存）
            logging.info(f"API库存: {api_inventory}, 初始库存: {INITIAL_USDT_INVENTORY}, 差值: {original_value}, 初始化库存为: {self.inventory}")
        self.inventory_adjustment = 0.0  # 库存理论调整量（不直接修改inventory，而是影响策略）
        self.last_inventory_check = time.time()  # 上次检查库存的时间
        self.inventory_check_interval = 10  # 检查库存的最小间隔（秒）
        self.mid_price = None  # 中间价
        self.pnl = 0.0  # 当前盈亏
        self.base_order_size = float(base_order_size)
        self.num_levels = int(num_levels)
        self.price_step = 0.0001  # 固定价格步长
        self.inventory_limit = float(inventory_limit)
        self.max_inventory = float(max_inventory)
        self.price_precision = int(price_precision)
        self.size_precision = int(size_precision)
        self.total_side_qty = float(total_side_qty)

        # 风险控制参数
        self.risk_aversion = risk_aversion
        self.max_leverage = 8.0  # 最大杠杆
        self.adapt_window = 200  # 自适应窗口大小
        self.position_penalty = 0.0
        self.dynamic_inventory_limit = inventory_limit

        # 库存管理参数
        self.inventory_target = 0.0  # 目标库存水平
        self.inventory_speed = 0.1  # 库存调整速度
        self.inventory_penalty = 0.2  # 库存惩罚因子
        self.inventory_deviation_limit = 0.2  # 库存偏离限制
        self.inventory_reversion_speed = 0.15  # 库存回归速度
        self.inventory_penalty_factor = 0.2  # 库存惩罚因子

        # 风险窗口参数
        self.inventory_risk_window = 50
        self.execution_window = 50
        self.order_imbalance_window = 50
        self.service_window = 50
        self.orderbook_window = 50
        self.orderflow_window = 50
        self.dynamic_risk_window = 50
        self.adaptation_threshold = 20

        # 风险敏感度参数
        self.inventory_risk_aversion = 0.2
        self.execution_risk_aversion = 0.2
        self.depth_aversion = 0.2
        self.liquidity_aversion = 0.2
        self.risk_sensitivity = 0.2
        self.adaptation_rate = 0.1
        self.spread_sensitivity = 0.2
        self.spread_adjustment = 0.1
        self.orderflow_sensitivity = 0.2

        # 服务成本参数
        self.service_cost = 0.0001

        # 历史数据初始化
        self.pnl_history = []  # 盈亏历史
        self.mid_price_history = []  # 中间价历史
        self.vol_history = []  # 波动率历史
        self.recent_prices = []  # 最近价格
        self.execution_history = []  # 成交历史
        self.inventory_risk_history = []  # 库存风险历史
        self.execution_risk_history = []  # 执行风险历史
        self.imbalance_history = []  # 订单失衡历史
        self.imbalance_ma = []  # 订单失衡移动平均
        self.depth_history = []  # 深度历史
        self.liquidity_history = []  # 流动性历史
        self.service_history = []  # 服务成本历史
        self.information_history = []  # 信息不对称历史
        self.orderbook_history = []  # 订单簿历史
        self.orderflow_history = []  # 订单流历史
        self.risk_history = []  # 风险历史
        self.adaptation_history = []  # 自适应历史
        self.spread_history = []  # 价差历史
        self.price_history = []  # 价格历史

        # 初始化成交历史
        self.execution_history = get_market_trades("usdtusd")
        self.last_execution_update = time.time()  # 上次更新成交历史的时间
        self.execution_update_interval = 60  # 更新成交历史的最小间隔（秒）

        # 加载库存历史数据
        self.inventory_history = self.load_inventory_history()  # 库存历史

        # 订单簿状态
        self.orderbook = {'bids': [], 'asks': []}
        self.update_orderbook("usdtusd")  # 初始化订单簿
        self.last_fill_price = None
        self.unfilled_quote_counter = {}
        self.inventory_cost = 0.0
        self.last_unfilled_quotes = None
        self.last_orderbook_update = time.time()  # 上次更新订单簿的时间
        self.orderbook_update_interval = 5  # 更新订单簿的最小间隔（秒）

        # 趋势分析参数
        self.trend_window = 100  # 趋势判断窗口
        self.trend_threshold = 0.01  # 趋势判断阈值
        self.trend_direction = 0  # 趋势方向
        self.price_ma = []  # 价格移动平均
        self.trend_strength = 0.0  # 趋势强度
        self.volatility_ma = []  # 波动率移动平均
        self.price_slope = 0.0  # 价格斜率

        # 趋势相关参数
        self.trend_inventory_factor = 0.15  # 趋势库存调整因子
        self.volatility_threshold = 0.01  # 波动率阈值
        self.slope_threshold = 0.0005  # 斜率阈值
        self.volatility_window = 20  # 波动率计算窗口
        self.slope_window = 30  # 斜率计算窗口

        # 订单控制参数
        self.min_order_size = 100.0  # 最小挂单量
        self.max_order_size = 100000.0  # 最大挂单量
        self.order_size_decay = 0.95  # 挂单量衰减因子
        self.depth_factor = 0.3  # 深度影响因子

        # 价差控制参数
        self.min_spread = 0.0002  # 最小价差
        self.max_spread = 0.001  # 最大价差


    def update_orderbook(self, symbol="usdtusd"):
        """使用 byex.spot.market 的 get_orderbook 方法更新订单簿"""
        try:
            # 获取订单簿数据
            asks_df, bids_df = spot_market.get_orderbook(symbol)

            # 转换为我们需要的格式: [(price, qty), ...]
            asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
            bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))

            # 更新订单簿
            self.orderbook = {'bids': bids, 'asks': asks}

            # 更新中间价（如果有买卖盘数据）
            if bids and asks:
                best_bid = bids[0][0]
                best_ask = asks[0][0]
                self.mid_price = (best_bid + best_ask) / 2

            # 更新时间戳
            self.last_orderbook_update = time.time()

            logging.info(f"订单簿已更新: {len(bids)} 买单, {len(asks)} 卖单")
            return True
        except Exception as e:
            logging.error(f"更新订单簿失败: {str(e)}\n{traceback.format_exc()}")
            return False


    async def _calculate_inventory_risk(self):
        """计算库存风险 (基于Guéant et al., 2012)"""
        if len(self.inventory_history) < self.inventory_risk_window:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values(self.inventory_history[-self.inventory_risk_window:])

        # 计算库存波动率
        recent_inventory = np.array(inventory_values)
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算库存风险
        inventory_risk = self.inventory_risk_aversion * inventory_volatility * abs(self.inventory)
        self.inventory_risk_history.append(inventory_risk)

        return inventory_risk


    async def _calculate_execution_risk(self):
        """计算执行风险 (基于Cartea & Jaimungal, 2015)"""
        # 由于没有执行历史数据，返回默认值
        if len(self.execution_history) < self.execution_window:
            return 0.0

        # 计算执行成本
        recent_executions = np.array(self.execution_history[-self.execution_window:])
        execution_costs = np.diff(recent_executions, axis=0)
        execution_risk = self.execution_risk_aversion * np.std(execution_costs)

        self.execution_risk_history.append(execution_risk)
        return execution_risk


    async def _calculate_order_imbalance(self):
        """计算订单失衡 (基于Avellaneda & Stoikov, 2008)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 0.0

        # 计算订单失衡
        imbalance = (bid_depth - ask_depth) / total_depth
        self.imbalance_history.append(imbalance)

        # 计算移动平均
        if len(self.imbalance_history) >= self.order_imbalance_window:
            ma = np.mean(self.imbalance_history[-self.order_imbalance_window:])
            self.imbalance_ma.append(ma)

        return imbalance


    async def _calculate_depth_risk(self):
        """计算订单簿深度风险 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘深度
        bid_depth = sum(size for _, size in self.orderbook['bids'])
        ask_depth = sum(size for _, size in self.orderbook['asks'])
        total_depth = bid_depth + ask_depth

        if total_depth == 0:
            return 1.0

        # 计算深度风险
        depth_ratio = min(bid_depth, ask_depth) / max(bid_depth, ask_depth)
        depth_risk = 1.0 - self.depth_aversion * depth_ratio

        self.depth_history.append(depth_risk)
        return depth_risk


    async def _calculate_liquidity_risk(self):
        """计算流动性风险 (基于Garman, 1976)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 1.0

        # 计算买卖盘流动性
        bid_liquidity = sum(size for _, size in self.orderbook['bids'])
        ask_liquidity = sum(size for _, size in self.orderbook['asks'])
        total_liquidity = bid_liquidity + ask_liquidity

        if total_liquidity == 0:
            return 1.0

        # 计算流动性风险
        liquidity_ratio = min(bid_liquidity, ask_liquidity) / total_liquidity
        liquidity_risk = 1.0 - self.liquidity_aversion * liquidity_ratio

        self.liquidity_history.append(liquidity_risk)
        return liquidity_risk


    async def _calculate_service_cost(self):
        """计算做市商服务成本 (基于Stoll, 1978，添加时间衰减，值域范围)"""
        if len(self.execution_history) < self.service_window:
            return self.service_cost

        # 获取最近的成交记录
        recent_executions = self.execution_history[-self.service_window:]
        service_costs = []

        # 计算时间衰减权重（使用较慢的衰减）
        time_weights = np.exp(-0.05 * np.arange(len(recent_executions)))
        time_weights = time_weights / np.sum(time_weights)

        for i, (_, price, _) in enumerate(recent_executions):
            if self.mid_price is not None:
                # 基础成本计算（只考虑价格偏离）
                base_cost = abs(price - self.mid_price) / self.mid_price

                # 应用时间衰减
                final_cost = base_cost * time_weights[i]

                service_costs.append(final_cost)

        if not service_costs:
            return self.service_cost

        # 使用加权平均计算最终服务成本
        weighted_avg_cost = np.average(service_costs, weights=time_weights)

        # 应用平滑处理（更保守的平滑）
        if len(self.service_history) > 0:
            last_cost = self.service_history[-1]
            smoothed_cost = 0.8 * last_cost + 0.2 * weighted_avg_cost
        else:
            smoothed_cost = weighted_avg_cost

        # 确保服务成本在更保守的范围内
        min_cost = self.service_cost * 0.7  # 最小不低于基础成本的70%
        max_cost = self.service_cost * 2  # 最大不超过基础成本的200%
        final_cost = max(min_cost, min(max_cost, smoothed_cost))

        self.service_history.append(final_cost)
        return final_cost


    async def _calculate_information_asymmetry(self):
        """计算信息不对称程度 (基于O'Hara, 1995)"""
        if len(self.orderbook['bids']) == 0 or len(self.orderbook['asks']) == 0:
            return 0.0

        # 计算买卖盘价差
        best_bid = self.orderbook['bids'][0][0]
        best_ask = self.orderbook['asks'][0][0]
        spread = best_ask - best_bid

        if self.mid_price is None:
            return 0.0

        # 计算信息不对称指标
        spread_ratio = spread / self.mid_price
        imbalance = await self._calculate_order_imbalance()

        # 结合价差和订单失衡计算信息不对称
        information_asymmetry = spread_ratio * (1 + abs(imbalance))
        self.information_history.append(information_asymmetry)

        return information_asymmetry


    async def _calculate_orderbook_dynamics(self):
        """计算订单簿动态 (基于Foucault et al., 2005)"""
        if len(self.orderbook_history) < self.orderbook_window:
            return 0.0

        # 计算订单簿变化率
        recent_changes = []
        for i in range(1, len(self.orderbook_history)):
            prev = self.orderbook_history[i - 1]
            curr = self.orderbook_history[i]
            change = abs(curr - prev) / (prev + 1e-8)
            recent_changes.append(change)

        if not recent_changes:
            return 0.0

        # 计算订单簿动态指标
        dynamics = np.mean(recent_changes)
        self.orderbook_history.append(dynamics)

        return dynamics


    async def _calculate_orderflow(self):
        """计算订单流 (基于Cont et al., 2010)"""
        # 由于没有执行历史数据，返回默认值
        if len(self.orderflow_history) > 0:
            # 如果有历史数据，使用最后一个值
            return self.orderflow_history[-1]
        else:
            # 否则使用默认值
            return 0.0


    async def _calculate_dynamic_risk(self):
        """计算动态风险 (基于Baldacci et al., 2021)"""
        if len(self.inventory_history) < self.dynamic_risk_window:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values(self.inventory_history[-self.dynamic_risk_window:])

        # 计算库存波动率
        recent_inventory = np.array(inventory_values)
        inventory_volatility = np.std(recent_inventory) / (np.mean(abs(recent_inventory)) + 1e-8)

        # 计算价格波动率
        if len(self.price_history) >= self.dynamic_risk_window:
            recent_prices = np.array(self.price_history[-self.dynamic_risk_window:])
            price_volatility = np.std(recent_prices) / np.mean(recent_prices)
        else:
            price_volatility = 0.0

        # 计算综合风险
        risk = self.risk_sensitivity * (inventory_volatility + price_volatility)
        self.risk_history.append(risk)

        return risk


    async def _calculate_adaptation_factor(self):
        """计算自适应因子 (基于Cartea et al., 2021)"""
        if len(self.risk_history) < self.adaptation_threshold:
            return 1.0

        # 计算风险趋势
        recent_risk = np.array(self.risk_history[-self.adaptation_threshold:])
        risk_trend = np.mean(np.diff(recent_risk))

        # 计算自适应因子
        adaptation = 1.0 - self.adaptation_rate * abs(risk_trend)
        self.adaptation_history.append(adaptation)

        return max(0.5, min(1.5, adaptation))


    async def _calculate_inventory_balance(self):
        """计算库存平衡调整 (基于Guéant et al., 2012)"""
        if len(self.inventory_history) < 2:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values()

        # 计算累计库存（确保库存值为正数）
        if inventory_values:
            # 将负库存值转换为正值（表示库存减少）
            positive_inventory_values = [abs(val) for val in inventory_values]
            cumulative_inventory = np.cumsum(positive_inventory_values)
            current_cumulative = cumulative_inventory[-1]
        else:
            current_cumulative = 0

        # 计算库存偏离
        inventory_deviation = current_cumulative - self.inventory_target

        # 计算调整量
        adjustment = -self.inventory_speed * inventory_deviation

        # 应用惩罚因子
        if abs(inventory_deviation) > self.inventory_limit:
            adjustment *= (1 + self.inventory_penalty)

        return adjustment


    async def _calculate_spread_adjustment(self):
        """计算价差调整 (基于Avellaneda & Stoikov, 2008)"""
        if len(self.spread_history) < 2:
            return 0.0

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values()

        # 计算累计库存（确保库存值为正数）
        if inventory_values:
            # 将负库存值转换为正值（表示库存减少）
            positive_inventory_values = [abs(val) for val in inventory_values]
            cumulative_inventory = np.cumsum(positive_inventory_values)
            current_cumulative = cumulative_inventory[-1]
        else:
            current_cumulative = 0

        # 计算库存影响
        inventory_ratio = current_cumulative / self.max_inventory
        inventory_impact = self.spread_sensitivity * inventory_ratio

        # 计算风险影响
        risk_factor = await self._calculate_dynamic_risk()
        risk_impact = self.spread_adjustment * risk_factor

        # 计算总调整
        adjustment = inventory_impact + risk_impact
        self.spread_history.append(adjustment)

        return adjustment


    async def _calculate_orderflow_impact(self):
        """计算订单流影响 (基于Cartea & Jaimungal, 2015)"""
        if len(self.orderflow_history) < self.orderflow_window:
            return 0.0

        # 计算订单流趋势
        recent_flow = np.array(self.orderflow_history[-self.orderflow_window:])
        flow_trend = np.mean(recent_flow)

        # 计算影响因子
        impact = self.orderflow_sensitivity * flow_trend

        return impact


    async def _update_dynamic_limits(self):
        """更新动态限制 (综合多个模型)"""
        # 重置库存调整量
        self.inventory_adjustment = 0.0

        # 计算各项指标
        dynamic_risk = await self._calculate_dynamic_risk()
        adaptation_factor = await self._calculate_adaptation_factor()
        inventory_balance = await self._calculate_inventory_balance()
        spread_adjustment = await self._calculate_spread_adjustment()
        orderflow_impact = await self._calculate_orderflow_impact()

        # 检查上次获取库存的时间，确保有最小间隔
        current_time = time.time()
        time_since_last_check = current_time - self.last_inventory_check

        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values()

        # 计算累计库存（确保库存值为正数）
        if inventory_values:
            # 将负库存值转换为正值（表示库存减少）
            positive_inventory_values = [abs(val) for val in inventory_values]
            cumulative_inventory = np.cumsum(positive_inventory_values)
            current_cumulative = cumulative_inventory[-1]
        else:
            current_cumulative = 0

        # 计算库存偏离
        inventory_deviation = abs(current_cumulative) / self.max_inventory

        # 如果库存偏离过大，计算调整量但不直接修改库存
        adjustment = 0.0
        if inventory_deviation > self.inventory_deviation_limit:
            # 计算理论上需要调整的量
            adjustment = -np.sign(current_cumulative) * self.inventory_reversion_speed * self.max_inventory
            # 记录日志，但不直接修改库存
            logging.info(f"库存偏离过大: {inventory_deviation:.4f}，理论调整量: {adjustment:.4f}")
            # 只有当距离上次检查超过最小间隔时才获取最新库存
            if time_since_last_check >= self.inventory_check_interval:
                self.update_inventory()
                self.last_inventory_check = current_time
                logging.info(f"已更新库存，间隔: {time_since_last_check:.1f}秒")
            else:
                logging.info(f"距离上次检查库存仅 {time_since_last_check:.1f} 秒，不获取新库存")

        # 保存调整量供策略使用
        self.inventory_adjustment = adjustment

        # 计算综合调整
        total_adjustment = (
                inventory_balance * adaptation_factor +
                spread_adjustment * (1 + dynamic_risk) +
                orderflow_impact
        )

        # 更新动态限制
        self.dynamic_inventory_limit = max(
            self.inventory_limit * 0.5,
            min(
                self.inventory_limit * 1.5,
                self.inventory_limit * (1 + total_adjustment)
            )
        )

        # 确保累计库存不超过限制
        if abs(current_cumulative) > self.dynamic_inventory_limit:
            # 计算理论上的新库存值（确保为正值）
            theoretical_inventory = abs(np.sign(current_cumulative) * (self.dynamic_inventory_limit - abs(current_cumulative)))
            # 计算理论上的调整量
            limit_adjustment = theoretical_inventory - abs(self.inventory)
            # 记录日志，但不直接设置新的库存值
            logging.info(f"累计库存超过限制: {abs(current_cumulative):.4f} > {self.dynamic_inventory_limit:.4f}，理论库存值: {theoretical_inventory:.4f}，调整量: {limit_adjustment:.4f}")
            # 更新调整量（取更大的调整量，确保限制生效）
            if abs(limit_adjustment) > abs(self.inventory_adjustment):
                self.inventory_adjustment = limit_adjustment
                logging.info(f"更新库存调整量为: {self.inventory_adjustment:.4f}")
            # 只有当距离上次检查超过最小间隔时才获取最新库存
            if time_since_last_check >= self.inventory_check_interval:
                self.update_inventory()
                self.last_inventory_check = current_time
                logging.info(f"已更新库存，间隔: {time_since_last_check:.1f}秒")
            else:
                logging.info(f"距离上次检查库存仅 {time_since_last_check:.1f} 秒，不获取新库存")

        # 更新历史数据
        self.risk_history.append(dynamic_risk)
        self.adaptation_history.append(adaptation_factor)
        self.spread_history.append(spread_adjustment)
        self.orderflow_history.append(orderflow_impact)


    async def _calculate_position_penalty(self):
        """计算仓位惩罚系数 (基于Avellaneda & Stoikov, 2008)"""
        # 从库存历史中提取库存值
        inventory_values = self._extract_inventory_values()

        # 计算累计仓位（确保库存值为正数）
        if inventory_values:
            # 将负库存值转换为正值（表示库存减少）
            positive_inventory_values = [abs(val) for val in inventory_values]
            cumulative_inventory = np.cumsum(positive_inventory_values)
            current_cumulative = cumulative_inventory[-1]
        else:
            current_cumulative = 0

        position_ratio = abs(current_cumulative) / (self.dynamic_inventory_limit + 1e-8)

        # 计算库存风险
        inventory_risk = await self._calculate_inventory_risk()

        # 计算执行风险
        execution_risk = await self._calculate_execution_risk()

        # 计算综合风险惩罚
        risk_penalty = (inventory_risk + execution_risk) * self.risk_aversion

        # 使用更陡峭的惩罚函数
        self.position_penalty = tanh(position_ratio ** 3) * self.risk_aversion * 3 + risk_penalty

        # 波动率调整
        if len(self.vol_history) > 5:
            vol_factor = np.mean(self.vol_history[-5:]) * 150
            self.position_penalty *= 1 + vol_factor

        # 接近限制时更快增加惩罚
        if position_ratio > 0.5:
            self.position_penalty *= 3


    async def estimate_volatility(self, recent_prices):
        """估计价格波动率"""
        return np.std(recent_prices) if len(recent_prices) >= 2 else 0.0


    async def update_mid_price(self, fill_price):
        """更新中间价"""
        self.mid_price = fill_price


    async def _calculate_order_size(self, base_size, level, is_bid):
        """计算订单量，确保不会出现0挂单量，并引入随机性"""
        # 基础衰减
        decayed_size = base_size * (self.order_size_decay ** level)

        # 确保最小挂单量
        min_size = max(self.min_order_size, self.base_order_size)
        decayed_size = max(min_size, decayed_size)

        # 考虑市场深度影响
        if len(self.orderbook['bids']) > 0 and len(self.orderbook['asks']) > 0:
            total_depth = sum(size for _, size in self.orderbook['bids']) + sum(
                size for _, size in self.orderbook['asks'])
            depth_ratio = total_depth / (self.total_side_qty * 2)
            depth_adjustment = 1.0 + self.depth_factor * (1 - depth_ratio)
            decayed_size *= depth_adjustment

        # 考虑仓位影响
        inventory_ratio = abs(self.inventory) / self.max_inventory
        if inventory_ratio > 0.5:
            if (is_bid and self.inventory > 0) or (not is_bid and self.inventory < 0):
                decayed_size *= 0.5
            else:
                decayed_size *= 1.5

        # 添加随机性，使每个订单的大小略有不同
        # 对于买单和卖单使用不同的随机因子
        if is_bid:
            random_factor = 1.0 + random.uniform(-0.08, 0.08)  # 买单随机浮动正负8%
        else:
            random_factor = 1.0 + random.uniform(-0.08, 0.08)  # 卖单随机浮动正负8%

        decayed_size *= random_factor

        # 确保在合理范围内
        decayed_size = min(max(decayed_size, min_size), self.max_order_size)

        return round(decayed_size, self.size_precision)


    async def generate_layered_quotes(self):
        """生成分层报价"""
        # 设置最低服务成本
        current_service_cost = await self._calculate_service_cost()
        self.service_cost = max(0.0001, current_service_cost)

        if self.mid_price is None:
            return [], []

        # 更新动态参数
        await self._update_dynamic_limits()
        await self._calculate_position_penalty()

        # 计算各层权重
        levels = np.arange(1, self.num_levels + 1)

        # 创建两种不同的权重：
        # 1. 前十档使用递增权重（由小到大）
        # 2. 其余档位使用递减权重（由大到小）
        front_levels = np.arange(1, 11)  # 前十档
        back_levels = np.arange(11, self.num_levels + 1)  # 剩余档位

        # 前十档权重递增（使用指数增长函数，增长更加明显）
        # 使用幂函数来实现更陡岭的增长
        # 第一档的权重非常小，然后快速增长
        power = 2.5  # 幂指数，越大增长越陡岭
        min_weight = 0.05  # 第一档的最小权重

        # 使用幂函数计算权重，确保第一档权重为min_weight，第十档权重为1
        normalized_levels = (front_levels - 1) / 9.0  # 归一化到[0,1]范围
        front_weights = min_weight + (1 - min_weight) * (normalized_levels ** power)

        # 记录权重计算过程
        logging.debug(f"前十档权重计算: power={power}, min_weight={min_weight}")
        logging.debug(f"归一化级别: {normalized_levels}")
        logging.debug(f"计算结果: {front_weights}")

        # 剩余档位权重递减（使用指数衰减函数）
        # 确保与前十档的最后一个权重连续
        decay_rate = 0.05
        back_weights = np.exp(-decay_rate * (back_levels - 10))

        # 合并权重并归一化
        weights = np.zeros(self.num_levels)
        weights[:10] = front_weights
        if len(back_levels) > 0:  # 确保有剩余档位
            weights[10:] = back_weights
        weights /= weights.sum()  # 归一化确保总和为1

        # 计算目标挂单量，买卖双方不对称
        # 买单和卖单的数量比例随机浮动，确保不完全一样
        bid_ratio = 1.0 + random.uniform(-0.15, 0.15)  # 买单比例随机浮动正负15%
        ask_ratio = 1.0 + random.uniform(-0.15, 0.15)  # 卖单比例随机浮动正负15%

        # 确保买卖单比例不同
        if abs(bid_ratio - ask_ratio) < 0.05:
            # 如果比例差异太小，增加差异
            ask_ratio += 0.1

        logging.info(f"买单比例: {bid_ratio:.4f}, 卖单比例: {ask_ratio:.4f}")

        target_bid_sizes = self.total_side_qty * weights * bid_ratio
        target_ask_sizes = self.total_side_qty * weights * ask_ratio

        # 记录前十档权重分布
        logging.info("=== 前十档权重分布 ===")
        for i in range(10):
            logging.info(f"档位 {i+1}: 权重={weights[i]:.4f}, 目标买量={target_bid_sizes[i]:.2f}, 目标卖量={target_ask_sizes[i]:.2f}")

        # 计算并显示前十档的权重比例
        first_weight = weights[0]
        logging.info("=== 前十档权重比例（相对于第一档） ===")
        for i in range(10):
            ratio = weights[i] / first_weight
            logging.info(f"档位 {i+1}: 权重比例 = {ratio:.2f}x")

        # 计算并显示相邻档位的权重增长比例
        logging.info("=== 相邻档位的权重增长比例 ===")
        for i in range(1, 10):
            growth_ratio = weights[i] / weights[i-1]
            logging.info(f"档位 {i} -> {i+1}: 增长比例 = {growth_ratio:.2f}x")
        bids = []
        asks = []

        # 根据库存调整量计算报价偏移
        inventory_bias = 0.0
        if abs(self.inventory_adjustment) > 0.001:  # 如果有显著的调整量
            # 将调整量转换为价格偏移百分比
            inventory_bias = self.inventory_adjustment / self.max_inventory * 0.01
            logging.info(f"库存调整量 {self.inventory_adjustment:.4f} 转换为价格偏移: {inventory_bias:.6f}")

        # 设置最小价差，确保买卖单不会交叉
        min_spread = max(self.min_spread, self.price_step * 2)  # 确保最小价差至少为两倍价格步长

        # 创建不同档位的价格步长因子，使价差随档位增加而增加
        # 前十档使用幂函数增长，后续档位使用线性增长
        price_step_factors = np.zeros(self.num_levels)

        # 前十档使用幂函数增长，使价差更加明显
        front_power = 1.5  # 幂指数，越大价差增长越陡岭
        front_levels = np.arange(1, 11)
        price_step_factors[:10] = np.power(front_levels, front_power)

        # 后续档位使用线性增长，但起点为前十档的最后一个因子
        if self.num_levels > 10:
            back_levels = np.arange(11, self.num_levels + 1)
            back_step = price_step_factors[9] / 10  # 确保后续档位的增长速度适中
            price_step_factors[10:] = price_step_factors[9] + back_step * (back_levels - 10)

        # 记录价格步长因子
        logging.info("=== 价格步长因子 ===")
        for i in range(min(10, self.num_levels)):
            logging.info(f"档位 {i+1}: 价格步长因子 = {price_step_factors[i]:.2f}")

        # 生成各层报价
        for i in range(self.num_levels):
            # 计算报价价格，并考虑库存调整偏移
            service_cost_factor = 1 + self.service_cost
            # 使用动态价格步长因子计算价格
            dynamic_step = self.price_step * price_step_factors[i] * service_cost_factor

            # 库存调整量为正时，需要减少库存，因此降低买单价格、提高卖单价格
            # 库存调整量为负时，需要增加库存，因此提高买单价格、降低卖单价格
            bid_price = self.mid_price * (1 - dynamic_step - inventory_bias)
            ask_price = self.mid_price * (1 + dynamic_step + inventory_bias)

            # 确保价格为正且合理
            min_price = self.mid_price * 0.5  # 最低价格为中间价的50%
            max_price = self.mid_price * 1.5  # 最高价格为中间价的150%

            bid_price = max(min_price, min(bid_price, self.mid_price))
            ask_price = min(max_price, max(ask_price, self.mid_price))

            # 价格精度处理
            bid_price = round(bid_price, self.price_precision)
            ask_price = round(ask_price, self.price_precision)

            # 确保价格单调性
            if i > 0:
                if bid_price >= bids[-1][0]:
                    bid_price = round(bids[-1][0] * 0.999, self.price_precision)
                if ask_price <= asks[-1][0]:
                    ask_price = round(asks[-1][0] * 1.001, self.price_precision)

            # 计算挂单量，并考虑库存调整
            # 如果需要减少库存（调整量为正），增加卖单量、减少买单量
            # 如果需要增加库存（调整量为负），增加买单量、减少卖单量
            inventory_size_factor = 1.0
            if abs(self.inventory_adjustment) > 0.001:
                # 计算数量调整因子，最多调整正负30%
                inventory_size_factor = 1.0 + min(0.3, abs(self.inventory_adjustment / self.max_inventory))

            if self.inventory_adjustment > 0:  # 需要减少库存
                bid_size = await self._calculate_order_size(target_bid_sizes[i] / inventory_size_factor, i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i] * inventory_size_factor, i, False)
            elif self.inventory_adjustment < 0:  # 需要增加库存
                bid_size = await self._calculate_order_size(target_bid_sizes[i] * inventory_size_factor, i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i] / inventory_size_factor, i, False)
            else:  # 不需要调整
                bid_size = await self._calculate_order_size(target_bid_sizes[i], i, True)
                ask_size = await self._calculate_order_size(target_ask_sizes[i], i, False)

            bids.append((bid_price, bid_size))
            asks.append((ask_price, ask_size))

        # 确保单边做市总量不超过total_side_qty，并且买卖双方总量不完全相同
        total_bid = sum(s for _, s in bids)
        total_ask = sum(s for _, s in asks)

        # 计算调整比例，并引入小的随机浮动
        bid_target = self.total_side_qty * (1.0 + random.uniform(-0.05, 0.05))  # 买单目标总量浮动正负5%
        ask_target = self.total_side_qty * (1.0 + random.uniform(-0.05, 0.05))  # 卖单目标总量浮动正负5%

        # 确保买卖单目标总量不同
        if abs(bid_target - ask_target) < self.total_side_qty * 0.02:  # 如果差异小于2%
            ask_target += self.total_side_qty * 0.03  # 增加3%的差异

        logging.info(f"买单目标总量: {bid_target:.2f}, 卖单目标总量: {ask_target:.2f}")

        # 计算调整比例
        bid_scale = min(1.0, bid_target / (total_bid + 1e-8))
        ask_scale = min(1.0, ask_target / (total_ask + 1e-8))

        # 应用调整
        bids = [(price, round(size * bid_scale, self.size_precision)) for price, size in bids]
        asks = [(price, round(size * ask_scale, self.size_precision)) for price, size in asks]

        # 确保买卖单不会交叉（买单最高价低于卖单最低价）
        if bids and asks:
            best_bid = max([price for price, _ in bids])
            best_ask = min([price for price, _ in asks])

            # 如果买单最高价大于等于卖单最低价，调整价格
            if best_bid >= best_ask:
                logging.warning(f"检测到买卖单价格交叉: 最高买价={best_bid}, 最低卖价={best_ask}")

                # 计算中间价
                mid_price = (best_bid + best_ask) / 2

                # 计算最小价差（确保至少为两倍价格步长）
                min_price_gap = max(self.min_spread * self.mid_price, self.price_step * 2)

                # 调整买卖价格
                new_best_bid = round(mid_price - min_price_gap/2, self.price_precision)
                new_best_ask = round(mid_price + min_price_gap/2, self.price_precision)

                # 确保调整后的价格有效（买价低于卖价）
                if new_best_bid >= new_best_ask:
                    # 如果仍然交叉，进一步增加价差
                    new_best_bid = round(mid_price * (1 - self.min_spread), self.price_precision)
                    new_best_ask = round(mid_price * (1 + self.min_spread), self.price_precision)

                logging.info(f"调整后的价格: 最高买价={new_best_bid}, 最低卖价={new_best_ask}")

                # 更新买单价格
                bid_prices = [price for price, _ in bids]
                max_bid_idx = bid_prices.index(best_bid)
                bid_sizes = [size for _, size in bids]
                bids[max_bid_idx] = (new_best_bid, bid_sizes[max_bid_idx])

                # 更新卖单价格
                ask_prices = [price for price, _ in asks]
                min_ask_idx = ask_prices.index(best_ask)
                ask_sizes = [size for _, size in asks]
                asks[min_ask_idx] = (new_best_ask, ask_sizes[min_ask_idx])

                # 重新排序买卖单
                bids = sorted(bids, key=lambda x: -x[0])  # 买单按价格降序
                asks = sorted(asks, key=lambda x: x[0])   # 卖单按价格升序

        # 不直接覆盖 self.orderbook，而是将生成的报价保存到单独的变量
        self.generated_quotes = {'bids': bids, 'asks': asks}

        # 记录报价信息到日志
        logging.info(f"当前中间价: {self.mid_price}")
        logging.info("=== 买单报价 ===")
        for i, (price, size) in enumerate(bids[:10]):  # 只记录前10档
            logging.info(f"买单档位 {i+1}: 价格={price:.4f}, 数量={size:.3f}")

        logging.info("=== 卖单报价 ===")
        for i, (price, size) in enumerate(asks[:10]):  # 只记录前10档
            logging.info(f"卖单档位 {i+1}: 价格={price:.4f}, 数量={size:.3f}")

        # 记录每个档位的价差和相对中间价的偏移
        logging.info("=== 档位价差分析 ===")
        for i in range(min(10, len(bids))):  # 只分析前10档
            if i < len(bids) and i < len(asks):
                bid_price = bids[i][0]
                ask_price = asks[i][0]
                price_diff = ask_price - bid_price
                bid_offset = (self.mid_price - bid_price) / self.mid_price * 100  # 买单相对中间价的偏移百分比
                ask_offset = (ask_price - self.mid_price) / self.mid_price * 100  # 卖单相对中间价的偏移百分比
                logging.info(f"档位 {i+1}: 买价={bid_price:.4f}, 卖价={ask_price:.4f}, 价差={price_diff:.4f}, 买单偏移={bid_offset:.2f}%, 卖单偏移={ask_offset:.2f}%")

                # 计算相邻档位的价差
                if i > 0 and i-1 < len(bids) and i-1 < len(asks):
                    prev_bid_price = bids[i-1][0]
                    prev_ask_price = asks[i-1][0]
                    bid_step = prev_bid_price - bid_price
                    ask_step = ask_price - prev_ask_price
                    logging.info(f"  档位 {i} -> {i+1} 价格步长: 买单={bid_step:.4f}, 卖单={ask_step:.4f}")

        # 记录总量信息
        total_bid_volume = sum(size for _, size in bids)
        total_ask_volume = sum(size for _, size in asks)
        logging.info(f"买单总量: {total_bid_volume:.3f}, 卖单总量: {total_ask_volume:.3f}")

        # 记录价差信息
        if bids and asks:
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            spread = best_ask - best_bid
            spread_ratio = spread / self.mid_price
            logging.info(f"最优买卖价差: {spread:.4f} ({spread_ratio*100:.2f}%)")

        return bids, asks


    async def show_quotes(self, top_n=50, bids=None, asks=None):
        """显示订单簿

        参数:
            top_n: 显示的最大档位数
            bids: 可选的买单列表，如果不提供则使用 self.orderbook['bids']
            asks: 可选的卖单列表，如果不提供则使用 self.orderbook['asks']
        """
        # 如果没有提供买卖单，则使用订单簿数据
        if bids is None:
            bids = self.orderbook['bids']
        if asks is None:
            asks = self.orderbook['asks']

        bid_data = bids[:top_n]
        ask_data = asks[:top_n]

        bid_data = sorted(bid_data, key=lambda x: -x[0])
        ask_data = sorted(ask_data, key=lambda x: x[0])

        logging.info(f"\n{'买价':>12} {'买量':>10} | {'卖价':>10} {'卖量':>10}")
        logging.info("-" * 48)

        # 只记录前10档到日志，避免日志过大
        display_n = min(10, top_n)
        for i in range(display_n):
            bid_price, bid_size = bid_data[i] if i < len(bid_data) else ('', '')
            ask_price, ask_size = ask_data[i] if i < len(ask_data) else ('', '')

            bid_price_str = f"{bid_price:.4f}" if bid_price != '' else ''
            bid_size_str = f"{bid_size:.3f}" if bid_size != '' else ''
            ask_price_str = f"{ask_price:.4f}" if ask_price != '' else ''
            ask_size_str = f"{ask_size:.3f}" if ask_size != '' else ''

            logging.info(f"{bid_price_str:>12} {bid_size_str:>10} | {ask_price_str:>10} {ask_size_str:>10}")


    async def _update_trend_analysis(self):
        """更新趋势分析"""
        if len(self.recent_prices) < self.trend_window:
            return

        # 计算移动平均 (使用指数加权移动平均)
        prices = np.array(self.recent_prices[-self.trend_window:])
        ma = np.mean(prices)
        self.price_ma.append(ma)

        # 计算价格斜率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.slope_window:
            recent_prices = np.array(self.recent_prices[-self.slope_window:])
            x = np.arange(len(recent_prices))
            slope, _ = np.polyfit(x, recent_prices, 1)
            self.price_slope = slope / recent_prices[0]  # 归一化斜率

        # 计算波动率 (使用更短的时间窗口)
        if len(self.recent_prices) >= self.volatility_window:
            recent_prices = np.array(self.recent_prices[-self.volatility_window:])
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            self.volatility_ma.append(volatility)

        # 判断趋势方向 (更严格的判断条件)
        if len(self.volatility_ma) > 0:
            current_vol = self.volatility_ma[-1]
            if self.price_slope > self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = 1
                self.trend_strength = min(1.0, self.price_slope / self.slope_threshold)
            elif self.price_slope < -self.slope_threshold and current_vol < self.volatility_threshold:
                self.trend_direction = -1
                self.trend_strength = min(1.0, abs(self.price_slope) / self.slope_threshold)
            else:
                self.trend_direction = 0
                self.trend_strength = 0.0


    async def _calculate_trend_inventory_adjustment(self):
        """计算基于趋势的库存调整"""
        if self.trend_direction == 0 or self.mid_price is None or len(self.volatility_ma) == 0:
            return 0.0

        # 计算基础调整量 (考虑波动率影响)
        current_vol = self.volatility_ma[-1]
        vol_factor = max(0.3, 1 - current_vol / self.volatility_threshold)  # 更保守的波动率调整
        base_adjustment = self.trend_inventory_factor * self.trend_strength * vol_factor

        # 根据趋势方向和波动率调整
        if self.trend_direction == 1:  # 上升趋势
            # 在低波动率且斜率较大时增加库存
            if current_vol < self.volatility_threshold and self.price_slope > self.slope_threshold:
                return base_adjustment * 1.1  # 更保守的调整
            else:
                return base_adjustment
        else:  # 下降趋势
            # 在低波动率且斜率较大时减少库存
            if current_vol < self.volatility_threshold and self.price_slope < -self.slope_threshold:
                return -base_adjustment * 1.1  # 更保守的调整
            else:
                return -base_adjustment


    async def cancel_all_orders(self, symbol: str="usdtusd"):
        """取消所有未成交订单"""
        try:
            orders_to_cancel = list(active_orders)
            spot_client.cancel_all_orders_by_symbol(symbol)
            for order_id in orders_to_cancel:
                active_orders.remove(order_id)
                logging.info(f"成功取消订单: {order_id}")
            active_orders.clear()
            # 清空按价格分组的订单ID字典
            current_order_ids_grouped["buy"] = {}
            current_order_ids_grouped["sell"] = {}
        except Exception as e:
            logging.error(f"批量取消订单时发生错误: {str(e)}\n{traceback.format_exc()}")


    def save_inventory_history(self):
        """保存库存历史到JSON文件"""
        try:
            # 库存历史数据应该全部是 (timestamp, inventory) 格式
            inventory_data = []
            for item in self.inventory_history:
                if isinstance(item, tuple) or isinstance(item, list):
                    inventory_data.append(item)

            # 转换为可序列化的格式
            serializable_data = []
            for timestamp, inventory in inventory_data:
                serializable_data.append({
                    'timestamp': timestamp,
                    'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    'inventory': float(inventory)
                })

            # 保存到JSON文件
            with open(self.inventory_history_file, 'w') as f:
                json.dump(serializable_data, f, indent=2)

            logging.info(f"库存历史已保存到 {self.inventory_history_file}")
            return True
        except Exception as e:
            logging.error(f"保存库存历史时发生错误: {str(e)}\n{traceback.format_exc()}")
            return False


    def load_inventory_history(self):
        """从JSON文件加载库存历史"""
        try:
            if not os.path.exists(self.inventory_history_file):
                logging.info(f"库存历史文件 {self.inventory_history_file} 不存在，将创建新的历史记录")
                return []

            with open(self.inventory_history_file, 'r') as f:
                data = json.load(f)

            # 转换为程序内部使用的格式 [(timestamp, inventory), ...]
            inventory_history = [(item['timestamp'], item['inventory']) for item in data]
            logging.info(f"已从 {self.inventory_history_file} 加载 {len(inventory_history)} 条库存历史记录")
            return inventory_history
        except Exception as e:
            logging.error(f"加载库存历史时发生错误: {str(e)}\n{traceback.format_exc()}")
            return []

    def _extract_inventory_values(self, history_items=None):
        """从库存历史中提取库存值

        参数:
            history_items: 库存历史数据，如果为 None，则使用 self.inventory_history

        返回:
            库存值列表，只包含数值部分
        """
        if history_items is None:
            history_items = self.inventory_history

        inventory_values = []
        for item in history_items:
            if isinstance(item, tuple) or isinstance(item, list):
                # 如果是元组格式 (timestamp, inventory)
                # 确保库存值为正数（负值表示库存减少）
                inventory_value = item[1]
                inventory_values.append(inventory_value)
        return inventory_values


    def calculate_actual_inventory(self, api_inventory=None, coin='usdt'):
        """计算实际库存，从 API 库存中减去挂单量和初始库存

        参数:
            api_inventory: 从 API 获取的原始库存值，如果为 None 则自动获取
            coin: 货币类型，默认为 'usdt'

        返回:
            实际库存值（原始库存 - 初始库存 - 挂单量）
        """
        try:
            # 如果没有提供 API 库存值，则获取
            if api_inventory is None:
                api_inventory = get_inventory(coin)

            # 获取当前未成交的挂单
            pending_orders = get_pending_orders("usdtusd")

            # 计算挂单量
            pending_buy_volume = 0.0
            pending_sell_volume = 0.0

            # 检查pending_orders是否为列表且包含字典对象
            if isinstance(pending_orders, list):
                for order in pending_orders:
                    # 检查order是否为字典对象
                    if isinstance(order, dict):
                        # 检查是否包含侧向信息
                        side = None
                        if 'side' in order:
                            side = order.get('side')
                        elif 'side_msg' in order:
                            # 如果有中文侧向信息
                            side_msg = order.get('side_msg')
                            if side_msg == '买入' or side_msg == '买入':
                                side = 'BUY'
                            elif side_msg == '卖出' or side_msg == '卖出':
                                side = 'SELL'

                        # 检查价格和数量字段
                        price_field = 'price' if 'price' in order else 'avg_price' if 'avg_price' in order else None
                        volume_field = 'volume' if 'volume' in order else 'remain_volume' if 'remain_volume' in order else None

                        if side and price_field and volume_field:
                            price = float(order.get(price_field, 0))
                            volume = float(order.get(volume_field, 0))

                            if side == 'BUY':
                                # 买单会减少库存（因为要使用资金购买）
                                pending_buy_volume += price * volume  # 买单需要的资金
                                logging.info(f"计算买单挂单量: 价格={price}, 数量={volume}, 总金额={price * volume}")
                            elif side == 'SELL':
                                # 卖单不影响库存（因为已经锁定了资金）
                                logging.info(f"卖单不影响库存: 价格={price}, 数量={volume}")
                        else:
                            logging.warning(f"订单缺少必要字段: side={side}, price_field={price_field}, volume_field={volume_field}, 订单={order}")
                    else:
                        # 如果order不是字典对象，记录日志
                        logging.warning(f"挂单对象格式不正确: {type(order)}, 值: {order}")
            else:
                # 如果pending_orders不是列表，记录日志
                logging.warning(f"未成交挂单格式不正确: {type(pending_orders)}, 值: {pending_orders}")

            # 获取初始库存量
            initial_inventory = INITIAL_USDT_INVENTORY if coin.lower() == 'usdt' else INITIAL_USD_INVENTORY

            # 计算实际库存（API库存 - 初始库存 - 挂单量）
            # 先计算原始差值
            original_value = api_inventory - initial_inventory - pending_buy_volume

            # 检查是否非常接近初始库存（考虑浮点数精度问题）
            if abs(original_value) < 0.0001 or abs(api_inventory - initial_inventory) < 0.0001:
                # 如果差值非常小，说明实际库存应为0
                actual_inventory = 0.0
                logging.info(f"API库存与初始库存差值很小: {original_value}，返回0作为实际库存")
            elif abs(api_inventory - initial_inventory) > 0.0001 and abs(original_value - api_inventory) < 0.0001:
                # 如果原始差值接近API库存值，说明可能没有正确减去初始库存
                actual_inventory = 0.0
                logging.warning(f"警告: 原始差值 {original_value} 接近API库存 {api_inventory}，可能没有正确减去初始库存，强制返回0")
            else:
                # 正常情况，使用原始差值的绝对值
                actual_inventory = abs(original_value)
                logging.info(f"原始库存计算值: {original_value}，转换为正值: {actual_inventory}")

            logging.info(f"API库存: {api_inventory}, 初始库存: {initial_inventory}, 挂单量: 买单={pending_buy_volume}, 实际库存: {actual_inventory}")

            return actual_inventory
        except Exception as e:
            logging.error(f"计算实际库存时发生错误: {str(e)}\n{traceback.format_exc()}")
            # 如果计算失败，返回原始 API 库存或当前库存
            return api_inventory if api_inventory is not None else self.inventory

    def update_inventory(self, new_inventory=None, coin='usdt'):
        """更新库存并记录历史

        如果不提供new_inventory参数，则从交易所API获取最新库存并计算实际库存
        如果提供new_inventory参数，则使用该值更新库存（仅用于测试或特殊情况）
        """
        try:
            # 如果没有提供新的库存值，则从 API 获取并计算实际库存
            if new_inventory is None:
                api_inventory = get_inventory(coin)
                logging.info(f"从交易所API获取原始库存: {api_inventory}")
                # 计算实际库存（减去初始库存和挂单量）
                new_inventory = self.calculate_actual_inventory(api_inventory, coin)
                logging.info(f"计算实际库存（减去初始库存和挂单量）: {new_inventory}")
            else:
                logging.warning(f"使用提供的库存值更新: {new_inventory} (而非从API获取)")

            # 更新当前库存
            old_inventory = self.inventory
            self.inventory = float(new_inventory)

            # 记录库存变化
            if old_inventory != self.inventory:
                logging.info(f"库存已更新: {old_inventory} -> {self.inventory}, 变化量: {self.inventory - old_inventory}")

            # 添加到历史记录，包含时间戳
            current_time = time.time()
            self.inventory_history.append((current_time, self.inventory))

            # 每次更新后保存历史记录
            self.save_inventory_history()

            return self.inventory
        except Exception as e:
            logging.error(f"更新库存时发生错误: {str(e)}\n{traceback.format_exc()}")
            return self.inventory

    def update_execution_history(self, symbol="usdtusd"):
        """更新成交历史

        从交易所API获取最新成交记录并更新execution_history
        """
        try:
            # 获取最新的成交记录
            new_records = get_market_trades(symbol)

            if not new_records:
                logging.info("未获取到新的成交记录")
                return

            # 如果execution_history为空，直接使用新记录
            if not self.execution_history:
                self.execution_history = new_records
                logging.info(f"初始化成交历史，共{len(new_records)}条记录")
                return

            # 获取最后一条记录的时间戳
            last_timestamp = self.execution_history[-1][0]

            # 筛选出比最后一条记录更新的记录
            new_records = [record for record in new_records if record[0] > last_timestamp]

            if new_records:
                # 将新记录添加到历史记录中
                self.execution_history.extend(new_records)

                # 如果历史记录过长，可以裁剪
                max_history_length = 1000  # 设置一个合理的最大长度
                if len(self.execution_history) > max_history_length:
                    self.execution_history = self.execution_history[-max_history_length:]

                logging.info(f"更新了{len(new_records)}条新成交记录，当前共有{len(self.execution_history)}条记录")
            else:
                logging.info("未获取到新的成交记录")

        except Exception as e:
            logging.error(f"更新成交历史时发生错误: {str(e)}\n{traceback.format_exc()}")


    async def check_order_status(self, order_id, symbol: str="usdtusd"):
        """检查订单状态"""
        try:
            order_info = await spot_client.async_get_order(symbol, order_id)
            if order_info:
                if order_info.get('status') in [4, 5, 6]: # 订单状态：0 初始，1 新订单，2 完全成交，3 部份成交，4 取消，5 待取消，6 过期
                    active_orders.discard(order_id)

                    # 从按价格分组的订单ID字典中移除该订单
                    for side in ["buy", "sell"]:
                        for price, order_ids in list(current_order_ids_grouped[side].items()):
                            if order_id in order_ids:
                                order_ids.remove(order_id)
                                if not order_ids:  # 如果该价格没有订单了，删除该价格键
                                    del current_order_ids_grouped[side][price]
            return order_info
        except Exception as e:
            logging.error(f"查询订单状态时发生错误: {str(e)}\n{traceback.format_exc()}")
            return None


    async def place_new_order(self, symbol, price, volume, side, sem):
        """挂新单，优化版本以提高效率"""
        async with sem:  # 控制并发
            try:
                # 将side转换为小写以便于存储
                side_lower = side.lower()

                # 生成唯一的客户端订单ID，添加随机数确保唯一性
                client_order_id = f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'

                # 预先格式化参数，减少运行时转换
                volume_str = str(round(float(volume), self.size_precision))
                price_str = str(round(float(price), self.price_precision))

                # 直接使用格式化好的参数，减少中间处理
                params = {
                    "symbol": symbol,
                    "side": side,
                    "type": 1,
                    "volume": volume_str,
                    "price": price_str,
                    "clientOrderId": client_order_id
                }

                # 调用API下单
                order_result = await spot_client.async_new_order(**params)

                # 处理返回结果
                if order_result and "order_id" in order_result:
                    order_id = order_result["order_id"]

                    # 添加到活跃订单集合
                    active_orders.add(order_id)

                    # 添加到按价格分组的订单ID字典，使用更高效的方式
                    if price not in current_order_ids_grouped[side_lower]:
                        current_order_ids_grouped[side_lower][price] = [order_id]
                    else:
                        current_order_ids_grouped[side_lower][price].append(order_id)

                    return order_id

                # 如果下单失败，返回None
                return None
            except Exception as e:
                # 简化错误日志，减少堆栈输出
                logging.error(f"{side} 侧价格 {price} 下单失败: {str(e)}")
                return None


    async def cancel_and_place_order(self, symbol, price, volume, side, sem, price_list):
        """撤单后立即挂新单，优化版本以提高效率"""
        async with sem:
            # 将side转换为小写以便于存储
            side_lower = side.lower()

            # 先检查当前价格是否已有订单
            orders_at_level = current_order_ids_grouped.get(side_lower, {}).get(price, [])
            closest_price = None
            order_id_to_cancel = None

            # 如果当前价格没有订单，尝试找最近的需要撤销的旧价格
            if not orders_at_level:
                # 优化查找方式，使用集合操作提高效率
                current_prices = set(current_order_ids_grouped.get(side_lower, {}).keys())
                price_list_set = set(price_list)
                old_price_levels = list(current_prices - price_list_set)

                if old_price_levels:
                    # 使用更高效的方式找到最近的价格
                    closest_price = min(old_price_levels, key=lambda x: abs(float(x) - float(price)))
                    orders_at_level = current_order_ids_grouped[side_lower].get(closest_price, [])

                    # 如果有订单，取出第一个进行撤销
                    if orders_at_level:
                        order_id_to_cancel = orders_at_level[0]

            # 如果当前价格有订单，直接取第一个
            elif orders_at_level:
                order_id_to_cancel = orders_at_level[0]

            # 如果没有订单可撤，直接挂新单
            if not order_id_to_cancel:
                return await self.place_new_order(symbol, price, volume, side, sem)

            try:
                # 使用更高效的并发执行方式
                # 创建撤单任务
                cancel_task = spot_client.async_cancel_order(symbol=symbol, order_id=order_id_to_cancel)

                # 创建挂单任务，使用客户端订单ID确保唯一性
                client_order_id = f'bybMm_{time.time() * 1000}_{random.randint(1000, 9999)}'
                place_params = {
                    "symbol": symbol,
                    "side": side,
                    "type": 1,
                    "volume": str(volume),
                    "price": str(price),
                    "clientOrderId": client_order_id
                }
                place_task = spot_client.async_new_order(**place_params)

                # 并发执行撤单和挂单任务
                cancel_result, order_result = await asyncio.gather(cancel_task, place_task, return_exceptions=True)

                # 处理撤单结果
                if not isinstance(cancel_result, Exception):
                    # 从活跃订单集合中移除撤销的订单
                    active_orders.discard(order_id_to_cancel)

                    # 从价格分组字典中移除该订单
                    if closest_price:
                        # 如果是最近的价格
                        if order_id_to_cancel in current_order_ids_grouped[side_lower].get(closest_price, []):
                            current_order_ids_grouped[side_lower][closest_price].remove(order_id_to_cancel)
                            if not current_order_ids_grouped[side_lower][closest_price]:
                                del current_order_ids_grouped[side_lower][closest_price]
                    else:
                        # 如果是当前价格
                        if order_id_to_cancel in current_order_ids_grouped[side_lower].get(price, []):
                            current_order_ids_grouped[side_lower][price].remove(order_id_to_cancel)
                            if not current_order_ids_grouped[side_lower][price]:
                                del current_order_ids_grouped[side_lower][price]

                # 处理挂单结果
                if not isinstance(order_result, Exception) and order_result:
                    new_order_id = order_result.get("order_id")
                    if new_order_id:
                        # 添加到活跃订单集合
                        active_orders.add(new_order_id)

                        # 添加到按价格分组的订单ID字典
                        if price in current_order_ids_grouped[side_lower]:
                            current_order_ids_grouped[side_lower][price].append(new_order_id)
                        else:
                            current_order_ids_grouped[side_lower][price] = [new_order_id]

                        return new_order_id

                # 如果挂单失败但撤单成功，重试挂单
                if not isinstance(cancel_result, Exception) and (isinstance(order_result, Exception) or not order_result):
                    return await self.place_new_order(symbol, price, volume, side, sem)

                return None
            except Exception as e:
                logging.error(f"撤单或挂单操作失败: {str(e)}\n{traceback.format_exc()}")
                # 即使撤单失败也尝试挂新单
                try:
                    return await self.place_new_order(symbol, price, volume, side, sem)
                except Exception as e2:
                    logging.error(f"{side} 侧价格 {price} 下单失败: {str(e2)}")
                    return None


async def initialize_orders(mm, bids, asks, sem):
    """首次执行时，批量铺单"""
    global first_run
    symbol = "usdtusd"

    logging.info("首次执行，批量挂单...")

    # 先分别拿到 `bid` 和 `ask` 订单
    bid_tasks = [mm.place_new_order(symbol, bid_price, bid_size, "buy", sem) for bid_price, bid_size in bids]
    ask_tasks = [mm.place_new_order(symbol, ask_price, ask_size, "sell", sem) for ask_price, ask_size in asks]

    # 同时并发挂单
    await asyncio.gather(*bid_tasks, *ask_tasks)  # 执行所有挂 `bid` 和 `ask` 单的任务

    first_run = False  # 标记首次挂单完成


async def refresh_orders_random(mm, bids, asks, sem, max_time_seconds:float=2.0):
    """随机撤销部分订单后重新挂单，确保在指定时间内完成
    前十档变化更快，剩余档位变化较慢

    参数:
        mm: 做市商实例
        bids: 买单列表
        asks: 卖单列表
        sem: 信号量，控制并发
        max_time_seconds: 最大执行时间（秒）
    """
    global first_run
    symbol = "usdtusd"

    # 记录开始时间
    start_time = time.time()

    if first_run:
        await initialize_orders(mm, bids, asks, sem)  # 仅首次执行
        return

    logging.info("开始随机撤单并挂新单...")

    # 将买单和卖单分为前十档和剩余档位
    front_orders = []  # 前十档订单
    back_orders = []   # 剩余档位订单

    # 处理买单
    for i, (bid_price, bid_size) in enumerate(bids):
        if bid_size >= mm.min_order_size:  # 基础校验
            order = {
                "price": bid_price,
                "volume": bid_size,
                "side": "buy",
                "index": i  # 记录原始索引
            }

            # 分类到前十档或剩余档位
            if i < 10:
                order["priority"] = 1  # 前十档买单最高优先级
                front_orders.append(order)
            else:
                order["priority"] = 3  # 剩余档位买单优先级较低
                back_orders.append(order)

    # 处理卖单
    for i, (ask_price, ask_size) in enumerate(asks):
        if ask_size >= mm.min_order_size:  # 基础校验
            order = {
                "price": ask_price,
                "volume": ask_size,
                "side": "sell",
                "index": i  # 记录原始索引
            }

            # 分类到前十档或剩余档位
            if i < 10:
                order["priority"] = 2  # 前十档卖单次高优先级
                front_orders.append(order)
            else:
                order["priority"] = 4  # 剩余档位卖单优先级最低
                back_orders.append(order)

    # 随机打乱前十档和剩余档位的订单，但保持优先级
    random.shuffle(front_orders)
    random.shuffle(back_orders)

    # 合并所有订单，前十档在前，剩余档位在后
    all_orders = front_orders + back_orders

    # 获取所有价格列表，用于后续查找最近价格
    price_list = [order["price"] for order in all_orders]

    # 计算总订单数
    total_orders = len(all_orders)
    if total_orders == 0:
        logging.info("没有需要处理的订单")
        return

    # 计算前十档和剩余档位的订单数
    front_count = len(front_orders)
    back_count = len(back_orders)
    logging.info(f"前十档订单数: {front_count}, 剩余档位订单数: {back_count}")

    # 分配时间比例，前十档占用总时间的70%，剩余档位占用总时间的30%
    front_time_ratio = 0.7
    back_time_ratio = 0.3

    # 计算前十档和剩余档位的可用时间
    available_time = max_time_seconds * 0.85  # 预留15%的时间作为缓冲
    front_time = available_time * front_time_ratio
    back_time = available_time * back_time_ratio

    # 优化批处理大小
    front_batch_size = max(1, min(sem._value, int(front_count / 5)))  # 前十档使用更小的批处理大小
    back_batch_size = max(1, min(sem._value, int(back_count / 3)))   # 剩余档位使用更大的批处理大小

    # 创建任务列表
    all_tasks = []
    processed_count = 0

    # 先处理前十档订单（更高频率更新）
    front_start_time = time.time()
    front_processed = 0

    for i in range(0, front_count, front_batch_size):
        # 检查前十档处理是否超时
        elapsed = time.time() - front_start_time
        if elapsed > front_time:
            logging.warning(f"前十档处理超时: 已处理 {front_processed}/{front_count} 个订单")
            break

        # 获取当前批次
        batch = front_orders[i:i+front_batch_size]
        batch_tasks = []

        # 创建当前批次的任务
        for order in batch:
            task = mm.cancel_and_place_order(symbol, order["price"], order["volume"], order["side"], sem, price_list)
            batch_tasks.append(task)

        # 并发执行当前批次的任务
        results = await asyncio.gather(*batch_tasks, return_exceptions=True)

        # 处理结果
        success_count = sum(1 for r in results if r is not None and not isinstance(r, Exception))
        front_processed += len(batch)
        processed_count += len(batch)

        # 将成功的任务添加到总任务列表
        all_tasks.extend([r for r in results if r is not None and not isinstance(r, Exception)])

        # 记录当前批次处理情况
        logging.info(f"前十档批次处理完成: {success_count}/{len(batch)} 个订单成功，进度: {front_processed}/{front_count}")

    # 然后处理剩余档位订单（更低频率更新）
    back_start_time = time.time()
    back_processed = 0

    # 计算剩余时间
    remaining_time = max(0, available_time - (time.time() - start_time))
    back_time = min(back_time, remaining_time)

    for i in range(0, back_count, back_batch_size):
        # 检查剩余档位处理是否超时
        elapsed = time.time() - back_start_time
        if elapsed > back_time:
            logging.warning(f"剩余档位处理超时: 已处理 {back_processed}/{back_count} 个订单")
            break

        # 检查总时间是否超时
        total_elapsed = time.time() - start_time
        if total_elapsed > max_time_seconds * 0.85:
            logging.warning(f"总时间超时: 已处理 {processed_count}/{total_orders} 个订单")
            break

        # 获取当前批次
        batch = back_orders[i:i+back_batch_size]
        batch_tasks = []

        # 创建当前批次的任务
        for order in batch:
            task = mm.cancel_and_place_order(symbol, order["price"], order["volume"], order["side"], sem, price_list)
            batch_tasks.append(task)

        # 并发执行当前批次的任务
        results = await asyncio.gather(*batch_tasks, return_exceptions=True)

        # 处理结果
        success_count = sum(1 for r in results if r is not None and not isinstance(r, Exception))
        back_processed += len(batch)
        processed_count += len(batch)

        # 将成功的任务添加到总任务列表
        all_tasks.extend([r for r in results if r is not None and not isinstance(r, Exception)])

        # 记录当前批次处理情况
        logging.info(f"剩余档位批次处理完成: {success_count}/{len(batch)} 个订单成功，进度: {back_processed}/{back_count}")

    # 计算总执行时间
    total_time = time.time() - start_time
    logging.info(f"订单更新完成。总耗时: {total_time:.3f}秒，前十档: {front_processed}/{front_count}，剩余档位: {back_processed}/{back_count}，成功: {len(all_tasks)} 个")


async def main():
    # 使用全局变量 first_run 来控制是否是首次运行
    global first_run

    # 初始化做市商
    mm = BybMarketMaker(
        base_order_size=0.001,
        num_levels=50,
        inventory_limit=20000.0,
        max_inventory=100000.0,
        price_precision=4,
        size_precision=3,
        risk_aversion=0.5,
        total_side_qty=500000
    )

    # 使用get_trade_price获取最新成交价作为初始价格
    try:
        # 获取所有交易对的最新成交价
        trade_prices_df = spot_market.get_trade_price()
        # 过滤出'usdtusd'交易对的数据
        usdtusd_data = trade_prices_df[trade_prices_df['symbol'] == 'usdtusd']

        if not usdtusd_data.empty:
            # 获取最新成交价
            initial_price = float(usdtusd_data['price'].iloc[0])
            logging.info(f"从 get_trade_price 获取到的初始价格: {initial_price}")
        else:
            # 如果没有找到usdtusd的数据，使用默认值
            initial_price = 0.1  # 使用更合理的默认值
            logging.warning(f"未找到usdtusd的成交价数据，使用默认值: {initial_price}")
    except Exception as e:
        # 如果获取失败，使用默认值
        initial_price = 0.1  # 使用更合理的默认值
        logging.error(f"获取成交价失败: {str(e)}\n{traceback.format_exc()}")
        logging.warning(f"使用默认初始价格: {initial_price}")

    # 更新中间价
    await mm.update_mid_price(initial_price)

    # 确保首次运行标志被设置
    first_run = True

    # 初始化库存和成交历史更新时间
    last_inventory_update = time.time()
    last_execution_update = time.time()
    inventory_update_interval = 60  # 每60秒更新一次库存
    execution_update_interval = 30  # 每30秒更新一次成交历史

    # 限制最大并发请求数
    SEM = asyncio.Semaphore(5)  # 每次最多并发 5 个请求

    # 首次运行时取消所有订单
    await mm.cancel_all_orders()

    # 创建订单状态检查任务
    last_order_check = time.time()
    order_check_interval = 5  # 每5秒批量检查一次订单状态

    # 记录初始价格，以便后续使用
    logging.info(f"使用从 get_trade_price 获取的初始价格: {initial_price}")

    while True:
        try:
            # 记录循环开始时间
            current_time = time.time()

            # 批量检查活跃订单状态（每5秒检查一次，而不是每次循环都检查）
            if current_time - last_order_check >= order_check_interval and active_orders:
                # 创建批量检查任务
                check_tasks = []
                # 每次最多检查10个订单，避免API调用过多
                order_batch = list(active_orders)[:10]
                for order_id in order_batch:
                    check_tasks.append(mm.check_order_status(order_id))

                # 并发执行检查任务
                await asyncio.gather(*check_tasks, return_exceptions=True)
                last_order_check = current_time
                logging.info(f"批量检查了 {len(order_batch)} 个活跃订单状态")

            # 定期更新库存和成交历史
            # 更新库存（降低频率，减少API调用）
            if current_time - last_inventory_update >= inventory_update_interval:
                # 从交易所API获取最新库存，不提供参数以确保使用API数据
                mm.update_inventory()
                mm.last_inventory_check = current_time  # 更新检查时间
                last_inventory_update = current_time

            # 更新成交历史
            if current_time - last_execution_update >= execution_update_interval:
                mm.update_execution_history()
                last_execution_update = current_time

            # 获取当前市场价格
            if first_run:
                # 如果是首次运行，使用从 get_trade_price 获取的初始价格
                current_price = initial_price
                logging.info(f"首次运行使用从 get_trade_price 获取的初始价格: {initial_price}")
                # first_run 变量将在 refresh_orders_random 函数中被设置为 False
            else:
                # 从交易所获取当前市场价格
                market_data = spot_market.get_ticker(symbol="usdtusd")
                current_price = float(market_data['last'])
                logging.debug(f"从 get_ticker 获取当前市场价格: {current_price}")

            # 更新中间价
            await mm.update_mid_price(current_price)

            # 生成报价（这是一个计算密集型操作，但不涉及API调用）
            bids, asks = await mm.generate_layered_quotes()

            # 使用优化后的随机撤单和挂单策略，前十档变化更快，剩余档位变化较慢
            try:
                # 增加执行时间到 1.9 秒，给前十档更多时间处理
                await refresh_orders_random(mm, bids, asks, SEM, max_time_seconds=1.9)
            except Exception as e:
                logging.error(f"刷新订单失败: {str(e)}\n{traceback.format_exc()}")

            # 显示当前状态（仅在日志级别为DEBUG时显示详细信息，减少日志开销）
            if logging.getLogger().getEffectiveLevel() <= logging.DEBUG:
                logging.debug("=== 生成的报价 ===")
                await mm.show_quotes(top_n=10, bids=bids, asks=asks)
                logging.debug("=== 实际订单簿 ===")
                await mm.show_quotes(top_n=10)
            else:
                # 在INFO级别只显示简要信息
                if bids and asks:
                    logging.info(f"报价生成完成: 买单 {len(bids)} 档，卖单 {len(asks)} 档")

            # 计算当前循环执行时间
            loop_end_time = time.time()
            loop_duration = loop_end_time - current_time

            # 确保每个循环最多只等待到总时间为2秒
            remaining_time = max(0, 2.0 - loop_duration)
            if remaining_time > 0:
                logging.info(f"循环执行时间: {loop_duration:.3f}秒，等待 {remaining_time:.3f} 秒后刷新...")
                await asyncio.sleep(remaining_time)
            else:
                logging.warning(f"循环执行时间过长: {loop_duration:.3f}秒，超过2秒目标")
        except Exception as e:
            logging.error(f"主循环发生错误: {str(e)}\n{traceback.format_exc()}")
            await asyncio.sleep(5)  # 发生错误时等待更长时间


if __name__ == "__main__":
    asyncio.run(main())