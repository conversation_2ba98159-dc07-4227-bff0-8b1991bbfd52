#!/usr/bin/env python3
"""
测试修复后的wash_trading.py功能
"""

import sys
sys.path.append("/home/<USER>/usd_mm/")
from wash_trading import calculate_wash_qty_from_orderbook

def test_qty_calculation():
    """测试数量计算功能"""
    print("=== 测试修复后的数量计算功能 ===")
    
    try:
        qty = calculate_wash_qty_from_orderbook("manausdt")
        print(f"计算的刷量数量: {qty} (类型: {type(qty)})")
        
        # 验证返回值是整数
        if isinstance(qty, int):
            print("✅ 返回值类型正确 (整数)")
        else:
            print("❌ 返回值类型错误，应该是整数")
            
        # 验证数量范围
        if 50 <= qty <= 1000:
            print("✅ 数量范围合理")
        else:
            print(f"❌ 数量范围异常: {qty}")
            
    except Exception as e:
        print(f"❌ 数量计算功能异常: {e}")

if __name__ == "__main__":
    test_qty_calculation()
