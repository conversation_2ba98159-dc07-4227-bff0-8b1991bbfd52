#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试cancel_and_replace函数的功能
该函数用于批量撤销订单并创建新订单
"""

import sys
import time
import json
import logging
from byex.spot.trade import SpotTrade

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 请在此处填写您的API密钥
API_KEY = "875c05cebc511ac21919cff82620d887"
SECRET_KEY = "537437b5bc7748a1a6b02a0428ada312"

# 测试的交易对
SYMBOL = "usdtusd"  # 根据您的需求修改

def setup_client():
    """初始化API客户端"""
    try:
        client = SpotTrade(api_key=API_KEY, secret_key=SECRET_KEY)
        base_url = "https://openapi.100exdemo.com"
        client.BASE_URL = base_url
        logging.info("API客户端初始化成功")
        return client
    except Exception as e:
        logging.error(f"API客户端初始化失败: {e}")
        sys.exit(1)

def place_test_orders(client, count=3):
    """创建测试订单
    
    Args:
        client: API客户端
        count: 要创建的订单数量
        
    Returns:
        list: 创建的订单ID列表
    """
    order_ids = []
    
    # 获取当前市场价格作为参考
    try:
        # 这里假设有一个获取市场价格的方法，如果没有，可以手动设置一个合理的价格
        # market_price = client.get_market_price(SYMBOL)
        market_price = 1.0  # 假设市场价格为1.0
        logging.info(f"当前市场价格: {market_price}")
    except Exception as e:
        logging.error(f"获取市场价格失败: {e}")
        market_price = 1.0  # 使用默认值
    
    # 创建买单和卖单
    try:
        # 创建买单 (低于市场价)
        for i in range(count):
            price = round(market_price * (0.95 - i * 0.01), 4)  # 依次降低价格
            volume = 10 + i  # 依次增加数量
            
            order = client.new_order(
                symbol=SYMBOL,
                side="BUY",
                type=1,  # 限价单
                volume=str(volume),
                price=price
            )
            
            if order and "order_id" in order:
                order_id = order["order_id"]
                order_ids.append(order_id)
                logging.info(f"创建买单成功: ID={order_id}, 价格={price}, 数量={volume}")
            else:
                logging.error(f"创建买单失败: {order}")
        
        # 创建卖单 (高于市场价)
        for i in range(count):
            price = round(market_price * (1.05 + i * 0.01), 4)  # 依次提高价格
            volume = 10 + i  # 依次增加数量
            
            order = client.new_order(
                symbol=SYMBOL,
                side="SELL",
                type=1,  # 限价单
                volume=str(volume),
                price=price
            )
            
            if order and "order_id" in order:
                order_id = order["order_id"]
                order_ids.append(order_id)
                logging.info(f"创建卖单成功: ID={order_id}, 价格={price}, 数量={volume}")
            else:
                logging.error(f"创建卖单失败: {order}")
                
        logging.info(f"共创建 {len(order_ids)} 个测试订单")
        return order_ids
    
    except Exception as e:
        logging.error(f"创建测试订单时发生错误: {e}")
        return order_ids

def get_pending_orders(client, symbol):
    """获取当前挂单"""
    try:
        orders = client.get_open_orders(symbol=symbol)
        if orders and "resultList" in orders:
            return orders["resultList"]
        return []
    except Exception as e:
        logging.error(f"获取挂单失败: {e}")
        return []

def test_cancel_and_replace(client, order_ids):
    """测试cancel_and_replace函数
    
    Args:
        client: API客户端
        order_ids: 要撤销的订单ID列表
    """
    if not order_ids:
        logging.warning("没有订单可撤销，跳过测试")
        return
    
    # 准备新订单数据
    market_price = 1.0  # 假设市场价格为1.0
    
    # 准备要撤销的订单ID
    mass_cancel = order_ids
    
    # 准备要创建的新订单
    mass_place = []
    
    # 创建新的买单
    for i in range(2):
        price = round(market_price * (0.97 - i * 0.01), 4)
        volume = 15 + i
        
        mass_place.append({
            "price": str(price),
            "volume": str(volume),
            "side": "BUY",
            "type": 1,  # 限价单
            "volumeType": 1  # 数量类型，根据API要求设置
        })
    
    # 创建新的卖单
    for i in range(2):
        price = round(market_price * (1.03 + i * 0.01), 4)
        volume = 15 + i
        
        mass_place.append({
            "price": str(price),
            "volume": str(volume),
            "side": "SELL",
            "type": 1,  # 限价单
            "volumeType": 1  # 数量类型，根据API要求设置
        })
    
    logging.info(f"准备撤销 {len(mass_cancel)} 个订单，创建 {len(mass_place)} 个新订单")
    
    # 将列表转换为JSON字符串
    mass_place_str = json.dumps(mass_place)
    mass_cancel_str = json.dumps(mass_cancel)
    
    try:
        # 调用cancel_and_replace函数
        result = client.cancel_and_replace(
            symbol=SYMBOL,
            mass_place=mass_place_str,
            mass_cancel=mass_cancel_str
        )
        
        logging.info(f"cancel_and_replace调用结果: {result}")
        
        # 等待一段时间，让订单状态更新
        time.sleep(2)
        
        # 获取当前挂单，验证结果
        current_orders = get_pending_orders(client, SYMBOL)
        logging.info(f"当前挂单数量: {len(current_orders)}")
        
        # 检查原订单是否已撤销
        original_order_exists = False
        for order in current_orders:
            if order.get("order_id") in order_ids:
                original_order_exists = True
                logging.warning(f"原订单未被撤销: {order}")
        
        if not original_order_exists:
            logging.info("所有原订单已成功撤销")
        
        # 检查新订单是否已创建
        new_orders_count = len(current_orders)
        logging.info(f"当前挂单数量: {new_orders_count}")
        
        # 打印当前所有挂单详情
        for order in current_orders:
            logging.info(f"当前挂单: ID={order.get('order_id')}, 价格={order.get('price')}, 数量={order.get('volume')}, 方向={order.get('side')}")
        
    except Exception as e:
        logging.error(f"测试cancel_and_replace时发生错误: {e}")

def cleanup(client, symbol):
    """清理所有挂单"""
    try:
        result = client.cancel_all_orders_by_symbol(symbol)
        logging.info(f"清理所有挂单结果: {result}")
    except Exception as e:
        logging.error(f"清理挂单时发生错误: {e}")

def main():
    """主函数"""
    logging.info("开始测试cancel_and_replace函数")
    
    # 检查API密钥是否已设置
    if API_KEY == "your_api_key" or SECRET_KEY == "your_secret_key":
        logging.error("请先设置API_KEY和SECRET_KEY")
        sys.exit(1)
    
    # 初始化客户端
    client = setup_client()
    
    try:
        # 清理可能存在的挂单
        cleanup(client, SYMBOL)
        
        # 创建测试订单
        order_ids = place_test_orders(client)
        
        if order_ids:
            # 等待一段时间，确保订单已经进入订单簿
            time.sleep(2)
            
            # 获取当前挂单，确认测试订单已创建
            current_orders = get_pending_orders(client, SYMBOL)
            logging.info(f"创建测试订单后，当前挂单数量: {len(current_orders)}")
            
            # 测试cancel_and_replace函数
            test_cancel_and_replace(client, order_ids)
        else:
            logging.warning("没有成功创建测试订单，跳过测试")

        time.sleep(10)
        
        # 最后清理所有挂单
        cleanup(client, SYMBOL)
        
    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
        # 确保清理所有挂单
        cleanup(client, SYMBOL)
    
    logging.info("测试完成")

if __name__ == "__main__":
    main()
