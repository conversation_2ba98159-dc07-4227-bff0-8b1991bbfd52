from api import API


class UMFutures(API):
    def __init__(self, key=None, secret=None, **kwargs):
        if "base_url" not in kwargs:
            kwargs["base_url"] = "https://futuresopenapi.100ex.com"
        super().__init__(key, secret, **kwargs)

    # MARKETS
    # from um_futures.market import ping
    # from um_futures.market import time
    # from um_futures.market import exchange_info
    from um_futures.market import depth
    from um_futures.market import allDepth
    from um_futures.market import allIndexPrice
    from um_futures.market import allTicker
    # from um_futures.market import trades
    # from um_futures.market import historical_trades
    # from um_futures.market import agg_trades
    from um_futures.market import klines
    from um_futures.market import allIndexTagPrice
    # from um_futures.market import continuous_klines
    # from um_futures.market import index_price_klines
    # from um_futures.market import mark_price_klines
    # from um_futures.market import mark_price
    # from um_futures.market import funding_rate
    # from um_futures.market import funding_info
    # from um_futures.market import ticker_24hr_price_change
    # from um_futures.market import ticker_price
    # from um_futures.market import book_ticker
    # from um_futures.market import quarterly_contract_settlement_price
    # from um_futures.market import open_interest
    # from um_futures.market import open_interest_hist
    # from um_futures.market import top_long_short_position_ratio
    # from um_futures.market import long_short_account_ratio
    # from um_futures.market import top_long_short_account_ratio
    # from um_futures.market import taker_long_short_ratio
    # from um_futures.market import blvt_kline
    # from um_futures.market import index_info
    # from um_futures.market import asset_Index
    # from um_futures.market import index_price_constituents

    # # ACCOUNT(including orders and trades)
    # from um_futures.account import change_position_mode
    # from um_futures.account import get_position_mode
    # from um_futures.account import change_multi_asset_mode
    # from um_futures.account import get_multi_asset_mode
    # from um_futures.account import new_order
    # from um_futures.account import new_order_test
    # from um_futures.account import modify_order
    # from um_futures.account import new_batch_order
    # from um_futures.account import query_order
    # from um_futures.account import cancel_order
    # from um_futures.account import cancel_open_orders
    # from um_futures.account import cancel_batch_order
    # from um_futures.account import countdown_cancel_order
    # from um_futures.account import get_open_orders
    # from um_futures.account import get_orders
    # from um_futures.account import get_all_orders
    # from um_futures.account import balance
    # from um_futures.account import account
    # from um_futures.account import change_leverage
    # from um_futures.account import change_margin_type
    # from um_futures.account import modify_isolated_position_margin
    # from um_futures.account import get_position_margin_history
    # from um_futures.account import get_position_risk
    # from um_futures.account import get_account_trades
    # from um_futures.account import get_income_history
    # from um_futures.account import leverage_brackets
    # from um_futures.account import adl_quantile
    # from um_futures.account import force_orders
    # from um_futures.account import api_trading_status
    # from um_futures.account import commission_rate
    # from um_futures.account import futures_account_configuration
    # from um_futures.account import symbol_configuration
    # from um_futures.account import query_user_rate_limit
    # from um_futures.account import download_transactions_asyn
    # from um_futures.account import aysnc_download_info
    # from um_futures.account import download_order_asyn
    # from um_futures.account import async_download_order_id
    # from um_futures.account import download_trade_asyn
    # from um_futures.account import async_download_trade_id
    # from um_futures.account import toggle_bnb_burn
    # from um_futures.account import get_bnb_burn

    # # CONVERT
    # from um_futures.convert import list_all_convert_pairs
    # from um_futures.convert import send_quote_request
    # from um_futures.convert import accept_offered_quote
    # from um_futures.convert import order_status

    # # STREAMS
    # from um_futures.data_stream import new_listen_key
    # from um_futures.data_stream import renew_listen_key
    # from um_futures.data_stream import close_listen_key
