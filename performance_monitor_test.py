#!/usr/bin/env python3
"""
性能监控测试脚本
用于验证性能监控功能是否正常工作
"""

import time
import random
from collections import defaultdict

# 模拟性能统计数据结构
performance_stats = {
    'computation_times': [],
    'api_times': [],
    'total_times': [],
    'api_call_counts': [],
    'order_counts': [],
    'last_report_time': time.time(),
    'report_interval': 10  # 测试时使用10秒间隔
}

def simulate_computation_task(duration_ms):
    """模拟计算任务"""
    start = time.time()
    # 模拟计算工作
    time.sleep(duration_ms / 1000.0)
    return time.time() - start

def simulate_api_call(duration_ms):
    """模拟API调用"""
    start = time.time()
    # 模拟网络延迟
    time.sleep(duration_ms / 1000.0)
    return time.time() - start

def report_performance_stats():
    """生成性能统计报告"""
    global performance_stats
    
    current_time = time.time()
    if current_time - performance_stats['last_report_time'] < performance_stats['report_interval']:
        return
    
    if not performance_stats['computation_times']:
        return
    
    # 计算统计数据
    comp_times = performance_stats['computation_times']
    api_times = performance_stats['api_times']
    total_times = performance_stats['total_times']
    api_counts = performance_stats['api_call_counts']
    order_counts = performance_stats['order_counts']
    
    # 计算平均值、最大值、最小值
    comp_avg = sum(comp_times) / len(comp_times)
    comp_max = max(comp_times)
    comp_min = min(comp_times)
    
    api_avg = sum(api_times) / len(api_times)
    api_max = max(api_times)
    api_min = min(api_times)
    
    total_avg = sum(total_times) / len(total_times)
    total_max = max(total_times)
    total_min = min(total_times)
    
    avg_api_calls = sum(api_counts) / len(api_counts)
    avg_orders = sum(order_counts) / len(order_counts)
    
    # 计算比例
    comp_ratio = (comp_avg / total_avg) * 100 if total_avg > 0 else 0
    api_ratio = (api_avg / total_avg) * 100 if total_avg > 0 else 0
    
    # 生成报告
    print("=" * 80)
    print("性能统计报告 (测试模式)")
    print("=" * 80)
    print(f"样本数量: {len(comp_times)} 次")
    print(f"平均订单数量: {avg_orders:.1f} 个")
    print(f"平均API调用次数: {avg_api_calls:.1f} 次")
    print("-" * 40)
    print(f"计算耗时 - 平均: {comp_avg*1000:.1f}ms, 最大: {comp_max*1000:.1f}ms, 最小: {comp_min*1000:.1f}ms")
    print(f"API耗时  - 平均: {api_avg*1000:.1f}ms, 最大: {api_max*1000:.1f}ms, 最小: {api_min*1000:.1f}ms")
    print(f"总耗时   - 平均: {total_avg*1000:.1f}ms, 最大: {total_max*1000:.1f}ms, 最小: {total_min*1000:.1f}ms")
    print("-" * 40)
    print(f"耗时占比 - 计算: {comp_ratio:.1f}%, API: {api_ratio:.1f}%")
    
    if comp_ratio > api_ratio:
        print("⚠️  计算耗时占主导，建议优化算法逻辑")
    else:
        print("⚠️  API耗时占主导，建议优化网络调用")
    
    print("=" * 80)
    
    # 清空统计数据，准备下一轮统计
    performance_stats['computation_times'].clear()
    performance_stats['api_times'].clear()
    performance_stats['total_times'].clear()
    performance_stats['api_call_counts'].clear()
    performance_stats['order_counts'].clear()
    performance_stats['last_report_time'] = current_time

def simulate_order_processing():
    """模拟订单处理过程"""
    total_start = time.time()
    
    # 模拟不同的处理场景
    scenarios = [
        {"name": "计算密集型", "comp_time": 80, "api_time": 20, "api_calls": 2, "orders": 50},
        {"name": "API密集型", "comp_time": 15, "api_time": 200, "api_calls": 4, "orders": 30},
        {"name": "平衡型", "comp_time": 40, "api_time": 60, "api_calls": 3, "orders": 40},
        {"name": "高负载", "comp_time": 120, "api_time": 300, "api_calls": 5, "orders": 80},
    ]
    
    scenario = random.choice(scenarios)
    
    # 添加随机变化
    comp_time = scenario["comp_time"] + random.randint(-20, 20)
    api_time = scenario["api_time"] + random.randint(-50, 50)
    api_calls = scenario["api_calls"] + random.randint(-1, 1)
    orders = scenario["orders"] + random.randint(-10, 10)
    
    # 确保值为正
    comp_time = max(5, comp_time)
    api_time = max(10, api_time)
    api_calls = max(1, api_calls)
    orders = max(10, orders)
    
    # 模拟计算阶段
    computation_time = simulate_computation_task(comp_time)
    
    # 模拟API调用阶段
    api_total_time = 0
    for i in range(api_calls):
        api_call_time = simulate_api_call(api_time / api_calls)
        api_total_time += api_call_time
    
    # 计算总时间
    total_time = time.time() - total_start
    
    # 记录统计数据
    performance_stats['computation_times'].append(computation_time)
    performance_stats['api_times'].append(api_total_time)
    performance_stats['total_times'].append(total_time)
    performance_stats['api_call_counts'].append(api_calls)
    performance_stats['order_counts'].append(orders)
    
    # 输出实时性能信息
    comp_ratio = (computation_time / total_time) * 100 if total_time > 0 else 0
    api_ratio = (api_total_time / total_time) * 100 if total_time > 0 else 0
    
    print(f"[{scenario['name']}] 性能详情 - 总耗时: {total_time*1000:.1f}ms, "
          f"计算: {computation_time*1000:.1f}ms({comp_ratio:.1f}%), "
          f"API: {api_total_time*1000:.1f}ms({api_ratio:.1f}%), "
          f"API调用: {api_calls}次, 订单: {orders}个")
    
    # 检查是否需要生成报告
    report_performance_stats()

def main():
    """主测试函数"""
    print("开始性能监控测试...")
    print("模拟不同的订单处理场景，观察性能统计报告")
    print("测试将运行30秒，每10秒生成一次报告")
    print("-" * 60)
    
    start_time = time.time()
    test_duration = 30  # 测试30秒
    
    while time.time() - start_time < test_duration:
        simulate_order_processing()
        # 模拟订单处理间隔
        time.sleep(random.uniform(0.5, 2.0))
    
    # 最终报告
    if performance_stats['computation_times']:
        performance_stats['last_report_time'] = 0  # 强制生成最终报告
        report_performance_stats()
    
    print("\n测试完成！")
    print("在实际使用中，这些统计信息将帮助您：")
    print("1. 识别性能瓶颈（计算 vs API）")
    print("2. 优化系统配置")
    print("3. 监控系统健康状态")

if __name__ == "__main__":
    main()
