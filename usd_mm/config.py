import pandas as pd
import pymysql

# 数据库连接信息
host = "127.0.0.1"  # 服务器地址
port = 3306  # MySQL 端口
user = "hao"  # 数据库用户名
password = "12345678"  # 数据库密码
database = "market_making_db"  # 要连接的数据库


def get_mm_params():
    # 连接数据库
    conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conn.cursor() as cursor:
            # 执行 SQL 查询
            cursor.execute("SELECT * FROM market_making;")
            # 获取查询结果
            result = cursor.fetchall()
            dataframe = pd.DataFrame(result, columns=["id", "pair1", "pair2", "bid_price", "ask_price", "base_spread",
                                                      "total_bid_volume", "total_ask_volume", "daily_limit", "last_update"])
            dataframe[["bid_price", "ask_price", "base_spread", "total_bid_volume", "total_ask_volume", "daily_limit"]] = \
                dataframe[["bid_price", "ask_price", "base_spread", "total_bid_volume", "total_ask_volume", "daily_limit"]].astype(float)
            return dataframe
    finally:
        conn.close()  # 关闭连接


def get_trades():
    # 连接数据库
    conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conn.cursor() as cursor:
            # 执行 SQL 查询
            cursor.execute("select id, side, volume, price, ctime from trades_7473239 where ask_user_id = bid_user_id;")
            # 获取查询结果
            result = cursor.fetchall()
            dataframe = pd.DataFrame(result, columns=["id", "side", "volume", "price", "ctime"])
            dataframe[["volume", "price"]] = dataframe[["volume", "price"]].astype(float)
            dataframe['ctime'] = pd.to_datetime(dataframe['ctime'], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.strftime('%Y-%m-%d %H:%M:%S')
            dataframe.loc[:, 'date'] = dataframe['ctime'].apply(lambda x: pd.to_datetime(x).date())
            return dataframe
    finally:
        conn.close()  # 关闭连接

if __name__ == '__main__':
    mm_params = get_mm_params()
    print(mm_params)
