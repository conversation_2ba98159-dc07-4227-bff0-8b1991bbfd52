````python
import time
import hmac
import hashlib
import requests
import pandas as pd
import sys
import os
# 加入 byex 包的父目录，而非 byex 目录本身
sys.path.append('/Users/<USER>/Desktop/tsen-100ex服务器/API')
import byex
from byex.um_futures import UMFutures as Client
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
um_market = Client()

api_key = "3a22f315f731241ee4945f63b2f9fd27"
api_secret = "76441d66063e6c28b782490518bd3595"
spot_market = Spot()
spot_client = SpotTrade(api_key, api_secret)
import time
import random

# 假设基准参数 运营可调配
p_buy = 1.0000
p_sell = 1.0000
s1 = 0.0007  #

# 设置买卖挂单总量（单位：张/单子数量）
total_bid_volume = 90
total_ask_volume = 90

# 假设从运营接口获取到的精度参数
price_precision = 4    # 价格保留两位小数
amount_precision = 4   # 数量保留五位小数

# 全局字典用于记录当前挂单的订单ID
current_order_ids = {
    "BUY": [],   # 买单订单ID列表
    "SELL": []   # 卖单订单ID列表
}

price_level_config = [
    (1, 1),
    (2, 3), (3, 3),
    (4, 5), (5, 5),
    (6, 7), (7, 7),
    (8, 9), (9, 9),
    (10, 11), (11, 11),
    (12, 13), (13, 13),
    (14, 15), (15, 15),
    (16, 17), (17, 17),
    (18, 19), (19, 19),
    (20, 21), (21, 21),
    (22, 23), (23, 23),
    (24, 25), (25, 25),
    (26, 27), (27, 27),
    (28, 29), (29, 29)
]


# 假设挂单量分配比例，7层均分，权重都设为1
bid_volume_ratios = [1] * len(price_level_config)  # 买单各层权重
ask_volume_ratios = [1] * len(price_level_config)  # 卖单各层权重

# 计算各层目标挂单数量
total_bid_weight = sum(bid_volume_ratios)
total_ask_weight = sum(ask_volume_ratios)
bid_target_volumes = [total_bid_volume * (w / total_bid_weight) for w in bid_volume_ratios]
ask_target_volumes = [total_ask_volume * (w / total_ask_weight) for w in ask_volume_ratios]

print("bid_target_volumes:", bid_target_volumes)

def random_volume(volume):
    return volume * (1 + random.uniform(-0.4, 0.4))

def generate_bid_orders(p, s1, bid_target_volumes):
    bid_orders = []
    # 第一层买单价格为 p
    bid_price = p - s1
    target_volume = bid_target_volumes[0]
    bid_orders.append({
        "level": 1,
        "price": round(bid_price, price_precision),
        "target_volume": round(random_volume(target_volume), amount_precision)
    })
    # 后续层
    for idx, (level, multiplier) in enumerate(price_level_config[1:], start=1):
        bid_price = p - s1 * multiplier
        target_volume = bid_target_volumes[idx]
        bid_orders.append({
            "level": level,
            "price": round(bid_price, price_precision),
            "target_volume": round(random_volume(target_volume), amount_precision)
        })
    return bid_orders

def generate_ask_orders(p, s1, ask_target_volumes):
    ask_orders = []
    # 第一层卖单价格为 p+spread
    ask_price = p + s1
    target_volume = ask_target_volumes[0]
    ask_orders.append({
        "level": 1,
        "price": round(ask_price, price_precision),
        "target_volume": round(random_volume(target_volume), amount_precision)
    })
    # 后续层：卖单价格向上递增
    for idx, (level, multiplier) in enumerate(price_level_config[1:], start=1):
        ask_price = p + s1 * multiplier
        target_volume = ask_target_volumes[idx]
        ask_orders.append({
            "level": level,
            "price": round(ask_price, price_precision),
            "target_volume": round(random_volume(target_volume), amount_precision)
        })
    return ask_orders

# 计算铺单覆盖率
def calculate_order_coverage(p, ask_orders, bid_orders):
    try:
        asks_df, bids_df = spot_market.get_orderbook("usdtusd", "step0")  # 返回两个 DataFrame
        print("Orderbook DataFrame Received:")
        print(asks_df.head())  # 打印前几行检查结构
        print(bids_df.head())

        # 提取价格和数量
        asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
        bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))

    except Exception as e:
        print(f"获取盘口数据失败: {e}")
        return
    
    if not isinstance(asks, list) or not isinstance(bids, list):
        print("错误: 盘口数据格式异常")
        return

    coverage_results = {}
    coverage_levels = [0.001, 0.002, 0.005, 0.01, 0.02] 
    
    for slippage in coverage_levels:
        ask_limit = p * (1 + slippage)
        bid_limit = p * (1 - slippage)
        
        # 统计市场上的量
        market_ask_volume = sum(qty for price, qty in asks if price <= ask_limit)
        market_bid_volume = sum(qty for price, qty in bids if price >= bid_limit)
        
        # 统计自身订单量
        self_ask_volume = sum(order["target_volume"] for order in ask_orders if order["price"] <= ask_limit)
        self_bid_volume = sum(order["target_volume"] for order in bid_orders if order["price"] >= bid_limit)
        
        # 计算覆盖率
        ask_coverage = (self_ask_volume / market_ask_volume) if market_ask_volume > 0 else 0
        bid_coverage = (self_bid_volume / market_bid_volume) if market_bid_volume > 0 else 0
        
        coverage_results[slippage] = {
            "ask_coverage": round(ask_coverage * 100, 2),
            "bid_coverage": round(bid_coverage * 100, 2),
            "self_ask_volume": round(self_ask_volume, 2),
            "self_bid_volume": round(self_bid_volume, 2),
        }
    
    print("盘口覆盖率统计:")
    for slip, data in coverage_results.items():
        print(f"滑点 {slip*100:.2f}‰: 卖单覆盖率 {data['ask_coverage']}%, 买单覆盖率 {data['bid_coverage']}%")
        # print(f"卖单自身量 {data['self_ask_volume']}%, 买单自身量 {data['self_bid_volume']}%")
    return coverage_results


# 获取当前最新行情价格（可用 spot.get_ticker 或其他API）
# ticker_info = spot_market.get_ticker("usdtusd") 
# if ticker_info is not None and 'last' in ticker_info:
#     p = float(ticker_info['last'])
# else:
#     p = 1.0000  # 如果获取失败，则使用默认值
p = 1.0000

current_order_ids_grouped = {"BUY": {}, "SELL": {}}

def cancel_one_order(symbol="usdtusd", side="BUY", price_level=None):
    global current_order_ids_grouped
    if price_level is None:
        return  # No specific group to cancel from

    orders_at_level = current_order_ids_grouped.get(side, {}).get(price_level, [])
    if orders_at_level:
        order_id = orders_at_level.pop(0)
        try:
            order_info = spot_client.get_order(symbol=symbol, order_id=order_id)
            print(f"订单 {order_id} 状态：", order_info)
        except Exception as e:
            print(f"获取订单 {order_id} 状态失败: {e}")
            return  # Skip canceling if order info retrieval fails

        try:
            cancel_result = spot_client.cancel_order(symbol=symbol, order_id=order_id)
            print(f"撤单 side={side}, order_id={order_id} 结果: {cancel_result}")
        except Exception as e:
            print(f"撤销订单 {order_id} 失败: {e}")
    else:
        print(f"没有找到 {side} 侧，价格 {price_level} 的订单可以撤销。")

def place_new_order(symbol, price_level, volume, side):
    global current_order_ids_grouped
    try:
        result = spot_client.new_order(symbol=symbol,
                                       price=str(price_level),
                                       volume=volume,
                                       side=side,
                                       type=1)
        order_id = result.get("order_id")
        if order_id:
            if price_level in current_order_ids_grouped[side]:
                current_order_ids_grouped[side][price_level].append(order_id)
            else:
                current_order_ids_grouped[side][price_level] = [order_id]
        print(f"{side} 侧价格层级 {price_level} 下单结果：", result)
    except Exception as e:
        print(f"{side} 侧价格层级 {price_level} 下单失败: {e}")

# def refresh_orders_grouped():
#     global current_order_ids_grouped, bid_orders, ask_orders
#     symbol = "usdtusd"
    
#     print("开始按组撤单并挂新单...")

#     shuffled_bid_orders = bid_orders.copy()
#     random.shuffle(shuffled_bid_orders)
#     shuffled_ask_orders = ask_orders.copy()
#     random.shuffle(shuffled_ask_orders)

#     print("处理买单...")
#     for order in shuffled_bid_orders:
#         price_level = order["price"]
#         target_volume = order["target_volume"]
#         cancel_one_order(symbol=symbol, side="BUY", price_level=price_level)
#         place_new_order(symbol=symbol, price_level=price_level, volume=target_volume, side="BUY")

#     print("处理卖单...")
#     for order in shuffled_ask_orders:
#         price_level = order["price"]
#         target_volume = order["target_volume"]
#         cancel_one_order(symbol=symbol, side="SELL", price_level=price_level)
#         place_new_order(symbol=symbol, price_level=price_level, volume=target_volume, side="SELL")

def refresh_orders_random():
    global current_order_ids_grouped, bid_orders, ask_orders
    symbol = "usdtusd"
    
    print("开始随机撤单并挂新单...")

    all_orders = []
    for order in bid_orders:
        order["side"] = "BUY"
        all_orders.append(order)
    for order in ask_orders:
        order["side"] = "SELL"
        all_orders.append(order)

    random.shuffle(all_orders)
    
    for order in all_orders:
        price_level = order["price"]
        target_volume = order["target_volume"]
        side = order["side"]
        
        cancel_one_order(symbol=symbol, side=side, price_level=price_level)
        place_new_order(symbol=symbol, price_level=price_level, volume=target_volume, side=side)


while True:
    try:
        # spot_client.cancel_all_orders_by_symbol(symbol='usdtusd')
        ticker_info = spot_market.get_ticker("usdtusd")
        if ticker_info is not None and 'last' in ticker_info:
            p = float(ticker_info['last'])
        else:
            p = 1.0000  
    except Exception as e:
        print(f"获取基准价格失败，使用默认值: {e}")
        p = 1.0000
        p_buy = p - s1
        p_sell = p + s1

    print("基准价格 p =", p)

    try:
        bid_orders = generate_bid_orders(p_buy, s1, bid_target_volumes)
        ask_orders = generate_ask_orders(p_sell, s1, ask_target_volumes)
    except Exception as e:
        print(f"生成订单失败: {e}")
        bid_orders, ask_orders = [], []

    print("生成的买单挂单：")
    for order in bid_orders:
        print(order)
    print("生成的卖单挂单：")
    for order in ask_orders:
        print(order)

    try:
        refresh_orders_random()
    except Exception as e:
        print(f"刷新订单失败: {e}")

    try:
        calculate_order_coverage(p, ask_orders, bid_orders)
    except Exception as e:
        print(f"计算覆盖率失败: {e}")

    sleep_duration = random.uniform(1, 2)
    print(f"等待 {sleep_duration:.2f} 秒后刷新...")
    time.sleep(sleep_duration)
````



