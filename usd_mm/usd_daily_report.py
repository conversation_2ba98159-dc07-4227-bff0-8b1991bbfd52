import pandas as pd
import sys
sys.path.append("/home/<USER>/usd_mm/")
import byex
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import asyncio
import con_pri
import config
import logging
import pytz
from datetime import datetime, timedelta
import requests



beijing_tz = pytz.timezone("Asia/Shanghai")
spot_market = Spot()
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)


async def send_telegram_message(text, chat_id=con_pri.chat_id):
    bot_token = con_pri.bot_token

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message, 'parse_mode': 'Markdown'}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {message}")


async def assets_daily_report():
    now = datetime.now(beijing_tz)
    date = now.date() - timedelta(1)
    account = spot_client.account()
    df_account = pd.DataFrame(account['coin_list'])
    usd_assets = df_account[df_account['coin'] == 'usd']
    usdt_assets = df_account[df_account['coin'] == 'usdt']
    usd_num = float(usd_assets['normal'].iloc[0]) + float(usd_assets['locked'].iloc[0])
    usdt_num = float(usdt_assets['normal'].iloc[0]) + float(usdt_assets['locked'].iloc[0])
    df_trades = config.get_trades()
    df_trades = df_trades.groupby('id').first().reset_index()
    df_trades.loc[:, 'month'] = df_trades['date'].apply(lambda x: x.month)
    df_daily_amount = df_trades[['side', 'volume', 'date']].groupby(['date', 'side']).sum().reset_index()
    df_monthly_amount =  df_trades[['side', 'volume', 'month']].groupby(['month', 'side']).sum().reset_index()
    today_amount = df_daily_amount[df_daily_amount['date']==date]
    monthly_amount = df_monthly_amount[df_monthly_amount['month']==now.month]
    if "BUY" in today_amount['side'].values:
        today_buy_amount = today_amount[today_amount['side'] == 'BUY']['volume'].iloc[0]
    else:
        today_buy_amount = 0
    if "SELL" in today_amount['side'].values:
        today_sell_amount = today_amount[today_amount['side'] == 'SELL']['volume'].iloc[0]
    else:
        today_sell_amount = 0
    if "BUY" in monthly_amount['side'].values:
        month_buy_amount = monthly_amount[monthly_amount['side'] == 'BUY']['volume'].iloc[0]
    else:
        month_buy_amount = 0
    if "SELL" in monthly_amount['side'].values:
        month_sell_amount = monthly_amount[monthly_amount['side'] == 'SELL']['volume'].iloc[0]
    else:
        month_sell_amount = 0

    await send_telegram_message(f'📊 *USD做市日报*\n日期：{str(date)}\n'
                                f'USD余额：{usd_num:.2f}\nUSDT余额：{usdt_num:.2f}\n'
                                f'今日买方刷量：{today_buy_amount:.2f} USDT\n'
                                f'今日卖方刷量：{today_sell_amount:.2f} USDT\n'
                                f'本月累计买方刷量：{month_buy_amount:.2f} USDT\n'
                                f'本月累计卖方刷量：{month_sell_amount: .2f} USDT', chat_id=con_pri.chat_id2)

if __name__ == "__main__":
    asyncio.run(assets_daily_report())