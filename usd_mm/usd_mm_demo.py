import numpy as np
import pandas as pd
import sys
import byex
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import time
import random
import asyncio
import con_pri
import signal
import config
import logging
import pytz
from datetime import datetime
import requests


beijing_tz = pytz.timezone("Asia/Shanghai")
# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        tz = beijing_tz
        dt = datetime.fromtimestamp(timestamp, tz)
        return dt.timetuple()

logging.basicConfig(
    filename='usd_mm.log',  # Log to this file
    level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


def handle_exit(signum, frame):
    logging.info(f"收到信号 {signum}, 进行清理...")
    # 取消所有订单
    try:
        spot_client.cancel_all_orders_by_symbol('usdtusd')
    except Exception as e:
        logging.error(f"撤单失败: {e}")
    # 关闭事件循环
    loop = asyncio.get_event_loop()
    loop.stop()
    logging.info("程序即将退出...")
    sys.exit(0)

# 监听 Ctrl+C (SIGINT) 和 kill (SIGTERM)
signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

spot_market = Spot()
spot_market.BASE_URL = config.base_url
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)
spot_client.BASE_URL = config.base_url

first_run = True

# p_buy = 1.0000
# p_sell = 1.0001
# s1 = 0.0001
# total_bid_volume = 100000
# total_ask_volume = 100000


# 假设从运营接口获取到的精度参数
price_precision = 4  # 价格保留两位小数
amount_precision = 4  # 数量保留五位小数

# 全局字典用于记录当前挂单的订单ID
current_order_ids = {
    "BUY": [],  # 买单订单ID列表
    "SELL": []  # 卖单订单ID列表
}

current_order_ids_grouped = {"BUY": {}, "SELL": {}}

price_level_config = [
    (1, 0),
    (2, 1), (3, 1),
    (4, 3), (5, 3),
    (6, 5), (7, 5),
    (8, 7), (9, 7),
    (10, 9), (11, 9),
    (12, 11), (13, 11),
    (14, 13), (15, 13),
    (16, 15), (17, 15),
    (18, 17), (19, 17),
    (20, 19), (21, 19),
    (22, 21), (23, 21),
    (24, 23), (25, 23),
    (26, 25), (27, 25),
    (28, 27), (29, 27)
]

# p_buy = 1.0
# p_sell = 1.0001
# s1 = 0.001
# total_bid_volume = 5000
# total_ask_volume = 5000


async def send_telegram_message(text):
    bot_token = con_pri.bot_token
    chat_id = con_pri.chat_id

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message, 'parse_mode': 'Markdown'}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {message}")


async def get_params():
    try:
        global p_buy, p_sell, s1, total_bid_volume, total_ask_volume, first_run
        df_params = config.get_mm_params()
        df_params = df_params.loc[(df_params['pair1']=='USDT') & (df_params['pair2']=='USD'),:]
        # 假设基准参数 运营可调配
        p_buy = df_params['bid_price'].iloc[0]
        p_sell = df_params['ask_price'].iloc[0]
        s1 = df_params['base_spread'].iloc[0]  #

        if p_buy <= 0 or p_sell <= 0 or s1 <= 0:
            logging.warning(f"价格参数异常: p_buy={p_buy}, p_sell={p_sell}, s1={s1}")
            await send_telegram_message(f"USD价格参数异常: p_buy={p_buy}, p_sell={p_sell}, s1={s1}")

        # 设置买卖挂单总量（单位：张/单子数量）
        total_bid_volume = df_params['total_bid_volume'].iloc[0]
        total_ask_volume = df_params['total_ask_volume'].iloc[0]
        if total_bid_volume < 1000 or total_ask_volume < 1000:
            logging.warning(f"订单量异常: {total_bid_volume=}小于1000, {total_ask_volume=}小于1000")
            await send_telegram_message(f"USD订单量异常: {total_bid_volume=}小于1000, {total_ask_volume=}小于1000")
        if first_run:
            logging.info(f'USD做市参数设置: p_buy={p_buy}, p_sell={p_sell}, s1={s1}, total_bid_volume={total_bid_volume}, total_ask_volume={total_ask_volume}')
    except Exception as e:
        logging.error(f"USD做市参数获取失败: {e}")


async def cal_order(decay_factor=0.7):
    decay_factor = decay_factor  # 递减因子
    bid_volume_ratios = [decay_factor ** i for i in range(len(price_level_config))]
    ask_volume_ratios = [decay_factor ** i for i in range(len(price_level_config))]
    logging.debug(f"Bid volume ratios: {bid_volume_ratios}")
    logging.debug(f"Ask volume ratios: {ask_volume_ratios}")

    # 计算各层目标挂单数量
    total_bid_weight = sum(bid_volume_ratios)
    total_ask_weight = sum(ask_volume_ratios)
    bid_target_volumes = [total_bid_volume * (w / total_bid_weight) for w in bid_volume_ratios]
    ask_target_volumes = [total_ask_volume * (w / total_ask_weight) for w in ask_volume_ratios]
    return bid_target_volumes, ask_target_volumes


async def random_volume(volume):
    return volume * (1 + random.uniform(-0.3, 0.3))


async def generate_bid_orders(p_buy, s1, bid_target_volumes):
    bid_orders = []
    # 第一层买单价格为p_buy
    bid_price = p_buy
    target_volume = bid_target_volumes[0]
    bid_orders.append({
        "level": 1,
        "price": round(bid_price, price_precision),
        "target_volume": round(await random_volume(target_volume), amount_precision)
    })
    # 后续层
    for idx, (level, multiplier) in enumerate(price_level_config[1:], start=1):
        bid_price = bid_price - s1 * multiplier
        target_volume = bid_target_volumes[idx]
        bid_orders.append({
            "level": level,
            "price": round(bid_price, price_precision),
            "target_volume": round(await random_volume(target_volume), amount_precision)
        })
    return bid_orders


async def generate_ask_orders(p_sell, s1, ask_target_volumes):
    ask_orders = []
    # 第一层卖单价格为p_sell
    ask_price = p_sell
    target_volume = ask_target_volumes[0]
    ask_orders.append({
        "level": 1,
        "price": round(ask_price, price_precision),
        "target_volume": round(await random_volume(target_volume), amount_precision)
    })
    # 后续层：卖单价格向上递增
    for idx, (level, multiplier) in enumerate(price_level_config[1:], start=1):
        ask_price = ask_price + s1 * multiplier
        target_volume = ask_target_volumes[idx]
        ask_orders.append({
            "level": level,
            "price": round(ask_price, price_precision),
            "target_volume": round(await random_volume(target_volume), amount_precision)
        })
    return ask_orders


# 计算铺单覆盖率
async def calculate_order_coverage(p_buy, p_sell,  ask_orders, bid_orders):
    try:
        asks_df, bids_df = spot_market.get_orderbook("usdtusd", "step0")  # 返回两个 DataFrame
        logging.info(f"Orderbook DataFrame Received:\n{asks_df.head()}\n{bids_df.head()}")

        # 提取价格和数量
        asks = list(zip(asks_df["asks_price"].astype(float), asks_df["asks_qty"].astype(float)))
        bids = list(zip(bids_df["bids_price"].astype(float), bids_df["bids_qty"].astype(float)))

        if not asks or not bids:
            logging.warning("盘口数据异常：买卖数据为空，可能存在连接问题或市场暂停")
            await send_telegram_message("USD盘口数据异常：买卖数据为空，可能存在连接问题或市场暂停")

    except Exception as e:
        logging.error(f"获取盘口数据失败: {e}")
        return

    if not isinstance(asks, list) or not isinstance(bids, list):
        logging.error(f"盘口数据格式异常")
        return

    coverage_results = {}
    coverage_levels = [0.001, 0.002, 0.005, 0.01, 0.02]

    for slippage in coverage_levels:
        ask_limit = p_sell * (1 + slippage)
        bid_limit = p_buy * (1 - slippage)

        # 统计市场上的量
        market_ask_volume = sum(qty for price, qty in asks if price <= ask_limit)
        market_bid_volume = sum(qty for price, qty in bids if price >= bid_limit)

        if market_bid_volume < 5000 or market_ask_volume < 5000:
            logging.warning(f"市场流动性低: bid={market_bid_volume}, ask={market_ask_volume}")
            await send_telegram_message(f"USD{slippage}滑点下市场流动性低: bid={market_bid_volume}, ask={market_ask_volume}")

        # 统计自身订单量
        self_ask_volume = sum(order["target_volume"] for order in ask_orders if order["price"] <= ask_limit)
        self_bid_volume = sum(order["target_volume"] for order in bid_orders if order["price"] >= bid_limit)

        # 计算覆盖率
        ask_coverage = (self_ask_volume / market_ask_volume) if market_ask_volume > 0 else 0
        bid_coverage = (self_bid_volume / market_bid_volume) if market_bid_volume > 0 else 0

        # if ask_coverage > 0.8 or bid_coverage > 0.8:
        #     logging.warning(f"订单过量: 买单覆盖率 {bid_coverage * 100}%, 卖单覆盖率 {ask_coverage * 100}%")
        # await send_telegram_message("USD订单过量: 买单覆盖率 {bid_coverage * 100}%, 卖单覆盖率 {ask_coverage * 100}%")

        coverage_results[slippage] = {
            "ask_coverage": round(ask_coverage * 100, 2),
            "bid_coverage": round(bid_coverage * 100, 2),
            "self_ask_volume": round(self_ask_volume, 2),
            "self_bid_volume": round(self_bid_volume, 2),
        }

    logging.info("盘口覆盖率统计:")
    for slip, data in coverage_results.items():
        logging.info(f"滑点 {slip * 100:.2f}‰: 卖单覆盖率 {data['ask_coverage']}%, 买单覆盖率 {data['bid_coverage']}%")
    return coverage_results


async def place_new_order(symbol, price_level, volume, side, sem):
    """ 挂单 """
    global current_order_ids_grouped
    async with sem:  # 控制并发
        try:
            result = await spot_client.async_new_order(
                symbol=symbol,
                price=str(price_level),
                volume=volume,
                side=side,
                type=1)
            order_id = result.get("order_id")
            if order_id:
                if price_level in current_order_ids_grouped[side]:
                    current_order_ids_grouped[side][price_level].append(order_id)
                else:
                    current_order_ids_grouped[side][price_level] = [order_id]
                logging.info(f"{side} 侧价格 {price_level} 下单成功：", result)
            if not order_id:
                logging.warning(f"{side} 侧价格 {price_level} 挂单失败，API返回无订单ID")
                await send_telegram_message(f"USD {side} 侧价格 {price_level} 挂单失败，API返回无订单ID")
        except byex.exceptions.SymbolNotFoundError:
            logging.warning("交易对 usdtusd 不存在或已被暂停交易")
        except Exception as e:
            logging.error(f"{side} 侧价格 {price_level} 下单失败: {e}")



async def cancel_and_place_order(symbol, price_level, target_volume, side, sem, price_list):
    """ 撤单后立即挂新单 """
    global current_order_ids_grouped
    async with sem:
        # 先撤单
        orders_at_level = current_order_ids_grouped.get(side, {}).get(price_level, [])
        closest_price = None
        if not orders_at_level:
            old_price_levels = list(set(list(current_order_ids_grouped.get(side, {}).keys())) - set(price_list))
            if old_price_levels:
                closest_price = min(old_price_levels, key=lambda x: abs(x - price_level))
                logging.info(f"新价格 {price_level} 没有挂单，使用最近的价格 {closest_price} 撤单")
                orders_at_level = current_order_ids_grouped[side][closest_price]
        if not orders_at_level:
            logging.info(f"{side} 侧 {price_level} 没有订单，直接新挂单！")
            await place_new_order(symbol, price_level, target_volume, side, sem)
            return

        order_id = orders_at_level.pop(0)
        try:
            cancel_result = await spot_client.async_cancel_order(symbol=symbol, order_id=order_id)
            logging.info(f"撤单 {side}, order_id={order_id} 结果: {cancel_result}")
            if closest_price:
                del current_order_ids_grouped[side][closest_price]
        except Exception as e:
            logging.error(f"撤销订单 {order_id} 失败: {e}")
            logging.warning(f"撤单失败: {side} 侧, order_id={order_id}, 可能已经成交或API错误")
            await send_telegram_message(f"USD撤单失败: {side} 侧, order_id={order_id}, 可能已经成交或API错误")
            return  # 撤单失败就不挂新单
        # 再挂新单
        await place_new_order(symbol, price_level, target_volume, side, sem)
        return




async def initialize_orders(bid_orders, ask_orders, sem):
    """ 首次执行时，批量铺单 """
    global first_run
    symbol = "usdtusd"

    logging.info("首次执行，批量挂单...")

    # 先分别拿到 `bid` 和 `ask` 订单
    bid_tasks = [place_new_order(symbol, order["price"], order["target_volume"], "BUY", sem) for order in bid_orders]
    ask_tasks = [place_new_order(symbol, order["price"], order["target_volume"], "SELL", sem) for order in ask_orders]

    # 同时并发挂单
    await asyncio.gather(*bid_tasks, *ask_tasks)  # 执行所有挂 `bid` 和 `ask` 单的任务

    first_run = False  # 标记首次挂单完成


async def refresh_orders_random(bid_orders, ask_orders, sem, random_upper:float=60, random_lower:float=0.5):
    """ 随机撤销部分订单后重新挂单 """
    global first_run
    symbol = "usdtusd"

    if first_run:
        await initialize_orders(bid_orders, ask_orders, sem)  # 仅首次执行
        return

    logging.info("开始随机撤单并挂新单...")

    all_orders = []
    for order in bid_orders:
        order["side"] = "BUY"
        all_orders.append(order)
    for order in ask_orders:
        order["side"] = "SELL"
        all_orders.append(order)

    random.shuffle(all_orders)  # 乱序执行

    # **撤一单后立即挂一单，每次睡随机时间**
    price_list = [order["price"] for order in all_orders]
    for order in all_orders:
        await cancel_and_place_order(symbol, order["price"], target_volume=order["target_volume"], side=order["side"], sem=sem, price_list=price_list)
        random_time = random.uniform(random_lower, random_upper)
        await asyncio.sleep(random_time)  # 每个订单执行后等待 x 秒
    logging.info("订单更新完成。")


async def monitor_params(sem, interval=5):
    """ 定期检查参数变化 """
    global p_buy, p_sell, s1, total_bid_volume, total_ask_volume, first_run, current_order_ids_grouped
    prev_params = None

    while True:
        await get_params()
        current_params = (p_buy, p_sell, s1, total_bid_volume, total_ask_volume)

        # 如果参数发生变化，则重新铺单
        if prev_params and current_params != prev_params:
            logging.info(f"检测到参数变化: {prev_params} → {current_params}, 开始重新铺单...")
            bid_target_volumes, ask_target_volumes = await cal_order()

            bid_orders = await generate_bid_orders(p_buy, s1, bid_target_volumes)
            ask_orders = await generate_ask_orders(p_sell, s1, ask_target_volumes)
            # first_run = True
            # spot_client.cancel_all_orders_by_symbol('usdtusd')
            # current_order_ids_grouped = {"BUY": {}, "SELL": {}}
            await refresh_orders_random(bid_orders, ask_orders, sem, random_upper=0.1, random_lower=0)

        prev_params = current_params
        await asyncio.sleep(interval)  # 每 `interval` 秒检查一次参数


async def shortage_warning(coin: str, amount: float):
    while True:
        account = spot_client.account()
        df_account = pd.DataFrame(account['coin_list'])
        coin_assets = df_account[df_account['coin'] == coin]
        coin_num = float(coin_assets['normal'].iloc[0]) + float(coin_assets['locked'].iloc[0])
        if coin_num < amount:
            msg = f'{coin}数量不足{amount}，请及时对冲'
            await send_telegram_message(msg)
        await asyncio.sleep(3600)


async def assets_daily_report():
    while True:
        now = datetime.now(beijing_tz)
        if now.hour == 0 and now.minute == 0:
            account = spot_client.account()
            df_account = pd.DataFrame(account['coin_list'])
            usd_assets = df_account[df_account['coin'] == 'usd']
            usdt_assets = df_account[df_account['coin'] == 'usdt']
            usd_num = float(usd_assets['normal'].iloc[0]) + float(usd_assets['locked'].iloc[0])
            usdt_num = float(usdt_assets['normal'].iloc[0]) + float(usdt_assets['locked'].iloc[0])
            await send_telegram_message(f'📊 *每日余额播报*\n日期：{str(now.date())}\nUSD余额：{usd_num:.2f}\nUSDT余额：{usdt_num:.2f}')
        await asyncio.sleep(60)


async def main():
    # 限制最大并发请求数
    SEM = asyncio.Semaphore(5)  # 每次最多并发 5 个请求
    spot_client.cancel_all_orders_by_symbol('usdtusd')
    await get_params()
    # 启动参数监控任务
    asyncio.create_task(monitor_params(SEM, interval=5))
    # 启动币种余额监控任务
    asyncio.create_task(shortage_warning('usd', amount=100000))
    asyncio.create_task(shortage_warning('usdt', amount=100000))
    # 定时余额播报
    asyncio.create_task(assets_daily_report())
    while True:
        bid_target_volumes, ask_target_volumes = await cal_order()
        try:
            bid_orders = await generate_bid_orders(p_buy, s1, bid_target_volumes)
            ask_orders = await generate_ask_orders(p_sell, s1, ask_target_volumes)
        except Exception as e:
            logging.error(f"生成订单失败: {e}")
            bid_orders, ask_orders = [], []

        logging.info("生成的买单挂单：")
        for order in bid_orders:
            logging.info(order)
        logging.info("生成的卖单挂单：")
        for order in ask_orders:
            logging.info(order)

        try:
            await refresh_orders_random(bid_orders, ask_orders, SEM, random_upper=5, random_lower=0.5)
        except Exception as e:
            logging.error(f"刷新订单失败: {e}")
            await asyncio.sleep(5)

        try:
            await calculate_order_coverage(p_buy, p_sell, ask_orders, bid_orders)
        except Exception as e:
            logging.error(f"计算覆盖率失败: {e}")
            await asyncio.sleep(5)

        sleep_duration = random.uniform(300, 600)
        logging.info(f"等待 {sleep_duration:.2f} 秒后刷新...")
        await asyncio.sleep(sleep_duration)


if __name__ == '__main__':
    asyncio.run(main())