import requests
import os
import sys
sys.path.append(os.path.dirname(os.getcwd()))
from spot.market import Spot
from byex.spot.trade import SpotTrade
import pandas as pd
import time


api_key = ''
secret_key = ''
spot_client = SpotTrade(api_key=api_key, secret_key=secret_key)


def send_telegram_message(text):
    bot_token = "**********************************************"
    chat_id = "-1002324246464"

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
        print("Telegram message sent successfully!")
    except Exception as e:
        print(f"Failed to send Telegram message: {e}")


def shortage_warning(account: dict, coin: str, amount: float):
    df_account = pd.DataFrame(account['coin_list'])
    coin_assets = df_account[df_account['coin'] == coin]
    coin_num = float(coin_assets['normal'].iloc[0]) + float(coin_assets['locked'].iloc[0])
    if coin_num < amount:
        msg = f'{coin}数量不足{amount}，请注意划转'
        send_telegram_message(msg)


def main():
    # while True:
    account = spot_client.account()
    shortage_warning(account, "usdt", 500)
    shortage_warning(account, "usd", 500)
    time.sleep(0)


if __name__ == "__main__":
    main()