import os
import sys
sys.path.append(os.path.dirname(os.getcwd()))
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import pandas as pd


api_key = '6c08c0b81a9077376f4b712b3f312720'
secret_key = '97d5af0b5666ce47159af76037eba0da'
spot = Spot(api_key=api_key, secret_key=secret_key)
spot_client = SpotTrade(api_key=api_key, secret_key=secret_key)


def test():
    # order = spot_client.new_order(symbol='usdtusd', side='buy', type=1, price=0.901, volume=10)
    # print(order)
    # {'order_id': 428565917069213696}
    # order = spot_client.cancel_and_replace(symbol='usdtusd', mass_place=[{'price': 0.9002, 'volume': 1, 'side': 'buy', 'type': 1}],
    #                                        mass_cancel=[428565917069213696])
    # print(order)
    # 428567671886315521
    order = spot_client.cancel_order(symbol='usdtusd', order_id=428567671886315521)
    return order


if __name__ == '__main__':
    account = spot_client.account()
    df_account = pd.DataFrame(account['coin_list'])