nohup: ignoring input
sorted_keys  ['api_key', 'symbol', 'time']
Signing String: api_key875c05cebc511ac21919cff82620d887symbolusdtusdtime1744089110264537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'time']
Signing String: api_key875c05cebc511ac21919cff82620d887time*************537437b5bc7748a1a6b02a0428ada312
Request URL: https://openapi.100exdemo.com/open/api/user/account
Request Params: {'api_key': '875c05cebc511ac21919cff82620d887', 'time': '*************', 'sign': 'bd4a2a41eadd2d6ab8d5f29ae31dbd59'}
sorted_keys  ['api_key', 'time']
Signing String: api_key875c05cebc511ac21919cff82620d887time*************537437b5bc7748a1a6b02a0428ada312
Request URL: https://openapi.100exdemo.com/open/api/user/account
Request Params: {'api_key': '875c05cebc511ac21919cff82620d887', 'time': '*************', 'sign': '1afb5676a56ec81db9b5c4aa3bc59a50'}
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.998sideBUYsymbolusdtusdtime1744089116261type1volume124281.2893537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9979sideBUYsymbolusdtusdtime1744089116262type1volume128039.1444537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9978sideBUYsymbolusdtusdtime1744089116263type1volume60766.0229537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9975sideBUYsymbolusdtusdtime1744089116263type1volume63982.2433537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9972sideBUYsymbolusdtusdtime1744089116264type1volume40955.2093537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9967sideBUYsymbolusdtusdtime1744089116913type1volume22093.2197537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9962sideBUYsymbolusdtusdtime1744089116922type1volume13444.375537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9955sideBUYsymbolusdtusdtime1744089117565type1volume11107.3538537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9948sideBUYsymbolusdtusdtime1744089117713type1volume8913.2104537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9939sideBUYsymbolusdtusdtime1744089117715type1volume5999.7479537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.993sideBUYsymbolusdtusdtime1744089117739type1volume5334.9101537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9919sideBUYsymbolusdtusdtime1744089118221type1volume2285.5522537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9908sideBUYsymbolusdtusdtime1744089118364type1volume1894.2459537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9895sideBUYsymbolusdtusdtime1744089118391type1volume1770.147537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9882sideBUYsymbolusdtusdtime1744089118395type1volume1176.8129537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9867sideBUYsymbolusdtusdtime1744089118864type1volume696.9876537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9852sideBUYsymbolusdtusdtime1744089119004type1volume589.3831537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9835sideBUYsymbolusdtusdtime1744089119038type1volume401.3677537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9818sideBUYsymbolusdtusdtime1744089119169type1volume224.1865537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9799sideBUYsymbolusdtusdtime1744089119502type1volume175.6574537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.978sideBUYsymbolusdtusdtime1744089119663type1volume145.7982537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9759sideBUYsymbolusdtusdtime1744089119708type1volume74.14537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9738sideBUYsymbolusdtusdtime1744089119829type1volume61.5323537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9715sideBUYsymbolusdtusdtime1744089119836type1volume47.906537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9692sideBUYsymbolusdtusdtime1744089120148type1volume22.5918537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9667sideBUYsymbolusdtusdtime1744089120336type1volume22.6936537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9642sideBUYsymbolusdtusdtime1744089120488type1volume9.8751537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9615sideBUYsymbolusdtusdtime1744089120505type1volume7.0656537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price0.9588sideBUYsymbolusdtusdtime1744089120828type1volume6.0907537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.002sideSELLsymbolusdtusdtime1744089120977type1volume137966.1886537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0021sideSELLsymbolusdtusdtime1744089121167type1volume87658.0924537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0022sideSELLsymbolusdtusdtime1744089121172type1volume87240.7543537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0025sideSELLsymbolusdtusdtime1744089121219type1volume65666.523537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0028sideSELLsymbolusdtusdtime1744089121475type1volume34387.3221537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0033sideSELLsymbolusdtusdtime1744089121628type1volume24804.6537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0038sideSELLsymbolusdtusdtime1744089121883type1volume12993.7373537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
API返回错误: "Insufficient available balance."
API返回错误: "Insufficient available balance."
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0045sideSELLsymbolusdtusdtime1744089130601type1volume9094.5595537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0052sideSELLsymbolusdtusdtime1744089130601type1volume10094.2378537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0061sideSELLsymbolusdtusdtime1744089130601type1volume5642.5093537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.007sideSELLsymbolusdtusdtime1744089130602type1volume4259.8889537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0081sideSELLsymbolusdtusdtime1744089131031type1volume3391.7433537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0092sideSELLsymbolusdtusdtime1744089131292type1volume1587.1868537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0105sideSELLsymbolusdtusdtime1744089131295type1volume1527.0806537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0118sideSELLsymbolusdtusdtime1744089131297type1volume1053.4951537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0133sideSELLsymbolusdtusdtime1744089131298type1volume613.513537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0148sideSELLsymbolusdtusdtime1744089131705type1volume490.2746537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0165sideSELLsymbolusdtusdtime1744089131979type1volume403.6347537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0182sideSELLsymbolusdtusdtime1744089131985type1volume278.1599537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0201sideSELLsymbolusdtusdtime1744089132389type1volume161.4648537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.022sideSELLsymbolusdtusdtime1744089132658type1volume92.6365537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0241sideSELLsymbolusdtusdtime1744089132686type1volume84.2235537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0262sideSELLsymbolusdtusdtime1744089132774type1volume75.8831537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0285sideSELLsymbolusdtusdtime1744089132774type1volume37.383537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0308sideSELLsymbolusdtusdtime1744089133044type1volume22.4107537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0333sideSELLsymbolusdtusdtime1744089133331type1volume20.239537437b5bc7748a1a6b02a0428ada312
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0358sideSELLsymbolusdtusdtime1744089133335type1volume17.8067537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0385sideSELLsymbolusdtusdtime1744089133415type1volume7.9158537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'price', 'side', 'symbol', 'time', 'type', 'volume']
Signing String: api_key875c05cebc511ac21919cff82620d887price1.0412sideSELLsymbolusdtusdtime1744089133416type1volume7.9337537437b5bc7748a1a6b02a0428ada312
API返回错误: "Insufficient available balance."
API返回错误: "Insufficient available balance."
API返回错误: "Insufficient available balance."
sorted_keys  ['api_key', 'symbol', 'time']
Signing String: api_key875c05cebc511ac21919cff82620d887symbolusdtusdtime1744089171616537437b5bc7748a1a6b02a0428ada312
