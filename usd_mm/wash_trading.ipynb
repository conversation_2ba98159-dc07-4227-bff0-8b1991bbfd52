#%%
import pandas as pd
import os
import sys
sys.path.append(os.path.dirname(os.getcwd()))
from spot.market import Spot
from spot.trade import SpotTrade
import random
#%%
spot_market = Spot()
api_key = "506d50afcd6d2fd7e26b9afd69a4f189"
api_secret = "7002c53a26b4a19bc91a5a9241c93e24"
spot_client = SpotTrade(api_key, api_secret)
#%%
# 获取asks，bids
df_asks, df_bids = spot_market.get_orderbook('usdtusd')
#%%
df_asks
#%%
df_bids
#%%
random_time_list = [round(random.uniform(1,5), 1) for _ in range(0,5)]
print(random_time_list)
#%%
random_side = ['buy' if random.randrange(0, 2)==1 else 'sell' for _ in range(0,5)]
random_side
#%%

#%%
