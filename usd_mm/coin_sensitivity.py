import numpy as np
import pandas as pd
import statsmodels.api as sm
from binance.client import Client
from binance import ThreadedWebsocketManager
import time


client = Client()

def process_trade_message(msg):
    """
    处理 WebSocket 返回的交易数据
    """
    trade_time = pd.to_datetime(msg["T"], unit="ms")
    price = float(msg["p"])
    quantity = float(msg["q"])
    is_buyer_maker = msg["m"]  # True 表示卖单（市场买单）

    print(f"{trade_time} - Price: {price}, Quantity: {quantity}, Buyer Maker: {is_buyer_maker}")

# WebSocket 监听逐笔交易数据
def start_trade_websocket(symbol="btcusdt"):
    twm = ThreadedWebsocketManager(api_key='', api_secret='')
    twm.start()
    twm.start_trade_socket(callback=process_trade_message, symbol=symbol)

# # 启动 WebSocket 监听 BTC 交易数据
# start_trade_websocket("BTCUSDT")

# todo: 确保获取2小时数据
# 获取逐笔交易数据
def get_historical_trades(symbol="BTCUSDT", limit=10000, start_from_id=None):
    """
    获取 Binance 逐笔历史交易数据
    :param symbol: 交易对 (如 "BTCUSDT")
    :param limit: 获取交易数据的笔数
    :param start_from_id: 初始交易 ID (可选)
    :return: DataFrame
    """
    df = pd.DataFrame()
    if limit <= 1000:
        trades = client.get_recent_trades(symbol=symbol, limit=limit)  # 获取交易数据
        df = pd.DataFrame(trades)
    elif limit > 1000:
        all_trades = []
        last_trade_id = start_from_id
        for _ in range(limit // 1000):
            try:
                if last_trade_id:
                    trades = client.get_historical_trades(symbol=symbol, fromId=last_trade_id, limit=1000)
                else:
                    trades = client.get_historical_trades(symbol=symbol, limit=1000)
                if not trades:
                    break  # 没有更多数据
                all_trades.extend(trades)
                last_trade_id = trades[0]["id"] - 1000  # 取第一条交易 ID 并减1000
                print(f'last_trade_id: {last_trade_id}')
                print(f"已获取 {len(all_trades)} 条交易数据...")
                time.sleep(0.1)  # 避免 API 速率限制
            except Exception as e:
                print(f"错误: {e}")
                break
        df = pd.DataFrame(all_trades)
    else:
        print('limit参数填写错误')
        return
    if not df.empty:
        df = df.rename(columns={'time': 'timestamp'})
        # 处理数据
        df["time"] = pd.to_datetime(df["timestamp"], unit="ms", utc=True).dt.tz_convert('Asia/shanghai').dt.tz_localize(None)  # 转换时间格式
        df["quantity"] = df["qty"].astype(float)  # 成交量
        df["price"] = df["price"].astype(float)  # 成交价格
        df["is_buyer_maker"] = df["isBuyerMaker"].astype(bool)  # True 表示被动卖单
        df['side'] = df["is_buyer_maker"].apply(lambda x: 'buy' if x else 'sell')
        return df[["timestamp", "time", "price", "quantity", "side"]]


def calculate_order_imbalance(df, interval="1min"):
    """
    计算订单失衡 D_t = Buy Volume - Sell Volume
    :param df: 逐笔成交数据 DataFrame
    :param interval: 时间间隔 (默认 1 分钟)
    :return: 订单失衡 DataFrame
    """
    df["buy_volume"] = df["quantity"] * (df["side"]=="buy")  # 买单量
    df["sell_volume"] = df["quantity"] * (df["side"]=='sell')  # 卖单量
    df.set_index("time", inplace=True)  # 设定时间索引
    # 按时间间隔聚合买卖单量
    imbalance_df = df.resample(interval).agg({"timestamp": "first", "buy_volume": "sum", "sell_volume": "sum"})
    # 计算订单失衡
    imbalance_df["order_imbalance"] = imbalance_df["buy_volume"] - imbalance_df["sell_volume"]
    return imbalance_df


def get_kline_data_local(symbol: str) -> pd.DataFrame:
    symbol = symbol.upper().replace('_', '')
    if '-' in symbol:
        symbol = symbol.split('-')[1] + 'USDT'
    path = f'/Users/<USER>/PycharmProjects/1m_kline_analyse/minute_price/{symbol}_spot.csv'
    kline_data = pd.read_csv(path)
    try:
        # 处理缺失值
        # 如果第一个值缺失，填充0
        if pd.isna(kline_data.loc[0, 'open']):
            kline_data.loc[0, 'open'] = 0
        if pd.isna(kline_data.loc[0, 'high']):
            kline_data.loc[0, 'high'] = 0
        if pd.isna(kline_data.loc[0, 'low']):
            kline_data.loc[0, 'low'] = 0
        if pd.isna(kline_data.loc[0, 'close']):
            kline_data.loc[0, 'close'] = 0
        # 用前值填充其他缺失值
        kline_data['open'] = kline_data['open'].ffill()
        kline_data['high'] = kline_data['high'].ffill()
        kline_data['low'] = kline_data['low'].ffill()
        kline_data['close'] = kline_data['close'].ffill()
        # 价格全部float
        kline_data['open'] = kline_data[f'open'].astype(float)
        kline_data['high'] = kline_data[f'high'].astype(float)
        kline_data['low'] = kline_data[f'low'].astype(float)
        kline_data['close'] = kline_data[f'close'].astype(float)
        kline_data = kline_data.drop_duplicates()
    except:
        pass
    return kline_data


def get_kline_data(symbol: str, interval: str, start_ts: int=None, end_ts: int=None) -> pd.DataFrame:
    symbol = symbol.upper().replace('_', '')
    all_klines = []
    limit = 1000  # 每次最多 1000 根
    if start_ts and end_ts:
        while start_ts < end_ts:
            try:
                # 获取 K 线数据
                klines = client.get_klines(symbol=symbol, interval=interval, startTime=start_ts, limit=limit)
                if not klines:
                    break  # 没有更多数据
                all_klines.extend(klines)
                # 取最后一根 K 线的时间戳
                start_ts = klines[-1][0] + 1
                print(f"已获取 {len(all_klines)} 条 K 线数据...")
                time.sleep(0.1)  # 防止 API 速率限制
            except Exception as e:
                print(f"错误: {e}")
                break
    else:
        try:
            # 获取 K 线数据
            klines = client.get_klines(symbol=symbol, interval=interval, limit=limit)
            all_klines.extend(klines)
        except Exception as e:
            print(f"错误: {e}")
    df = pd.DataFrame(all_klines, columns=["open_time", "open", "high", "low", "close", "volume",
                                           "close_time", "quote_asset_volume", "trades",
                                           "taker_base_vol", "taker_quote_vol", "ignore"])

    # 数据处理
    df[["open", "high", "low", "close", "volume"]] = df[["open", "high", "low", "close", "volume"]].astype(float)
    df[["open", "high", "low", "close" ]] = df[["open", "high", "low", "close"]].ffill()
    return df[["open_time", "open", "high", "low", "close", "volume"]]


def merge_market_data(imbalance_data: pd.DataFrame, symbol: str='USDCUSDT', interval: str='5m') -> pd.DataFrame:
    # 获取btc分钟return
    interval = interval.replace('min', 'm')
    btc_kline = get_kline_data(symbol='BTCUSDT', interval=interval)
    btc_kline.loc[:, 'time'] = pd.to_datetime(btc_kline.loc[:, 'open_time'], unit='ms', utc=True).dt.tz_convert('Asia/shanghai').dt.tz_localize(None)
    btc_kline.index = btc_kline['time']
    btc_kline.loc[:, 'btc_return'] = btc_kline['close'].pct_change(fill_method=None)
    btc_kline = btc_kline.reset_index(drop=True)
    # 获取coin分钟return
    coin_kline = get_kline_data(symbol=symbol, interval=interval)
    coin_kline.loc[:, 'time'] = pd.to_datetime(coin_kline.loc[:, 'open_time'], unit='ms', utc=True).dt.tz_convert('Asia/shanghai').dt.tz_localize(None)
    coin_kline.index = coin_kline['time']
    coin_kline.loc[:, 'coin_return'] = coin_kline['close'].pct_change(fill_method=None)
    coin_kline = coin_kline.reset_index(drop=True)
    df_merged = pd.merge(imbalance_data, coin_kline[['time', 'coin_return']], on='time', how='left')
    df_merged = pd.merge(df_merged, btc_kline[['time', 'btc_return']], on='time', how='left')
    df_merged = df_merged[['time', 'coin_return', 'btc_return', 'order_imbalance']]
    return df_merged


def cal_coin_sensitivity(dataframe: pd.DataFrame):
    # 用市场收益对订单失衡回归，获取残差
    # 归一化订单失衡
    dataframe["order_imbalance"] = (dataframe["order_imbalance"] - dataframe["order_imbalance"].mean()) / dataframe[
        "order_imbalance"].std()
    X = sm.add_constant(dataframe["order_imbalance"])  # 添加常数项
    y = dataframe["btc_return"] * 100
    liq_model = sm.OLS(y, X).fit()
    dataframe.loc[:,"liq_t"] = liq_model.resid  # 市场流动性因子（残差
    # 归一化liq
    dataframe["liq_t"] = (dataframe["liq_t"] - dataframe["liq_t"].mean()) / dataframe["liq_t"].std()
    # 回归个股收益率 r_i,t 对 市场收益 r_m,t 和流动性因子 LIQ_t
    X = dataframe[["btc_return", "liq_t"]]
    X = sm.add_constant(X)
    y = dataframe["coin_return"] * 100
    gamma_model = sm.OLS(y, X).fit()
    gamma = gamma_model.params["liq_t"]  # γ 系数
    return gamma


def calculate_order_distribution_optimized(gamma: float, threshold: float=0.2, min_pct: float=20):
    """
    根据gamma调整做市铺单分配比例，同时增加中性区间，避免频繁调整做市策略
    """
    # 对gamma缩放在-1到1之间
    gamma = max(min(gamma, 1), -1)
    # 小于阈值平衡做市
    ask_pct, bid_pct = 50, 50
    if abs(gamma) < threshold:
        return ask_pct, bid_pct  # 保持平衡做市
    # 最低min_pct
    elif gamma < 0:
        bid_pct = min_pct + (1 + gamma) / 2 * 60
        ask_pct = 100 - bid_pct
    elif gamma > 0:
        ask_pct = min_pct + (1 + gamma) / 2 * 60
        bid_pct = 100 - ask_pct
    return round(ask_pct, 2), round(bid_pct, 2)


# todo: 计算BTC一小时订单失衡率，判断流动性增强还是减少
def cal_market_order_imbalance_rate():
    pass


def main(coin: str='USDC'):
    # 获取数据
    symbol = coin.upper() + 'USDT'
    df_trades = get_historical_trades(symbol, limit=100000)
    # print(df_trades.head())
    # df_trades.to_csv("USDCUSDT_trades_20250305.csv", index=None)
    # df_trades = pd.read_csv("USDCUSDT_trades_20250305.csv")
    # df_trades['time'] = pd.to_datetime(df_trades['time'])

    # 计算 5 分钟订单失衡
    df_imbalance = calculate_order_imbalance(df_trades, interval="5min")
    # print(df_imbalance.head())

    df_merged = merge_market_data(df_imbalance, symbol=symbol, interval='5min')
    print(df_merged)
    res = cal_coin_sensitivity(df_merged)
    ask_pct, bid_pct = calculate_order_distribution_optimized(res)
    print(f'{coin}敏感度gamma：{round(res, 2)}，BTC流动性增加时，ask比例：{ask_pct}%，bid比例：{bid_pct}%')
    return res


if __name__ == "__main__":
    r = main(coin='BNB')

