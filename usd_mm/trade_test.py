import sys
import os
sys.path.append(os.path.dirname(os.getcwd()))
from spot.market import Spot
from spot.trade import SpotTrade



spot_market = Spot()
spot_client = SpotTrade(api_key, api_secret)

#result = spot_market.get_all_ticker()
#print(result)
#result = spot_client.account()
#result = spot_client.get_orders("btcusdt")
#result = spot_client.my_trades("btcusdt")
result = spot_client.new_order(symbol="usdtusd", price="0.9950", volume=1, side="BUY", type=1)
order_id = result["order_id"]
##result = spot_client.cancel_order(symbol="usdtusd", order_id=427038298125369344)
print("new ortder", result)
result = spot_client.get_order(symbol="usdtusd", order_id = order_id)
print("get order", result)
result = spot_client.cancel_order(symbol="usdtusd", order_id = order_id)
print("cancel order", result)
result = spot_client.get_order(symbol="usdtusd", order_id = order_id)
print("get order", result)