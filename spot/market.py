import pandas as pd
from byex.spot.__init__ import Spot


class Spot(Spot):
    def __init__(self, api_key: str = None, secret_key: str = None):
        super().__init__()
        self.API_KEY = api_key
        self.SECRET_KEY = secret_key
        self.BASE_URL = "https://openapi.100ex.com"

    def get_all_ticker(self):
        """
        get all spot symbol ticker

        """
        data = self._request("GET", "/open/api/get_allticker")
        return data
        #dataframe = pd.DataFrame(data['ticker'])
        #dataframe.loc[:, 'timestamp'] = data['date']
        #return dataframe


    def get_kline(self, symbol: str, interval: int=1):
        """get_kline
        get symbol kline
        Args:
            symbol: symbol name
            interval: 单位为分钟，例如: 1分钟则为1，一天则为1440

        """
        params = {"symbol": symbol, "period": interval}
        data =  self._request("GET", "/open/api/get_records", params=params)
        dataframe = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        return dataframe



    def get_ticker(self, symbol):
        """
        get symbol ticker
        Args:
            symbol: symbol name
        """
        params = {"symbol": symbol}
        data = self._request("GET", "/open/api/get_ticker", params=params)
        series = pd.Series(data)
        return series


    def get_orderbook(self, symbol: str, type: str='step0'):
        """
        get symbol orderbook
        Args:
            symbol: symbol name
            type: 深度类型，step0, step1, step2（合并深度0-2）；step0时，精度最高

        """
        params = {"symbol": symbol, "type": type}
        data = self._request("GET", "/open/api/market_dept", params=params)
        df_asks = pd.DataFrame(data['tick']['asks'], columns=['asks_price', 'asks_qty'])
        df_bids = pd.DataFrame(data['tick']['bids'], columns=['bids_price', 'bids_qty'])
        return df_asks, df_bids


    def get_trade_price(self):
        """
        获取各个币对的最新成交价

        """
        data = self._request("GET", "/open/api/market")
        dataframe = pd.DataFrame.from_dict(data, orient='index', columns=['price'])
        dataframe.reset_index(inplace=True)
        dataframe.rename(columns={'index': 'symbol'}, inplace=True)
        # dataframe = pd.Series(data).reset_index()
        # dataframe = dataframe.rename(columns={'index': 'symbol', 0: 'price'})
        return dataframe


    def get_precision(self):
        """
        查询系统支持的所有交易对及精度

        """
        data = self._request("GET", "/open/api/common/symbols")
        dataframe = pd.DataFrame(data)
        return dataframe


    def get_trades(self, symbol: str):
        """
        获取交易对全市场成交纪录

        """
        data = self._request("GET", "/open/api/get_trades", params={"symbol": symbol})
        dataframe = pd.DataFrame(data)
        return dataframe
