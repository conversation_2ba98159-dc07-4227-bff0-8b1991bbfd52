import requests
import time
import hmac
import hashlib
from typing import Dict


class Spot:
    def __init__(self, api_key: str = None, secret_key: str = None):
        self.API_KEY = api_key
        self.SECRET_KEY = secret_key
        self.BASE_URL = "https://openapi.100ex.com"


    def _get_timestamp(self) -> str:
        # 返回13位的时间戳字符串
        return str(int(time.time() * 1000))


    def _generate_signature(self, timestamp: str, method: str, endpoint: str) -> str:
        # 生成签名
        message = str(timestamp) + method.upper() + endpoint
        if self.SECRET_KEY:
            signature = hmac.new(
                self.SECRET_KEY.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            return signature
        return ""


    def _get_headers(self, method: str, endpoint: str) -> Dict:
        timestamp = self._get_timestamp()
        # signature = self._generate_signature(timestamp, method, endpoint)
        headers = {
            "X-CH-TS": timestamp,
            # "X-CH_SIGN": signature,
            #"Content-Type": "application/json",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        # # 如果有API密钥，添加认证信息
        # if self.API_KEY:
        #     headers["X-CH-APIKEY"] = self.API_KEY
        #     signature = self._generate_signature(timestamp, method, endpoint)
        #     headers["X-CH-SIGN"] = signature
        # print(f"Generated Headers: {headers}")  # 调试信息
        return headers


    def _request(self, method: str, endpoint: str, **kwargs):
        headers = self._get_headers(method, endpoint)
        try:
            url = self.BASE_URL + endpoint
            if self.API_KEY:
                timestamp = self._get_timestamp()
                signature = self._generate_signature(timestamp, method, endpoint)
                kwargs['api_key'] = self.API_KEY
                kwargs['timestamp'] = timestamp
                kwargs['signature'] = signature
            if kwargs:
                params = '&'.join(f"{key}={value}" for key, value in kwargs['params'].items())
                url = f"{url}?{params}" if kwargs else url  # 如果有参数，则拼接，否则不加 '?'
            # print(f"\nMaking request to: {url}")
            # print(f"With headers: {json.dumps(headers, indent=2)}")

            response = requests.get(url, headers=headers)

            # print(f"Response Status: {response.status_code}")
            # print(f"Response Headers: {dict(response.headers)}")
            # print(f"Response Text: {response.text}")

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0 or data.get('msg')=='成功' or data.get('msg')=='suc' or data.get('msg')=='Success.':
                    return data.get('data')
                else:
                    print(f"API返回错误: {data.get('msg')}")
                    return None
            else:
                print(f"HTTP错误: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {str(e)}")
            return None
        except Exception as e:
            print(f"未知错误: {str(e)}")
            return None
