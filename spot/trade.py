import json
import logging
from byex.lib.utils import check_required_parameter
from byex.lib.utils import check_required_parameters
import pandas as pd
import requests
import hashlib
from byex.spot.__init__ import Spot
import aiohttp
import asyncio
import traceback


async def send_lark(msg, level='warning'):
    """发送消息到Lark（飞书）"""
    headers = {'Content-Type': 'application/json'}
    payload_message = {
        "msg_type": "text",
        "content": {
            "text": f"【{level}】:" + str(msg)
        }
    }
    webhook = 'https://open.larksuite.com/open-apis/bot/v2/hook/97ee256c-85b2-4729-9c0d-deba75082b64'
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(webhook, data=json.dumps(payload_message), headers=headers, timeout=10) as response:
                res = await response.json()
                statuscode = res.get('StatusCode', 404)
                if statuscode != 0 or res["StatusMessage"] != 'success':
                    logging.error(f"Lark webhook response error: {res}")
    except Exception as e:
        logging.error(f"Lark notification error for message '{msg}': {str(e)}")


def schedule_lark_message(msg, level='warning'):
    """在当前事件循环中调度Lark消息发送任务，不阻塞主程序"""
    try:
        # 获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果事件循环正在运行，创建一个任务但不等待
            task = loop.create_task(send_lark(msg, level))
            # 添加错误处理回调，避免未处理的异常
            task.add_done_callback(lambda t: None if not t.exception() else
                                 logging.error(f"Lark消息发送任务失败: {t.exception()}"))
        else:
            # 如果没有运行的事件循环，使用asyncio.run
            asyncio.run(send_lark(msg, level))
    except Exception as e:
        logging.error(f"调度Lark消息失败: {str(e)}")


class SpotTrade(Spot):
    def __init__(self, api_key: str = None, secret_key: str = None):
        super().__init__()
        self.API_KEY = api_key
        self.SECRET_KEY = secret_key
        self.BASE_URL = "https://openapi.100ex.com"
        self.session = None

    def _generate_signature(self, params: dict) -> str:
        """
        生成签名：
        1. 将请求参数（不包含 sign）按 key 升序排序，遍历时若 value 为空则跳过
        2. 拼接 key 与 value，末尾接上 SECRET_KEY
        3. 对拼接后的字符串进行 MD5 哈希处理，返回签名结果
        """
        sorted_keys = sorted(params.keys())
        print("sorted_keys ", sorted_keys)
        sign_str = ""
        for key in sorted_keys:
            value = params[key]
            if value == "" or value is None:
                continue
            sign_str += f"{key}{value}"
        sign_str += self.SECRET_KEY
        print("Signing String:", sign_str)  # 调试输出
        signature = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
        # print(f"✅ 计算出的 sign: {signature}")
        return signature

    async def async_request(self, method, url, params=None, data=None, headers=None):
        """ 使用 aiohttp 发起异步请求 """
        if not self.session:
            self.session = aiohttp.ClientSession()  # 确保只创建一个 session
        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, params=params, json=data, headers=headers) as response:
                if response.status == 200:
                    return await response.json()  # 返回 JSON 数据
                else:
                    await send_lark(f"异步请求失败: {response.status}, {await response.text()}")
                    return None

    def new_order_test(self, symbol: str, side: str, type: str, **kwargs):
        """Test New Order (TRADE)

        Test new order creation and signature/recvWindow. Creates and validates a new order but does not send it into
        the matching engine.

        POST /api/v3/order/test

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#test-new-order-trade

        Args:
            symbol (str)
            side (str)
            type (str)
        Keyword Args:
            timeInForce (str, optional)
            quantity (float, optional)
            quoteOrderQty (float, optional)
            price (float, optional)
            newClientOrderId (str, optional): A unique id among open orders. Automatically generated if not sent.
            stopPrice (float, optional): Used with STOP_LOSS, STOP_LOSS_LIMIT, TAKE_PROFIT, and TAKE_PROFIT_LIMIT orders.
            icebergQty (float, optional): Used with LIMIT, STOP_LOSS_LIMIT, and TAKE_PROFIT_LIMIT to create an iceberg order.
            newOrderRespType (str, optional): Set the response JSON. ACK, RESULT, or FULL;
                    MARKET and LIMIT order types default to FULL, all other orders default to ACK.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameters([[symbol, "symbol"], [side, "side"], [type, "type"]])
        params = {"symbol": symbol, "side": side, "type": type, **kwargs}
        url_path = "/api/v3/order/test"
        return self.sign_request("POST", url_path, params)

    def new_order(self, symbol: str, side: str, type: int, volume: str, **kwargs):
        """New Order (TRADE)

        Post a new order

        POST /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#new-order-trade

        Args:
            symbol (str)
            side (str)
            type (str)
        Keyword Args:
            timeInForce (str, optional)
            quantity (float, optional)
            quoteOrderQty (float, optional)
            price (float, optional)
            newClientOrderId (str, optional): A unique id among open orders. Automatically generated if not sent.
            strategyId (int, optional)
            strategyType (int, optional): The value cannot be less than 1000000.
            stopPrice (float, optional): Used with STOP_LOSS, STOP_LOSS_LIMIT, TAKE_PROFIT, and TAKE_PROFIT_LIMIT orders.
            icebergQty (float, optional): Used with LIMIT, STOP_LOSS_LIMIT, and TAKE_PROFIT_LIMIT to create an iceberg order.
            newOrderRespType (str, optional): Set the response JSON. ACK, RESULT, or FULL;
                    MARKET and LIMIT order types default to FULL, all other orders default to ACK.
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        endpoint = "/open/api/create_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        if type == 1:
            params = {
                "api_key": self.API_KEY,
                "price": kwargs["price"],
                "side": side,
                "symbol": symbol,
                "type": type,
                "volume": volume,
                "clientOrderId": kwargs.get("clientOrderId"),
                "time": timestamp
            }
        else:
            params = {
                "api_key": self.API_KEY,
                "side": side,
                "symbol": symbol,
                "type": type,
                "volume": volume,
                "clientOrderId": kwargs.get("clientOrderId"),
                "time": timestamp
            }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.post(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"API返回错误: {data}", 'error')
                return None
        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    async def async_new_order(self, symbol: str, side: str, type: int, volume: str, **kwargs):
        """AYSNC New Order (TRADE)

        Post a new order

        POST /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#new-order-trade

        Args:
            symbol (str)
            side (str)
            type (str)
        Keyword Args:
            timeInForce (str, optional)
            quantity (float, optional)
            quoteOrderQty (float, optional)
            price (float, optional)
            newClientOrderId (str, optional): A unique id among open orders. Automatically generated if not sent.
            strategyId (int, optional)
            strategyType (int, optional): The value cannot be less than 1000000.
            stopPrice (float, optional): Used with STOP_LOSS, STOP_LOSS_LIMIT, TAKE_PROFIT, and TAKE_PROFIT_LIMIT orders.
            icebergQty (float, optional): Used with LIMIT, STOP_LOSS_LIMIT, and TAKE_PROFIT_LIMIT to create an iceberg order.
            newOrderRespType (str, optional): Set the response JSON. ACK, RESULT, or FULL;
                    MARKET and LIMIT order types default to FULL, all other orders default to ACK.
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        endpoint = "/open/api/create_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        if type == 1:
            params = {
                "api_key": self.API_KEY,
                "price": kwargs["price"],
                "side": side,
                "symbol": symbol,
                "type": type,
                "volume": volume,
                "clientOrderId": kwargs.get("clientOrderId"),
                "time": timestamp
            }
        else:
            params = {
                "api_key": self.API_KEY,
                "side": side,
                "symbol": symbol,
                "type": type,
                "volume": volume,
                "clientOrderId": kwargs.get("clientOrderId"),
                "time": timestamp
            }

        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            data = await self.async_request(method='POST', url=url, params=params, headers=headers)

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                await send_lark(f"async new order API返回错误: {data}", level='error')
                return None
        except requests.exceptions.RequestException as e:
            await send_lark(f"async new order 请求错误: {str(e)}\n{traceback.format_exc()}", level='error')
            return None

    def cancel_order(self, symbol: str, order_id: int, **kwargs):
        """Cancel Order (TRADE)

        Cancel an active order.

        DELETE /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#cancel-order-trade

        Args:
            symbol (str)
        Keyword Args:
            orderId (int, optional)
            origClientOrderId (str, optional)
            newClientOrderId (str, optional)
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        endpoint = "/open/api/cancel_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "order_id": order_id,
            "symbol": symbol,
            "time": timestamp,
            # "clientOrderId": kwargs.get("clientOrderId"),
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.post(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"cancel order API返回错误: {data}", 'error')
                return None
        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"cancel order 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    async def async_cancel_order(self, symbol: str, order_id: int, **kwargs):
        """ASYNC Cancel Order (TRADE)

        Cancel an active order.

        DELETE /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#cancel-order-trade

        Args:
            symbol (str)
        Keyword Args:
            orderId (int, optional)
            origClientOrderId (str, optional)
            newClientOrderId (str, optional)
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        endpoint = "/open/api/cancel_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "order_id": order_id,
            "symbol": symbol,
            "time": timestamp,
            # "clientOrderId": kwargs.get("clientOrderId"),
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            data = await self.async_request(method='post', url=url, params=params, headers=headers)

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                await send_lark(f"async cancel order API返回错误: {data}", 'error')
                return None
        except requests.exceptions.RequestException as e:
            await send_lark(f"async cancel order 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    def cancel_all_orders_by_symbol(self, symbol: str) -> dict:
        """
        根据币对取消全部委托单

        HTTP 请求: POST /open/api/cancel_order_all
        请求参数:
            - api_key (str): API管理的 key
            - time (Long): 当前时间戳（毫秒）
            - sign (str): 根据签名规则生成的签名
            - symbol (str): 币对，例如 "btcusdt"
        返回:
            如果成功，返回 {"data": None} 或其他接口返回内容；否则返回 None 并打印错误信息。
        """
        endpoint = "/open/api/cancel_order_all"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "time": str(timestamp),
            "symbol": symbol
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据接口要求，Content-Type 为 application/x-www-form-urlencoded
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        try:
            response = requests.post(url, data=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"cancel all API返回错误: {data}", 'error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"cancel all 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    def cancel_open_orders(self, symbol: str, **kwargs):
        """Cancel all Open Orders on a Symbol (TRADE)

        Cancels all active orders on a symbol.
        This includes OCO orders.

        DELETE api/v3/openOrders

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#cancel-all-open-orders-on-a-symbol-trade

        Args:
            symbol (str)
        Keyword Args:
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameter(symbol, "symbol")

        url_path = "/api/v3/openOrders"
        payload = {"symbol": symbol, **kwargs}
        return self.sign_request("DELETE", url_path, payload)

    def get_order(self, symbol, order_id, **kwargs):
        """Query Order (USER_DATA)

        Check an order's status.

        GET /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-order-user_data

        Args:
            symbol (str)
        Keyword Args:
            orderId (int, optional)
            origClientOrderId (str, optional)
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        endpoint = "/open/api/order_info"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "order_id": order_id,
            "symbol": symbol,
            "time": timestamp
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign
        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"get order API返回错误: {data}", 'error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"get order 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    async def async_get_order(self, symbol, order_id, **kwargs):
        """ASYNC Query Order (USER_DATA)

        Check an order's status.

        GET /api/v3/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-order-user_data

        Args:
            symbol (str)
        Keyword Args:
            orderId (int, optional)
            origClientOrderId (str, optional)
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        endpoint = "/open/api/order_info"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "order_id": order_id,
            "symbol": symbol,
            "time": timestamp
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign
        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            data = await self.async_request(method="get", url=url, params=params, headers=headers)

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                await send_lark(f"async get order API返回错误: {data}", 'error')
                return None

        except requests.exceptions.RequestException as e:
            await send_lark(f"async get order 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    def cancel_and_replace(self, symbol, **kwargs):

        endpoint = "/open/api/mass_replaceV2"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        old_params = {
            "api_key": self.API_KEY,
            "symbol": symbol,
            "time": timestamp
        }
        # "mass_cancel" : mass_cancel,
        #     "mass_replace" : mass_replace,

        if ("mass_cancel") in kwargs:
            old_params["mass_cancel"] = str(kwargs["mass_cancel"]).replace(' ', '')
        if ("mass_place") in kwargs:
            old_params["mass_place"] = str(kwargs["mass_place"]).replace(' ', '')
        sorted_keys = sorted(old_params.keys())
        print(sorted_keys)
        params = dict()
        for key in sorted_keys:
            params[key] = old_params[key]
        # 计算签名并加入参数
        print("to signed params ", params)
        sign = self._generate_signature(params)
        print("sign", sign)
        params["sign"] = sign
        print(params)

        # 使用 Content-Type: application/x-www-form-urlencoded 并将参数放在请求体中
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        print("Request URL:", url)

        try:
            # 将参数放在请求体中，而不是 URL 参数中
            response = requests.post(url, data=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"cancel and replace API返回错误: {data}", 'error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"cancel and replace 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    def get_open_orders(self, symbol=None, **kwargs):
        """Current Open Orders (USER_DATA)

        Get all open orders on a symbol.

        GET /api/v3/openOrders

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#current-open-orders-user_data

        Args:
            symbol (str, optional)
        Keyword Args:
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        endpoint = "/open/api/v2/new_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        old_params = {
            "api_key": self.API_KEY,
            "symbol": symbol,
            "time": timestamp
        }
        if ("pageSize") in kwargs:
            old_params["pageSize"] = kwargs["pageSize"]
        if ("page") in kwargs:
            old_params["page"] = kwargs["page"]
        sorted_keys = sorted(old_params.keys())
        # print(sorted_keys)
        params = dict()
        for key in sorted_keys:
            params[key] = old_params[key]
        sign = self._generate_signature(params)
        params["sign"] = sign
        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            # "Content-Type": "application/json"
            "Content-Type": "application/x-www-form-urlencoded"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"get opn orders API返回错误: {data}", level='error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"get open orders 请求错误: {str(e)}\n{traceback.format_exc()}", level='error')
            return None

    def get_orders(self, symbol: str, **kwargs):
        check_required_parameter(symbol, "symbol")
        endpoint = "/open/api/v2/all_order"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        old_params = {
            "api_key": self.API_KEY,
            "symbol": symbol,
            "time": timestamp
        }
        if ("pageSize") in kwargs:
            old_params["pageSize"] = kwargs["pageSize"]
        if ("page") in kwargs:
            old_params["page"] = kwargs["page"]
        if ("startDate") in kwargs:
            old_params["startDate"] = kwargs["startDate"]
        if ("endDate") in kwargs:
            old_params["endDate"] = kwargs["endDate"]

        sorted_keys = sorted(old_params.keys())
        params = dict()

        for key in sorted_keys:
            params[key] = old_params[key]
        sign = self._generate_signature(params)
        params["sign"] = sign
        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            # "Content-Type": "application/json"
            "Content-Type": "application/x-www-form-urlencoded"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            # print(data)
            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"get orders API返回错误: {data}", 'error')
                return None
        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"get orders 请求错误: {str(e)}\n{traceback.format_exc()}", 'error')
            return None

    def new_oco_order(
            self,
            symbol: str,
            side: str,
            quantity: float,
            aboveType: str,
            belowType: str,
            **kwargs
    ):
        """New Order List - OCO (TRADE)

        Send in an one-cancels-the-other (OCO) pair, where activation of one order immediately cancels the other.

        - An OCO has 2 legs called the above leg and below leg.
        - One of the legs must be a LIMIT_MAKER order and the other leg must be STOP_LOSS or STOP_LOSS_LIMIT order.
        - Price restrictions:
            - If the OCO is on the SELL side: LIMIT_MAKER price > Last Traded Price > stopPrice
            - If the OCO is on the BUY side: LIMIT_MAKER price < Last Traded Price < stopPrice
        - OCO counts as 2 orders against the order rate limit.

        Response format for orderReports is selected using the newOrderRespType parameter. The response example is for the RESULT response type. See POST /api/v3/order for more examples.

        POST /api/v3/orderList/oco

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#new-order-list---oco-trade

        Args:
            symbol (str)
            side (str)
            quantity (float)
            aboveType (str)
            belowType (str)
        Keyword Args:
            listClientOrderId (str, optional): Arbitrary unique ID among open order lists. Automatically generated if not sent. A new order list with the same listClientOrderId is accepted only when the previous one is filled or completely expired. listClientOrderId is distinct from the aboveClientOrderId and the belowCLientOrderId
            aboveClientOrderId (str, optional): Supported values : STOP_LOSS_LIMIT, STOP_LOSS, LIMIT_MAKER
            aboveIcebergQty (int, optional): Note that this can only be used if aboveTimeInForce is GTC.
            abovePrice (float, optional)
            aboveStopPrice (float, optional): Can be used if aboveType is STOP_LOSS or STOP_LOSS_LIMIT. Either aboveStopPrice or aboveTrailingDelta or both, must be specified.
            aboveTrailingDelta (int, optional)
            aboveTimeInForce (float, optional): Required if the aboveType is STOP_LOSS_LIMIT.
            aboveStrategyId (int, optional): Arbitrary numeric value identifying the above leg order within an order strategy.
            aboveStrategyType (int, optional): Arbitrary numeric value identifying the above leg order strategy. Values smaller than 1000000 are reserved and cannot be used.
            belowClientOrderId (str, optional): Arbitrary unique ID among open orders for the below leg order. Automatically generated if not sent
            belowIcebergQty (int, optional): Note that this can only be used if belowTimeInForce is GTC.
            belowPrice (float, optional)
            belowStopPrice (float, optional): Can be used if belowType is STOP_LOSS or STOP_LOSS_LIMIT. Either belowStopPrice or belowTrailingDelta or both, must be specified.
            belowTrailingDelta (int, optional)
            belowTimeInForce (str, optional): Required if the belowType is STOP_LOSS_LIMIT.
            belowStrategyId (int, optional): Arbitrary numeric value identifying the below leg order within an order strategy.
            belowStrategyType (int, optional): Arbitrary numeric value identifying the below leg order strategy. Values smaller than 1000000 are reserved and cannot be used.
            newOrderRespType (str, optional): Select response format: ACK, RESULT, FULL
            selfTradePreventionMode (str, optional): The allowed enums is dependent on what is configured on the symbol. The possible supported values are EXPIRE_TAKER, EXPIRE_MAKER, EXPIRE_BOTH, NONE.
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        check_required_parameters(
            [
                [symbol, "symbol"],
                [side, "side"],
                [quantity, "quantity"],
                [aboveType, "aboveType"],
                [belowType, "belowType"],
            ]
        )
        params = {
            "symbol": symbol,
            "side": side,
            "quantity": quantity,
            "aboveType": aboveType,
            "belowType": belowType,
            **kwargs,
        }

        url_path = "/api/v3/orderList/oco"
        return self.sign_request("POST", url_path, params)

    def new_oto_order(
            self,
            symbol: str,
            workingType: str,
            workingSide: str,
            workingPrice: float,
            workingQuantity: float,
            pendingType: str,
            pendingSide: str,
            pendingQuantity: float,
            **kwargs
    ):
        """New Order List - OTO (TRADE)


        - An OTO (One-Triggers-the-Other) is an order list comprised of 2 orders.
        - The first order is called the working order and must be LIMIT or LIMIT_MAKER. Initially, only the working order goes on the order book.
        - The second order is called the pending order. It can be any order type except for MARKET orders using parameter quoteOrderQty. The pending order is only placed on the order book when the working order gets fully filled.
        - If either the working order or the pending order is cancelled individually, the other order in the order list will also be canceled or expired.
        - When the order list is placed, if the working order gets immediately fully filled, the placement response will show the working order as FILLED but the pending order will still appear as PENDING_NEW. You need to query the status of the pending order again to see its updated status.
        - OTOs count as 2 orders against the order rate limit, EXCHANGE_MAX_NUM_ORDERS filter and MAX_NUM_ORDERS filter.

        POST /api/v3/orderList/oto

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#new-order-list---oto-trade

        Args:
            symbol (str)
            workingType (str)
            workingSide (str)
            workingPrice (float)
            workingQuantity (float)
            pendingType (str)
            pendingSide (str)
            pendingQuantity (float)
        Keyword Args:
            listClientOrderId (str, optional): Arbitrary unique ID among open order lists. Automatically generated if not sent. A new order list with the same listClientOrderId is accepted only when the previous one is filled or completely expired. listClientOrderId is distinct from the workingClientOrderId and the pendingClientOrderId
            newOrderRespType (str, optional): Format of the JSON response. Supported values: ACK, FULL, RESULT
            selfTradePreventionMode (str, optional): The allowed values are dependent on what is configured on the symbol.
            workingClientOrderId (str, optional): Arbitrary unique ID among open orders for the working order. Automatically generated if not sent.
            workingIcebergQty (float, optional): This can only be used if workingTimeInForce is GTC or if workingType is LIMIT_MAKER.
            workingTimeInForce (str, optional): Supported values: FOK, IOC, GTC
            workingStrategyId (int, optional): Arbitrary numeric value identifying the working order within an order strategy.
            workingStrategyType (int, optional): Arbitrary numeric value identifying the working order strategy. Values smaller than 1000000 are reserved and cannot be used.
            pendingClientOrderId (str, optional): Arbitrary unique ID among open orders for the pending order. Automatically generated if not sent.
            pendingPrice (float, optional)
            pendingStopPrice (float, optional)
            pendingTrailingDelta (float, optional)
            pendingIcebergQty (float, optional): This can only be used if pendingTimeInForce is GTC or if pendingType is LIMIT_MAKER.
            pendingTimeInForce (str, optional): Supported values: GTC, FOK, IOC
            pendingStrategyId (int, optional): Arbitrary numeric value identifying the pending order within an order strategy.
            pendingStrategyType (int, optional): Arbitrary numeric value identifying the pending order strategy. Values smaller than 1000000 are reserved and cannot be used.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameters(
            [
                [symbol, "symbol"],
                [workingType, "workingType"],
                [workingSide, "workingSide"],
                [workingPrice, "workingPrice"],
                [workingQuantity, "workingQuantity"],
                [pendingType, "pendingType"],
                [pendingSide, "pendingSide"],
                [pendingQuantity, "pendingQuantity"],
            ]
        )
        params = {
            "symbol": symbol,
            "workingType": workingType,
            "workingSide": workingSide,
            "workingPrice": workingPrice,
            "workingQuantity": workingQuantity,
            "pendingType": pendingType,
            "pendingSide": pendingSide,
            "pendingQuantity": pendingQuantity,
            **kwargs,
        }

        url_path = "/api/v3/orderList/oto"
        return self.sign_request("POST", url_path, params)

    def new_otoco_order(
            self,
            symbol: str,
            workingType: str,
            workingSide: str,
            workingPrice: float,
            workingQuantity: float,
            pendingSide: str,
            pendingQuantity: float,
            pendingAboveType: str,
            **kwargs
    ):
        """New Order List - OTOCO (TRADE)

        Place an OTOCO.

        - An OTOCO (One-Triggers-One-Cancels-the-Other) is an order list comprised of 3 orders.
        - The first order is called the working order and must be LIMIT or LIMIT_MAKER. Initially, only the working order goes on the order book.
            - The behavior of the working order is the same as the OTO.
        - OTOCO has 2 pending orders (pending above and pending below), forming an OCO pair. The pending orders are only placed on the order book when the working order gets fully filled.
            - The rules of the pending above and pending below follow the same rules as the Order List OCO.
        - OTOCOs count as 3 orders against the order rate limit, EXCHANGE_MAX_NUM_ORDERS filter, and MAX_NUM_ORDERS filter.

        POST /api/v3/orderList/otoco

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#new-order-list---otoco-trade

        Args:
            symbol (str)
            workingType (str)
            workingSide (str)
            workingPrice (float)
            workingQuantity (float)
            pendingSide (str)
            pendingQuantity (float)
            pendingAboveType (str)
        Keyword Args:
            listClientOrderId (str, optional): Arbitrary unique ID among open order lists. Automatically generated if not sent. A new order list with the same listClientOrderId is accepted only when the previous one is filled or completely expired. listClientOrderId is distinct from the workingClientOrderId, pendingAboveClientOrderId, and the pendingBelowClientOrderId.
            newOrderRespType (str, optional): Format the JSON response. Supported values: ACK, FULL, RESPONSE
            selfTradePreventionMode (str, optional): The allowed values are dependent on what is configured on the symbol.
            workingClientOrderId (str, optional): Arbitrary unique ID among open orders for the working order. Automatically generated if not sent.
            workingIcebergQty (float, optional): This can only be used if workingTimeInForce is GTC or if workingType is LIMIT_MAKER.
            workingTimeInForce (str, optional): Supported values: GTC, IOC, FOK
            workingStrategyId (int, optional): Arbitrary numeric value identifying the working order within an order strategy.
            workingStrategyType (int, optional): Arbitrary numeric value identifying the working order strategy. Values smaller than 1000000 are reserved and cannot be used.
            pendingAboveClientOrderId (str, optional): Arbitrary unique ID among open orders for the pending above order. Automatically generated if not sent.
            pendingAbovePrice (float, optional)
            pendingAboveStopPrice (float, optional)
            pendingAboveTrailingDelta (float, optional)
            pendingAboveIcebergQty (float, optional): This can only be used if pendingAboveTimeInForce is GTC or if pendingAboveType is LIMIT_MAKER.
            pendingAboveTimeInForce (str, optional)
            pendingAboveStrategyId (int, optional): Arbitrary numeric value identifying the pending above order within an order strategy.
            pendingAboveStrategyType (int, optional): Arbitrary numeric value identifying the pending above order strategy. Values smaller than 1000000 are reserved and cannot be used.
            pendingBelowType (str, optional): Supported values: LIMIT_MAKER, STOP_LOSS, and STOP_LOSS_LIMIT
            pendingBelowClientOrderId (str, optional): Arbitrary unique ID among open orders for the pending below order. Automatically generated if not sent.
            pendingBelowPrice (float, optional)
            pendingBelowStopPrice (float, optional)
            pendingBelowTrailingDelta (float, optional)
            pendingBelowIcebergQty (float, optional): This can only be used if pendingBelowTimeInForce is GTC or if pendingBelowType is LIMIT_MAKER.
            pendingBelowTimeInForce (str, optional)
            pendingBelowStrategyId (int, optional): Arbitrary numeric value identifying the pending below order within an order strategy.
            pendingBelowStrategyType (int, optional): Arbitrary numeric value identifying the pending below order strategy. Values smaller than 1000000 are reserved and cannot be used.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameters(
            [
                [symbol, "symbol"],
                [workingType, "workingType"],
                [workingSide, "workingSide"],
                [workingPrice, "workingPrice"],
                [workingQuantity, "workingQuantity"],
                [pendingSide, "pendingSide"],
                [pendingQuantity, "pendingQuantity"],
                [pendingAboveType, "pendingAboveType"],
            ]
        )
        params = {
            "symbol": symbol,
            "workingType": workingType,
            "workingSide": workingSide,
            "workingPrice": workingPrice,
            "workingQuantity": workingQuantity,
            "pendingSide": pendingSide,
            "pendingQuantity": pendingQuantity,
            "pendingAboveType": pendingAboveType,
            **kwargs,
        }

        url_path = "/api/v3/orderList/otoco"
        return self.sign_request("POST", url_path, params)

    def cancel_oco_order(self, symbol, **kwargs):
        """Cancel OCO (TRADE)

        Cancel an entire Order List

        DELETE /api/v3/orderList

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#cancel-order-list-trade

        Args:
            symbol (str)
        Keyword Args:
            orderListId (int, optional): Either orderListId or listClientOrderId must be provided
            listClientOrderId (str, optional): Either orderListId or listClientOrderId must be provided
            newClientOrderId (str, optional): Used to uniquely identify this cancel. Automatically generated by default.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameter(symbol, "symbol")

        url_path = "/api/v3/orderList"
        payload = {"symbol": symbol, **kwargs}
        return self.sign_request("DELETE", url_path, payload)

    def get_oco_order(self, **kwargs):
        """Query OCO (USER_DATA)

        Retrieves a specific OCO based on provided optional parameters

        GET /api/v3/orderList

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-order-list-user_data

        Keyword Args:
            orderListId (int, optional): Either orderListId or listClientOrderId must be provided
            origClientOrderId (str, optional): Either orderListId or listClientOrderId must be provided.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        url_path = "/api/v3/orderList"
        return self.sign_request("GET", url_path, {**kwargs})

    def get_oco_orders(self, **kwargs):
        """Query all OCO (USER_DATA)

        Retrieves all OCO based on provided optional parameters

        GET /api/v3/allOrderList

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-all-order-lists-user_data

        Keyword Args:
            fromId (int, optional): If supplied, neither startTime or endTime can be provided
            startTime (int, optional)
            endTime (int, optional)
            limit (int, optional): Default Value: 500; Max Value: 1000
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        url_path = "/api/v3/allOrderList"
        return self.sign_request("GET", url_path, {**kwargs})

    def get_oco_open_orders(self, **kwargs):
        """Query Open OCO (USER_DATA)

        GET /api/v3/openOrderList

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-open-order-lists-user_data

        Keyword Args:
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        url_path = "/api/v3/openOrderList"
        return self.sign_request("GET", url_path, {**kwargs})

    def get_balance(self):
        """
        获取资产余额
        """

    def account(self, **kwargs):
        endpoint = "/open/api/user/account"
        url = f"{self.BASE_URL}{endpoint}"
        timestamp = self._get_timestamp()
        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "time": timestamp
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/json"
        }
        print("Request URL:", url)
        print("Request Params:", params)
        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"account API返回错误: {data}", level='error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"account 请求错误:{str(e)}\n{traceback.format_exc()}", level='error')
            return None

    def my_trades(self, symbol: str, **kwargs):
        endpoint = "/open/api/all_trade"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()
        old_params = {
            "api_key": self.API_KEY,
            "symbol": symbol,
            "time": timestamp
        }
        if ("page") in kwargs:
            old_params["page"] = kwargs["page"]
        if ("pageSize") in kwargs:
            old_params["pageSize"] = kwargs["pageSize"]
        if ("startDate") in kwargs:
            old_params["startDate"] = kwargs["startDate"]
        if ("endDate") in kwargs:
            old_params["endDate"] = kwargs["endDate"]
        if ("sort") in kwargs:
            old_params["sort"] = kwargs["sort"]

        sorted_keys = sorted(old_params.keys())
        params = dict()
        for key in sorted_keys:
            params[key] = old_params[key]

            # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign
        # 计算签名并加入参数
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        print("Request URL:", url)
        print("Request Params:", params)

        try:
            response = requests.get(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"my trades API返回错误: {data}", level='error')
                return None

        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"my trades 请求错误: {str(e)}\n{traceback.format_exc()}", level='error')
            return None

    def get_order_rate_limit(self, **kwargs):
        """Query Current Order Count Usage (TRADE)

        Displays the user's current order count usage for all intervals.

        GET /api/v3/rateLimit/order

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-unfilled-order-count-user_data

        Keyword Args:
            recvWindow (int, optional): The value cannot be greater than 60000
        """

        url_path = "/api/v3/rateLimit/order"
        return self.sign_request("GET", url_path, {**kwargs})

    def query_prevented_matches(self, symbol: str, **kwargs):
        """Query Prevented Matches (USER_DATA)

        Displays the list of orders that were expired because of STP.

        For additional information on what a Prevented match is, as well as Self Trade Prevention (STP), please refer to our STP FAQ page.

        These are the combinations supported:

        * symbol + preventedMatchId
        * symbol + orderId
        * symbol + orderId + fromPreventedMatchId (limit will default to 500)
        * symbol + orderId + fromPreventedMatchId + limit

        Weight(IP):

        Case 	                          Weight
        If symbol is invalid: 	        2
        Querying by preventedMatchId: 	2
        Querying by orderId: 	          20

        GET /api/v3/myPreventedMatches

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-prevented-matches-user_data

        Args:
            symbol (str)
        Keyword Args:
            preventedMatchId (int, optional)
            orderId (int, optional): Order id
            fromPreventedMatchId (int, optional)
            limit (int, optional): Default 500; max 1000.
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameter(symbol, "symbol")

        params = {"symbol": symbol, **kwargs}
        url_path = "/api/v3/myPreventedMatches"
        return self.sign_request("GET", url_path, params)

    def query_allocations(self, symbol: str, **kwargs):
        """Query Cross-Collateral Information (USER_DATA)

        GET /api/v3/myAllocations

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-allocations-user_data

        Args:
            symbol (str)
        Keyword Args:
            startTime (int, optional)
            endTime (int, optional)
            fromAllocationId (int, optional)
            limit (int, optional): Default Value: 500; Max Value: 1000
            orderId (int, optional)
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameter(symbol, "symbol")

        params = {"symbol": symbol, **kwargs}
        url_path = "/api/v3/myAllocations"
        return self.sign_request("GET", url_path, params)

    def query_commission_rates(self, symbol: str, **kwargs):
        """Query Commission Rates (USER_DATA)

        GET /api/v3/account/commission

        https://developers.binance.com/docs/binance-spot-api-docs/rest-api/public-api-endpoints#query-commission-rates-user_data

        Args:
            symbol (str)
        Keyword Args:
            recvWindow (int, optional): The value cannot be greater than 60000
        """
        check_required_parameter(symbol, "symbol")

        params = {"symbol": symbol, **kwargs}
        url_path = "/api/v3/account/commission"
        return self.sign_request("GET", url_path, params)

    async def async_self_trade(self, symbol: str, volume: int, side: str, type: int, **kwargs):
        """Self Trade

        POST /open/api/self_trade
        """
        endpoint = "/open/api/self_trade"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "price": kwargs["price"],
            "side": side,
            "symbol": symbol,
            "type": type,
            "volume": volume,
            "clientOrderId": kwargs.get("clientOrderId"),
            "time": timestamp
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            data = await self.async_request(method='POST', url=url, params=params, headers=headers)

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                await send_lark(f"async_self_trade API返回错误: {data}", level='error')
                return None
        except requests.exceptions.RequestException as e:
            await send_lark(f"async_self_trade 请求错误: {str(e)}\n{traceback.format_exc()}", level='error')
            return None

    def self_trade(self, symbol: str, volume: int, side: str, type: int, **kwargs):
        """Self Trade

        POST /open/api/self_trade
        """
        endpoint = "/open/api/self_trade"
        url = f"{self.BASE_URL}{endpoint}"

        # 获取时间戳
        timestamp = self._get_timestamp()

        # 构造参与签名的参数
        params = {
            "api_key": self.API_KEY,
            "price": kwargs["price"],
            "side": side,
            "symbol": symbol,
            "type": type,
            "volume": volume,
            "clientOrderId": kwargs.get("clientOrderId"),
            "time": timestamp
        }
        # 计算签名并加入参数
        sign = self._generate_signature(params)
        params["sign"] = sign

        # 根据文档 GET 请求需要将参数放在 query parameter 内
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        # print("Request URL:", url)
        # print("Request Params:", params)

        try:
            response = requests.post(url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "0":
                return data.get("data", {})
            else:
                schedule_lark_message(f"self_trade API返回错误: {data}", level='error')
                return None
        except requests.exceptions.RequestException as e:
            schedule_lark_message(f"self_trade 请求错误: {str(e)}\n{traceback.format_exc()}", level='error')
            return None

