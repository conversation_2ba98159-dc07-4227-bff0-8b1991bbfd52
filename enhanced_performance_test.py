#!/usr/bin/env python3
"""
增强版性能监控测试脚本
测试详细的API性能监控功能
"""

import time
import random
from collections import defaultdict

# 模拟详细API性能统计
api_performance_stats = {
    'get_pending_orders': {'times': [], 'calls': 0, 'total_time': 0},
    'cancel_and_replace': {'times': [], 'calls': 0, 'total_time': 0},
    'update_active_orders': {'times': [], 'calls': 0, 'total_time': 0},
    'get_ticker': {'times': [], 'calls': 0, 'total_time': 0},
    'get_orderbook': {'times': [], 'calls': 0, 'total_time': 0},
    'get_trades': {'times': [], 'calls': 0, 'total_time': 0},
    'get_assets': {'times': [], 'calls': 0, 'total_time': 0},
    'cancel_all_orders': {'times': [], 'calls': 0, 'total_time': 0},
    'last_report_time': time.time(),
    'report_interval': 10  # 测试时使用10秒间隔
}

def track_api_performance(api_name):
    """API性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                # 记录API性能数据
                if api_name in api_performance_stats:
                    api_performance_stats[api_name]['times'].append(duration)
                    api_performance_stats[api_name]['calls'] += 1
                    api_performance_stats[api_name]['total_time'] += duration
                    
                    # 如果耗时较长，立即记录警告
                    if duration > 0.5:  # 超过500ms
                        print(f"⚠️  {api_name} API调用耗时较长: {duration*1000:.1f}ms")
        return wrapper
    return decorator

def report_api_performance_stats():
    """生成详细的API性能统计报告"""
    global api_performance_stats
    
    current_time = time.time()
    if current_time - api_performance_stats['last_report_time'] < api_performance_stats['report_interval']:
        return
    
    # 检查是否有数据
    has_data = False
    for api_name, stats in api_performance_stats.items():
        if isinstance(stats, dict) and stats.get('calls', 0) > 0:
            has_data = True
            break
    
    if not has_data:
        return
    
    print("=" * 80)
    print("API性能详细统计报告 (测试模式)")
    print("=" * 80)
    
    # 按总耗时排序API
    api_list = []
    for api_name, stats in api_performance_stats.items():
        if isinstance(stats, dict) and stats.get('calls', 0) > 0:
            times = stats['times']
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            total_time = stats['total_time']
            calls = stats['calls']
            
            api_list.append({
                'name': api_name,
                'avg_time': avg_time,
                'max_time': max_time,
                'min_time': min_time,
                'total_time': total_time,
                'calls': calls
            })
    
    # 按总耗时排序
    api_list.sort(key=lambda x: x['total_time'], reverse=True)
    
    # 计算总API时间
    total_api_time = sum(api['total_time'] for api in api_list)
    
    print(f"{'API名称':<20} {'调用次数':<8} {'总耗时(ms)':<12} {'平均耗时(ms)':<12} {'最大耗时(ms)':<12} {'占比':<8}")
    print("-" * 80)
    
    for api in api_list:
        percentage = (api['total_time'] / total_api_time) * 100 if total_api_time > 0 else 0
        print(f"{api['name']:<20} {api['calls']:<8} "
              f"{api['total_time']*1000:<12.1f} {api['avg_time']*1000:<12.1f} "
              f"{api['max_time']*1000:<12.1f} {percentage:<8.1f}%")
    
    print("-" * 80)
    print(f"总API调用时间: {total_api_time*1000:.1f}ms")
    
    # 性能建议
    if api_list:
        slowest_api = api_list[0]
        if slowest_api['total_time'] > total_api_time * 0.5:
            print(f"⚠️  {slowest_api['name']} 占用了 {(slowest_api['total_time']/total_api_time)*100:.1f}% 的API时间，建议优化")
        
        # 检查平均耗时最高的API
        avg_slowest = max(api_list, key=lambda x: x['avg_time'])
        if avg_slowest['avg_time'] > 0.2:  # 平均超过200ms
            print(f"⚠️  {avg_slowest['name']} 平均耗时最高 ({avg_slowest['avg_time']*1000:.1f}ms)，可能存在性能问题")
    
    print("=" * 80)
    
    # 清空API统计数据
    for api_name, stats in api_performance_stats.items():
        if isinstance(stats, dict):
            stats['times'].clear()
            stats['calls'] = 0
            stats['total_time'] = 0
    
    api_performance_stats['last_report_time'] = current_time

# 模拟各种API调用
@track_api_performance('get_pending_orders')
def simulate_get_pending_orders():
    # 模拟获取挂单API，通常较慢
    time.sleep(random.uniform(0.1, 0.3))
    return "pending_orders_data"

@track_api_performance('cancel_and_replace')
def simulate_cancel_and_replace():
    # 模拟撤单挂单API，通常最慢
    time.sleep(random.uniform(0.2, 0.6))
    return "cancel_replace_result"

@track_api_performance('get_ticker')
def simulate_get_ticker():
    # 模拟获取价格API，通常较快
    time.sleep(random.uniform(0.05, 0.15))
    return "ticker_data"

@track_api_performance('get_orderbook')
def simulate_get_orderbook():
    # 模拟获取订单簿API，中等速度
    time.sleep(random.uniform(0.08, 0.2))
    return "orderbook_data"

@track_api_performance('get_assets')
def simulate_get_assets():
    # 模拟获取资产API，中等速度
    time.sleep(random.uniform(0.06, 0.18))
    return "assets_data"

@track_api_performance('get_trades')
def simulate_get_trades():
    # 模拟获取成交记录API，较快
    time.sleep(random.uniform(0.04, 0.12))
    return "trades_data"

@track_api_performance('cancel_all_orders')
def simulate_cancel_all_orders():
    # 模拟取消所有订单API，较慢
    time.sleep(random.uniform(0.15, 0.4))
    return "cancel_all_result"

def simulate_trading_cycle():
    """模拟一个完整的交易周期"""
    print("开始交易周期...")
    
    # 1. 获取当前价格
    simulate_get_ticker()
    
    # 2. 获取当前挂单
    simulate_get_pending_orders()
    
    # 3. 获取订单簿
    simulate_get_orderbook()
    
    # 4. 获取资产信息
    simulate_get_assets()
    
    # 5. 执行撤单挂单操作（多次）
    num_replace_calls = random.randint(2, 5)
    for i in range(num_replace_calls):
        simulate_cancel_and_replace()
    
    # 6. 偶尔获取成交记录
    if random.random() < 0.3:
        simulate_get_trades()
    
    # 7. 偶尔取消所有订单
    if random.random() < 0.1:
        simulate_cancel_all_orders()
    
    print(f"交易周期完成，执行了 {num_replace_calls} 次撤单挂单操作")

def main():
    """主测试函数"""
    print("开始增强版API性能监控测试...")
    print("模拟真实的交易API调用场景")
    print("测试将运行30秒，每10秒生成一次详细的API性能报告")
    print("-" * 60)
    
    start_time = time.time()
    test_duration = 30  # 测试30秒
    cycle_count = 0
    
    while time.time() - start_time < test_duration:
        cycle_count += 1
        print(f"\n--- 交易周期 {cycle_count} ---")
        simulate_trading_cycle()
        
        # 检查是否需要生成报告
        report_api_performance_stats()
        
        # 模拟交易间隔
        time.sleep(random.uniform(1.0, 3.0))
    
    # 最终报告
    if any(stats.get('calls', 0) > 0 for stats in api_performance_stats.values() if isinstance(stats, dict)):
        api_performance_stats['last_report_time'] = 0  # 强制生成最终报告
        report_api_performance_stats()
    
    print(f"\n测试完成！共执行了 {cycle_count} 个交易周期")
    print("\n在实际使用中，这些API性能统计将帮助您：")
    print("1. 识别最慢的API调用")
    print("2. 发现性能瓶颈")
    print("3. 优化API调用策略")
    print("4. 监控API服务质量")

if __name__ == "__main__":
    main()
